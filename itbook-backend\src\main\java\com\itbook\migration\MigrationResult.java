package com.itbook.migration;

import com.itbook.dto.SkillMappingResult;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据迁移结果类
 * 记录迁移过程的详细信息和结果
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
public class MigrationResult {

    /**
     * 迁移开始时间
     */
    private LocalDateTime startTime;

    /**
     * 迁移结束时间
     */
    private LocalDateTime endTime;

    /**
     * 迁移状态
     */
    private MigrationStatus status;

    /**
     * 成功消息
     */
    private String message;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 是否完成备份
     */
    private boolean backupCompleted;

    /**
     * 清洗的记录数
     */
    private int cleanedRecords;

    /**
     * 技能映射结果
     */
    private SkillMappingResult mappingResult;

    /**
     * 数据验证结果
     */
    private ValidationResult validationResult;

    /**
     * 是否完成回滚
     */
    private boolean rollbackCompleted;

    /**
     * 迁移执行的详细步骤
     */
    private java.util.List<MigrationStep> steps;

    // 构造函数
    public MigrationResult() {
        this.status = MigrationStatus.NOT_STARTED;
        this.backupCompleted = false;
        this.rollbackCompleted = false;
        this.cleanedRecords = 0;
        this.steps = new java.util.ArrayList<>();
    }

    // Getter和Setter方法
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public MigrationStatus getStatus() {
        return status;
    }

    public void setStatus(MigrationStatus status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isBackupCompleted() {
        return backupCompleted;
    }

    public void setBackupCompleted(boolean backupCompleted) {
        this.backupCompleted = backupCompleted;
    }

    public int getCleanedRecords() {
        return cleanedRecords;
    }

    public void setCleanedRecords(int cleanedRecords) {
        this.cleanedRecords = cleanedRecords;
    }

    public SkillMappingResult getMappingResult() {
        return mappingResult;
    }

    public void setMappingResult(SkillMappingResult mappingResult) {
        this.mappingResult = mappingResult;
    }

    public ValidationResult getValidationResult() {
        return validationResult;
    }

    public void setValidationResult(ValidationResult validationResult) {
        this.validationResult = validationResult;
    }

    public boolean isRollbackCompleted() {
        return rollbackCompleted;
    }

    public void setRollbackCompleted(boolean rollbackCompleted) {
        this.rollbackCompleted = rollbackCompleted;
    }

    public java.util.List<MigrationStep> getSteps() {
        return steps;
    }

    public void setSteps(java.util.List<MigrationStep> steps) {
        this.steps = steps;
    }

    /**
     * 添加迁移步骤
     */
    public void addStep(String stepName, String description, boolean success) {
        MigrationStep step = new MigrationStep();
        step.setStepName(stepName);
        step.setDescription(description);
        step.setSuccess(success);
        step.setTimestamp(LocalDateTime.now());
        this.steps.add(step);
    }

    /**
     * 计算迁移耗时（毫秒）
     */
    public long getExecutionTimeMs() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, endTime).toMillis();
    }

    /**
     * 获取格式化的执行时间
     */
    public String getFormattedExecutionTime() {
        long ms = getExecutionTimeMs();
        if (ms < 1000) {
            return ms + "ms";
        } else if (ms < 60000) {
            return String.format("%.2fs", ms / 1000.0);
        } else {
            long minutes = ms / 60000;
            long seconds = (ms % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 获取迁移成功率
     */
    public double getSuccessRate() {
        if (steps.isEmpty()) {
            return 0.0;
        }
        long successCount = steps.stream().mapToLong(step -> step.isSuccess() ? 1 : 0).sum();
        return (double) successCount / steps.size() * 100;
    }

    /**
     * 判断迁移是否成功
     */
    public boolean isSuccess() {
        return status == MigrationStatus.SUCCESS;
    }

    /**
     * 判断迁移是否失败
     */
    public boolean isFailed() {
        return status == MigrationStatus.FAILED;
    }

    /**
     * 获取迁移摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("迁移状态: ").append(status.getDescription()).append("\n");
        
        if (startTime != null) {
            summary.append("开始时间: ").append(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        }
        
        if (endTime != null) {
            summary.append("结束时间: ").append(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            summary.append("执行时长: ").append(getFormattedExecutionTime()).append("\n");
        }
        
        summary.append("备份完成: ").append(backupCompleted ? "是" : "否").append("\n");
        summary.append("清洗记录数: ").append(cleanedRecords).append("\n");
        
        if (mappingResult != null) {
            summary.append("生成原子技能: ").append(mappingResult.getTotalAtomicSkillCount()).append("个\n");
            summary.append("创建映射关系: ").append(mappingResult.getMappingCount()).append("个\n");
            summary.append("平均置信度: ").append(String.format("%.2f", mappingResult.getAverageConfidence())).append("\n");
        }
        
        if (validationResult != null) {
            summary.append("数据验证: ").append(validationResult.isValid() ? "通过" : "失败").append("\n");
        }
        
        summary.append("成功率: ").append(String.format("%.1f%%", getSuccessRate())).append("\n");
        
        if (errorMessage != null) {
            summary.append("错误信息: ").append(errorMessage).append("\n");
        }
        
        if (rollbackCompleted) {
            summary.append("回滚状态: 已完成\n");
        }
        
        return summary.toString();
    }

    @Override
    public String toString() {
        return "MigrationResult{" +
                "status=" + status +
                ", executionTime='" + getFormattedExecutionTime() + '\'' +
                ", backupCompleted=" + backupCompleted +
                ", cleanedRecords=" + cleanedRecords +
                ", mappingResult=" + (mappingResult != null ? mappingResult.toString() : "null") +
                ", validationResult=" + (validationResult != null ? validationResult.toString() : "null") +
                ", successRate=" + String.format("%.1f%%", getSuccessRate()) +
                ", rollbackCompleted=" + rollbackCompleted +
                '}';
    }

    /**
     * 迁移步骤内部类
     */
    public static class MigrationStep {
        private String stepName;
        private String description;
        private boolean success;
        private LocalDateTime timestamp;
        private String errorMessage;

        // Getter和Setter方法
        public String getStepName() {
            return stepName;
        }

        public void setStepName(String stepName) {
            this.stepName = stepName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "MigrationStep{" +
                    "stepName='" + stepName + '\'' +
                    ", description='" + description + '\'' +
                    ", success=" + success +
                    ", timestamp=" + (timestamp != null ? timestamp.format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "null") +
                    (errorMessage != null ? ", errorMessage='" + errorMessage + '\'' : "") +
                    '}';
        }
    }
}
