package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.entity.PersonalizedPath;
import com.itbook.service.PersonalizedLearningService;
import com.itbook.service.RecommendationService;
import com.itbook.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 个性化学习路径控制器
 * 提供个性化学习路径的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-10
 */
@RestController
@RequestMapping("/personalized-paths")
@Tag(name = "个性化学习路径", description = "基于用户画像的智能个性化学习路径")
public class PersonalizedPathController {

    private static final Logger log = LoggerFactory.getLogger(PersonalizedPathController.class);

    @Autowired
    private PersonalizedLearningService personalizedLearningService;

    @Autowired
    private RecommendationService recommendationService;

    /**
     * 生成个性化学习路径
     */
    @PostMapping("/generate")
    @Operation(summary = "生成个性化学习路径", description = "为用户生成针对特定岗位的个性化学习路径")
    public ResponseEntity<ApiResponse<Object>> generatePersonalizedPath(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "目标岗位ID") @RequestParam Long targetJobId) {

        log.info("生成个性化学习路径: userId={}, targetJobId={}", userId, targetJobId);

        try {
            // 生成个性化推荐
            java.util.List<com.itbook.service.RecommendationService.RecommendationResult> recommendations =
                    recommendationService.generatePersonalizedRecommendations(userId, targetJobId, 5);

            // 获取个性化学习建议
            com.itbook.service.PersonalizedLearningService.PersonalizedLearningPlan learningPlan =
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);

            // 构建响应数据
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("recommendations", recommendations);
            response.put("learningPlan", learningPlan);
            response.put("userId", userId);
            response.put("targetJobId", targetJobId);
            response.put("generatedAt", java.time.LocalDateTime.now());

            log.info("个性化学习路径生成成功: userId={}, 推荐数量={}", userId, recommendations.size());
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("生成个性化学习路径失败: userId={}, targetJobId={}", userId, targetJobId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成个性化学习路径失败: " + e.getMessage()));
        }
    }

    /**
     * 批量生成个性化路径
     */
    @PostMapping("/batch-generate")
    @Operation(summary = "批量生成个性化路径", description = "为多个用户批量生成个性化学习路径")
    public ResponseEntity<ApiResponse<String>> batchGeneratePersonalizedPaths(
            @Parameter(description = "批量生成请求") @Valid @RequestBody BatchGenerateRequest request) {
        
        log.info("批量生成个性化路径: userIds={}, jobId={}", request.getUserIds(), request.getJobId());

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("批量个性化路径功能开发中，敬请期待"));
    }

    /**
     * 接受个性化路径
     */
    @PostMapping("/{personalizedPathId}/accept")
    @Operation(summary = "接受个性化路径", description = "用户接受指定的个性化学习路径")
    public ResponseEntity<ApiResponse<PersonalizedPath>> acceptPersonalizedPath(
            @Parameter(description = "个性化路径记录ID") @PathVariable Long personalizedPathId,
            @Parameter(description = "接受个性化路径请求") @Valid @RequestBody AcceptPersonalizedPathRequest request) {
        
        log.info("用户接受个性化路径: personalizedPathId={}, userId={}", personalizedPathId, request.getUserId());

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("个性化路径功能开发中，敬请期待"));
    }

    /**
     * 拒绝个性化路径
     */
    @PostMapping("/{personalizedPathId}/reject")
    @Operation(summary = "拒绝个性化路径", description = "用户拒绝指定的个性化学习路径")
    public ResponseEntity<ApiResponse<PersonalizedPath>> rejectPersonalizedPath(
            @Parameter(description = "个性化路径记录ID") @PathVariable Long personalizedPathId,
            @Parameter(description = "拒绝个性化路径请求") @Valid @RequestBody RejectPersonalizedPathRequest request) {
        
        log.info("用户拒绝个性化路径: personalizedPathId={}, userId={}, reason={}", 
                personalizedPathId, request.getUserId(), request.getReason());

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("个性化路径功能开发中，敬请期待"));
    }

    /**
     * 获取用户的个性化推荐
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户个性化推荐", description = "获取指定用户的个性化学习推荐")
    public ResponseEntity<ApiResponse<Object>> getUserPersonalizedRecommendations(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "目标岗位ID（可选）") @RequestParam(required = false) Long targetJobId,
            @Parameter(description = "推荐数量限制") @RequestParam(defaultValue = "10") int limit) {

        log.info("获取用户个性化推荐: userId={}, targetJobId={}, limit={}", userId, targetJobId, limit);

        try {
            // 生成个性化推荐
            java.util.List<com.itbook.service.RecommendationService.RecommendationResult> recommendations =
                    recommendationService.generatePersonalizedRecommendations(userId, targetJobId, limit);

            // 获取用户画像信息
            com.itbook.service.PersonalizedLearningService.PersonalizedLearningPlan learningPlan =
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);

            // 构建响应数据
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("userId", userId);
            response.put("targetJobId", targetJobId);
            response.put("recommendations", recommendations);
            response.put("learningPlan", learningPlan);
            response.put("totalRecommendations", recommendations.size());

            log.info("获取用户个性化推荐成功: userId={}, 推荐数量={}", userId, recommendations.size());
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("获取用户个性化推荐失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取个性化推荐失败: " + e.getMessage()));
        }
    }

    /**
     * 获取个性化路径详情
     */
    @GetMapping("/{personalizedPathId}")
    @Operation(summary = "获取个性化路径详情", description = "获取指定个性化路径记录的详细信息")
    public ResponseEntity<ApiResponse<PersonalizedPath>> getPersonalizedPathDetail(
            @Parameter(description = "个性化路径记录ID") @PathVariable Long personalizedPathId) {
        
        log.info("获取个性化路径详情: personalizedPathId={}", personalizedPathId);

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("个性化路径功能开发中，敬请期待"));
    }

    /**
     * 获取个性化路径统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取个性化路径统计", description = "获取个性化路径的统计信息")
    public ResponseEntity<ApiResponse<Object>> getPersonalizedPathStatistics(
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) Long userId) {
        
        log.info("获取个性化路径统计: userId={}", userId);

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("个性化路径功能开发中，敬请期待"));
    }

    /**
     * 更新个性化路径反馈
     */
    @PutMapping("/{personalizedPathId}/feedback")
    @Operation(summary = "更新个性化路径反馈", description = "用户对个性化路径提供反馈")
    public ResponseEntity<ApiResponse<PersonalizedPath>> updatePersonalizedPathFeedback(
            @Parameter(description = "个性化路径记录ID") @PathVariable Long personalizedPathId,
            @Parameter(description = "反馈更新请求") @Valid @RequestBody UpdateFeedbackRequest request) {
        
        log.info("更新个性化路径反馈: personalizedPathId={}, rating={}", 
                personalizedPathId, request.getRating());

        // 暂时返回开发中状态
        return ResponseEntity.internalServerError()
                .body(ApiResponse.error("个性化路径功能开发中，敬请期待"));
    }

    // 请求DTO类定义

    /**
     * 生成个性化路径请求
     */
    public static class GeneratePersonalizedPathRequest {
        private Long userId;
        private Long jobId;
        private String preferences; // 用户偏好设置

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getJobId() { return jobId; }
        public void setJobId(Long jobId) { this.jobId = jobId; }
        
        public String getPreferences() { return preferences; }
        public void setPreferences(String preferences) { this.preferences = preferences; }
    }

    /**
     * 批量生成请求
     */
    public static class BatchGenerateRequest {
        private Long[] userIds;
        private Long jobId;

        // Getter和Setter方法
        public Long[] getUserIds() { return userIds; }
        public void setUserIds(Long[] userIds) { this.userIds = userIds; }
        
        public Long getJobId() { return jobId; }
        public void setJobId(Long jobId) { this.jobId = jobId; }
    }

    /**
     * 接受个性化路径请求
     */
    public static class AcceptPersonalizedPathRequest {
        private Long userId;
        private String note; // 用户备注

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
    }

    /**
     * 拒绝个性化路径请求
     */
    public static class RejectPersonalizedPathRequest {
        private Long userId;
        private String reason; // 拒绝理由

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    /**
     * 更新反馈请求
     */
    public static class UpdateFeedbackRequest {
        private Integer rating; // 评分 1-5
        private String feedback; // 反馈文本

        // Getter和Setter方法
        public Integer getRating() { return rating; }
        public void setRating(Integer rating) { this.rating = rating; }
        
        public String getFeedback() { return feedback; }
        public void setFeedback(String feedback) { this.feedback = feedback; }
    }
}
