package com.itbook.controller;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.UserAtomicSkillMastery;
import com.itbook.service.AtomicSkillService;
import com.itbook.dto.AtomicSkillDTO;
import com.itbook.dto.SkillGraphDTO;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 原子技能控制器
 * 提供原子技能相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/atomic-skills")
@Tag(name = "原子技能管理", description = "原子技能的CRUD操作、技能图谱查询、技能推荐等API")
public class AtomicSkillController {

    @Autowired
    private AtomicSkillService atomicSkillService;

    /**
     * 创建原子技能
     */
    @PostMapping
    @Operation(summary = "创建原子技能", description = "创建新的原子技能")
    public ApiResponse<AtomicSkill> createAtomicSkill(@Valid @RequestBody AtomicSkillDTO skillDTO) {
        return atomicSkillService.createAtomicSkill(skillDTO);
    }

    /**
     * 更新原子技能
     */
    @PutMapping("/{skillId}")
    @Operation(summary = "更新原子技能", description = "更新指定ID的原子技能信息")
    public ApiResponse<AtomicSkill> updateAtomicSkill(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Valid @RequestBody AtomicSkillDTO skillDTO) {
        return atomicSkillService.updateAtomicSkill(skillId, skillDTO);
    }

    /**
     * 获取原子技能详情
     */
    @GetMapping("/{skillId}")
    @Operation(summary = "获取原子技能详情", description = "根据ID获取原子技能的详细信息")
    public ApiResponse<AtomicSkill> getAtomicSkillById(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return atomicSkillService.getAtomicSkillById(skillId);
    }

    /**
     * 分页查询原子技能
     */
    @GetMapping
    @Operation(summary = "分页查询原子技能", description = "根据条件分页查询原子技能列表")
    public ApiResponse<Page<AtomicSkill>> getAtomicSkills(
            @Parameter(description = "技能分类") @RequestParam(required = false) String category,
            @Parameter(description = "技能子分类") @RequestParam(required = false) String subcategory,
            @Parameter(description = "难度级别") @RequestParam(required = false) AtomicSkill.DifficultyLevel difficultyLevel,
            @Parameter(description = "状态") @RequestParam(required = false) AtomicSkill.Status status,
            Pageable pageable) {
        return atomicSkillService.getAtomicSkills(category, subcategory, difficultyLevel, status, pageable);
    }

    /**
     * 搜索原子技能
     */
    @GetMapping("/search")
    @Operation(summary = "搜索原子技能", description = "根据关键词搜索原子技能")
    public ApiResponse<List<AtomicSkill>> searchAtomicSkills(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        return atomicSkillService.searchAtomicSkills(keyword);
    }

    /**
     * 高级搜索原子技能
     */
    @GetMapping("/advanced-search")
    @Operation(summary = "高级搜索原子技能", description = "支持多条件的高级搜索功能")
    public ApiResponse<List<AtomicSkill>> advancedSearchAtomicSkills(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "技能分类") @RequestParam(required = false) String category,
            @Parameter(description = "技能子分类") @RequestParam(required = false) String subcategory,
            @Parameter(description = "难度级别") @RequestParam(required = false) AtomicSkill.DifficultyLevel difficultyLevel,
            @Parameter(description = "技能类型") @RequestParam(required = false) AtomicSkill.SkillType skillType,
            @Parameter(description = "最小预计时长") @RequestParam(required = false) Integer minHours,
            @Parameter(description = "最大预计时长") @RequestParam(required = false) Integer maxHours,
            @Parameter(description = "最小评分") @RequestParam(required = false) Double minRating,
            @Parameter(description = "最大评分") @RequestParam(required = false) Double maxRating,
            @Parameter(description = "标签") @RequestParam(required = false) String tag,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "averageRating") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDirection,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "20") int limit) {
        return atomicSkillService.advancedSearchAtomicSkills(
                keyword, category, subcategory, difficultyLevel, skillType,
                minHours, maxHours, minRating, maxRating, tag,
                sortBy, sortDirection, limit);
    }

    /**
     * 根据分类获取技能
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类获取技能", description = "获取指定分类下的所有技能")
    public ApiResponse<List<AtomicSkill>> getSkillsByCategory(
            @Parameter(description = "技能分类") @PathVariable String category,
            @Parameter(description = "子分类") @RequestParam(required = false) String subcategory) {
        return atomicSkillService.getSkillsByCategory(category, subcategory);
    }

    /**
     * 根据难度级别获取技能
     */
    @GetMapping("/difficulty/{difficultyLevel}")
    @Operation(summary = "根据难度获取技能", description = "获取指定难度级别的技能")
    public ApiResponse<List<AtomicSkill>> getSkillsByDifficulty(
            @Parameter(description = "难度级别") @PathVariable AtomicSkill.DifficultyLevel difficultyLevel) {
        return atomicSkillService.getSkillsByDifficulty(difficultyLevel);
    }

    /**
     * 根据技能类型获取技能
     */
    @GetMapping("/type/{skillType}")
    @Operation(summary = "根据类型获取技能", description = "获取指定类型的技能")
    public ApiResponse<List<AtomicSkill>> getSkillsByType(
            @Parameter(description = "技能类型") @PathVariable AtomicSkill.SkillType skillType) {
        return atomicSkillService.getSkillsByType(skillType);
    }

    /**
     * 根据标签搜索技能
     */
    @GetMapping("/tag/{tag}")
    @Operation(summary = "根据标签搜索技能", description = "根据标签搜索相关技能")
    public ApiResponse<List<AtomicSkill>> getSkillsByTag(
            @Parameter(description = "标签") @PathVariable String tag) {
        return atomicSkillService.getSkillsByTag(tag);
    }

    /**
     * 获取技能的前置技能
     */
    @GetMapping("/{skillId}/prerequisites")
    @Operation(summary = "获取前置技能", description = "获取指定技能的所有前置技能")
    public ApiResponse<List<AtomicSkill>> getPrerequisiteSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return atomicSkillService.getPrerequisiteSkills(skillId);
    }

    /**
     * 获取技能的后续技能
     */
    @GetMapping("/{skillId}/successors")
    @Operation(summary = "获取后续技能", description = "获取指定技能的所有后续技能")
    public ApiResponse<List<AtomicSkill>> getSuccessorSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return atomicSkillService.getSuccessorSkills(skillId);
    }

    /**
     * 获取相关技能
     */
    @GetMapping("/{skillId}/related")
    @Operation(summary = "获取相关技能", description = "获取与指定技能相关的技能列表")
    public ApiResponse<List<AtomicSkill>> getRelatedSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return atomicSkillService.getRelatedSkills(skillId);
    }

    /**
     * 构建技能图谱
     */
    @GetMapping("/{skillId}/graph")
    @Operation(summary = "构建技能图谱", description = "以指定技能为根节点构建技能关系图谱")
    public ApiResponse<SkillGraphDTO> buildSkillGraph(
            @Parameter(description = "根技能ID") @PathVariable Long skillId,
            @Parameter(description = "图谱深度") @RequestParam(defaultValue = "3") int depth) {
        return atomicSkillService.buildSkillGraph(skillId, depth);
    }

    /**
     * 获取用户技能掌握度
     */
    @GetMapping("/{skillId}/mastery/{userId}")
    @Operation(summary = "获取用户技能掌握度", description = "获取指定用户对指定技能的掌握情况")
    public ApiResponse<UserAtomicSkillMastery> getUserSkillMastery(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return atomicSkillService.getUserSkillMastery(userId, skillId);
    }

    /**
     * 更新用户技能掌握度
     */
    @PostMapping("/{skillId}/mastery/{userId}")
    @Operation(summary = "更新用户技能掌握度", description = "更新指定用户对指定技能的掌握情况")
    public ApiResponse<UserAtomicSkillMastery> updateUserSkillMastery(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "掌握水平") @RequestParam UserAtomicSkillMastery.MasteryLevel masteryLevel,
            @Parameter(description = "掌握分数") @RequestParam BigDecimal masteryScore) {
        return atomicSkillService.updateUserSkillMastery(userId, skillId, masteryLevel, masteryScore);
    }

    /**
     * 推荐学习技能
     */
    @GetMapping("/recommendations/{userId}")
    @Operation(summary = "推荐学习技能", description = "基于用户当前技能水平和职业目标推荐下一步学习的技能")
    public ApiResponse<List<AtomicSkill>> recommendSkillsForUser(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "职业目标ID") @RequestParam(required = false) Long careerGoalId) {
        return atomicSkillService.recommendSkillsForUser(userId, careerGoalId);
    }

    /**
     * 获取技能统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取技能统计信息", description = "获取原子技能库的统计信息")
    public ApiResponse<java.util.Map<String, Object>> getSkillStatistics() {
        // 实现技能统计逻辑
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalSkills", 0);
        statistics.put("skillsByCategory", new java.util.HashMap<>());
        statistics.put("skillsByDifficulty", new java.util.HashMap<>());
        return ApiResponse.success(statistics);
    }

    /**
     * 批量导入原子技能
     */
    @PostMapping("/batch-import")
    @Operation(summary = "批量导入原子技能", description = "批量导入原子技能数据")
    public ApiResponse<List<AtomicSkill>> batchImportSkills(
            @Valid @RequestBody List<AtomicSkillDTO> skillDTOs) {
        // 实现批量导入逻辑
        java.util.List<AtomicSkill> importedSkills = new java.util.ArrayList<>();
        for (AtomicSkillDTO dto : skillDTOs) {
            ApiResponse<AtomicSkill> result = atomicSkillService.createAtomicSkill(dto);
            if (result.getCode() == 20000) {
                importedSkills.add(result.getData());
            }
        }
        return ApiResponse.success(importedSkills);
    }

    /**
     * 验证技能关系
     */
    @PostMapping("/{sourceSkillId}/validate-relationship/{targetSkillId}")
    @Operation(summary = "验证技能关系", description = "验证两个技能之间是否可以建立指定类型的关系")
    public ApiResponse<Boolean> validateSkillRelationship(
            @Parameter(description = "源技能ID") @PathVariable Long sourceSkillId,
            @Parameter(description = "目标技能ID") @PathVariable Long targetSkillId,
            @Parameter(description = "关系类型") @RequestParam String relationshipType) {
        // 实现关系验证逻辑
        return ApiResponse.success(true);
    }

    /**
     * 获取技能学习路径建议
     */
    @GetMapping("/{skillId}/learning-path-suggestions")
    @Operation(summary = "获取技能学习路径建议", description = "获取掌握指定技能的学习路径建议")
    public ApiResponse<List<AtomicSkill>> getSkillLearningPathSuggestions(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        // 基于技能依赖关系生成学习路径建议
        return atomicSkillService.getPrerequisiteSkills(skillId);
    }
}
