package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.entity.AtomicSkill;
import com.itbook.service.AtomicSkillService;
import com.itbook.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * 原子技能控制器
 * 提供原子技能管理的REST API接口，包括技能的CRUD操作、搜索和推荐功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/atomic-skills")
@Tag(name = "原子技能管理", description = "原子技能的创建、查询、更新、删除、搜索和推荐等操作")
public class AtomicSkillController {

    private static final Logger log = LoggerFactory.getLogger(AtomicSkillController.class);

    @Autowired
    private AtomicSkillService atomicSkillService;

    /**
     * 创建原子技能
     */
    @PostMapping
    @Operation(summary = "创建原子技能", description = "创建新的原子技能")
    public ResponseEntity<ApiResponse<AtomicSkill>> createAtomicSkill(
            @Parameter(description = "原子技能信息") @Valid @RequestBody AtomicSkill atomicSkill) {
        
        log.info("创建原子技能: name={}", atomicSkill.getName());
        
        try {
            AtomicSkill createdSkill = atomicSkillService.createAtomicSkill(atomicSkill);
            return ResponseEntity.ok(ApiResponse.success(createdSkill));
        } catch (BusinessException e) {
            log.warn("创建原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("创建原子技能失败"));
        }
    }

    /**
     * 根据ID获取原子技能
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取原子技能详情", description = "根据ID获取原子技能的详细信息")
    public ResponseEntity<ApiResponse<AtomicSkill>> getAtomicSkillById(
            @Parameter(description = "技能ID") @PathVariable Long id) {
        
        log.debug("获取原子技能详情: id={}", id);
        
        try {
            AtomicSkill skill = atomicSkillService.getAtomicSkillById(id);
            return ResponseEntity.ok(ApiResponse.success(skill));
        } catch (BusinessException e) {
            log.warn("获取原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("获取原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取原子技能失败"));
        }
    }

    /**
     * 更新原子技能
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新原子技能", description = "更新指定ID的原子技能信息")
    public ResponseEntity<ApiResponse<AtomicSkill>> updateAtomicSkill(
            @Parameter(description = "技能ID") @PathVariable Long id,
            @Parameter(description = "更新的技能信息") @Valid @RequestBody AtomicSkill atomicSkill) {
        
        log.info("更新原子技能: id={}, name={}", id, atomicSkill.getName());
        
        try {
            AtomicSkill updatedSkill = atomicSkillService.updateAtomicSkill(id, atomicSkill);
            return ResponseEntity.ok(ApiResponse.success(updatedSkill));
        } catch (BusinessException e) {
            log.warn("更新原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("更新原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("更新原子技能失败"));
        }
    }

    /**
     * 删除原子技能
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除原子技能", description = "删除指定ID的原子技能（软删除）")
    public ResponseEntity<ApiResponse<String>> deleteAtomicSkill(
            @Parameter(description = "技能ID") @PathVariable Long id) {

        log.info("删除原子技能: id={}", id);

        try {
            atomicSkillService.deleteAtomicSkill(id);
            return ResponseEntity.ok(ApiResponse.success("原子技能删除成功"));
        } catch (BusinessException e) {
            log.warn("删除原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("删除原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("删除原子技能失败"));
        }
    }

    /**
     * 分页查询原子技能
     */
    @GetMapping
    @Operation(summary = "分页查询原子技能", description = "分页获取原子技能列表")
    public ResponseEntity<ApiResponse<Page<AtomicSkill>>> getAtomicSkills(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("分页查询原子技能: page={}, size={}", pageable.getPageNumber(), pageable.getPageSize());
        
        try {
            Page<AtomicSkill> skills = atomicSkillService.getAtomicSkills(pageable);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("查询原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("查询原子技能失败"));
        }
    }

    /**
     * 根据分类查询原子技能
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类查询技能", description = "根据技能分类分页查询原子技能")
    public ResponseEntity<ApiResponse<Page<AtomicSkill>>> getAtomicSkillsByCategory(
            @Parameter(description = "技能分类") @PathVariable String category,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("根据分类查询原子技能: category={}", category);
        
        try {
            Page<AtomicSkill> skills = atomicSkillService.getAtomicSkillsByCategory(category, pageable);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("根据分类查询原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("查询原子技能失败"));
        }
    }

    /**
     * 搜索原子技能
     */
    @GetMapping("/search")
    @Operation(summary = "搜索原子技能", description = "根据关键词搜索原子技能")
    public ResponseEntity<ApiResponse<Page<AtomicSkill>>> searchAtomicSkills(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("搜索原子技能: keyword={}", keyword);
        
        try {
            Page<AtomicSkill> skills = atomicSkillService.searchAtomicSkills(keyword, pageable);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("搜索原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("搜索原子技能失败"));
        }
    }

    /**
     * 根据职业目标推荐技能
     */
    @GetMapping("/recommendations/career/{careerGoalId}")
    @Operation(summary = "职业技能推荐", description = "根据职业目标推荐相关的原子技能")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getRecommendedSkillsForCareer(
            @Parameter(description = "职业目标ID") @PathVariable Long careerGoalId,
            @Parameter(description = "排除的技能ID列表") @RequestParam(required = false) Set<Long> excludeSkillIds) {
        
        log.debug("根据职业目标推荐技能: careerGoalId={}", careerGoalId);
        
        try {
            List<AtomicSkill> skills = atomicSkillService.getRecommendedSkillsForCareer(careerGoalId, excludeSkillIds);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("推荐技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("推荐技能失败"));
        }
    }

    /**
     * 发布技能
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "发布技能", description = "将技能状态设置为已发布")
    public ResponseEntity<ApiResponse<AtomicSkill>> publishAtomicSkill(
            @Parameter(description = "技能ID") @PathVariable Long id) {
        
        log.info("发布原子技能: id={}", id);
        
        try {
            AtomicSkill publishedSkill = atomicSkillService.publishAtomicSkill(id);
            return ResponseEntity.ok(ApiResponse.success(publishedSkill));
        } catch (BusinessException e) {
            log.warn("发布原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("发布原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("发布原子技能失败"));
        }
    }

    /**
     * 获取所有技能分类
     */
    @GetMapping("/categories")
    @Operation(summary = "获取技能分类", description = "获取所有可用的技能分类列表")
    public ResponseEntity<ApiResponse<List<String>>> getAllCategories() {
        
        log.debug("获取所有技能分类");
        
        try {
            List<String> categories = atomicSkillService.getAllCategories();
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取技能分类时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取技能分类失败"));
        }
    }

    /**
     * 根据难度级别查询技能
     */
    @GetMapping("/difficulty/{difficultyLevel}")
    @Operation(summary = "根据难度查询技能", description = "根据难度级别分页查询原子技能")
    public ResponseEntity<ApiResponse<Page<AtomicSkill>>> getAtomicSkillsByDifficulty(
            @Parameter(description = "难度级别") @PathVariable AtomicSkill.DifficultyLevel difficultyLevel,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("根据难度级别查询原子技能: difficultyLevel={}", difficultyLevel);
        
        try {
            Page<AtomicSkill> skills = atomicSkillService.getAtomicSkillsByDifficulty(difficultyLevel, pageable);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("根据难度查询原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("查询原子技能失败"));
        }
    }

    /**
     * 获取热门技能
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门技能", description = "获取按评分排序的热门原子技能")
    public ResponseEntity<ApiResponse<Page<AtomicSkill>>> getPopularAtomicSkills(
            @PageableDefault(size = 20) Pageable pageable) {

        log.debug("获取热门原子技能");

        try {
            Page<AtomicSkill> skills = atomicSkillService.getPopularAtomicSkills(pageable);
            return ResponseEntity.ok(ApiResponse.success(skills));
        } catch (Exception e) {
            log.error("获取热门技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取热门技能失败"));
        }
    }

    /**
     * 批量创建原子技能
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建技能", description = "批量创建多个原子技能")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> createAtomicSkillsBatch(
            @Parameter(description = "原子技能列表") @Valid @RequestBody List<AtomicSkill> atomicSkills) {

        log.info("批量创建原子技能: count={}", atomicSkills.size());

        try {
            List<AtomicSkill> createdSkills = atomicSkillService.createAtomicSkillsBatch(atomicSkills);
            return ResponseEntity.ok(ApiResponse.success(createdSkills));
        } catch (BusinessException e) {
            log.warn("批量创建原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("批量创建原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("批量创建原子技能失败"));
        }
    }

    /**
     * 批量删除原子技能
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除技能", description = "批量删除多个原子技能（软删除）")
    public ResponseEntity<ApiResponse<String>> deleteAtomicSkillsBatch(
            @Parameter(description = "技能ID列表") @RequestBody Set<Long> skillIds) {

        log.info("批量删除原子技能: ids={}", skillIds);

        try {
            atomicSkillService.deleteAtomicSkillsBatch(skillIds);
            return ResponseEntity.ok(ApiResponse.success("批量删除原子技能成功"));
        } catch (BusinessException e) {
            log.warn("批量删除原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("批量删除原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("批量删除原子技能失败"));
        }
    }

    /**
     * 批量发布技能
     */
    @PostMapping("/batch/publish")
    @Operation(summary = "批量发布技能", description = "批量将技能状态设置为已发布")
    public ResponseEntity<ApiResponse<Integer>> publishAtomicSkillsBatch(
            @Parameter(description = "技能ID列表") @RequestBody Set<Long> skillIds) {

        log.info("批量发布原子技能: ids={}", skillIds);

        try {
            int publishedCount = atomicSkillService.publishAtomicSkillsBatch(skillIds);
            return ResponseEntity.ok(ApiResponse.success(publishedCount));
        } catch (BusinessException e) {
            log.warn("批量发布原子技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("批量发布原子技能时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("批量发布原子技能失败"));
        }
    }

    /**
     * 获取技能统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取技能统计", description = "获取原子技能的统计信息")
    public ResponseEntity<ApiResponse<AtomicSkillService.AtomicSkillStatistics>> getSkillStatistics() {

        log.debug("获取技能统计信息");

        try {
            AtomicSkillService.AtomicSkillStatistics statistics = atomicSkillService.getSkillStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取技能统计时发生未知错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取技能统计失败"));
        }
    }
}
