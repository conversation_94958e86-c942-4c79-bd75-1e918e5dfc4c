package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.PersonalizedRecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 个性化推荐控制器
 * 提供基于用户画像的个性化学习推荐API
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/personalized-recommendations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "个性化推荐", description = "基于用户画像的个性化学习推荐功能")
public class PersonalizedRecommendationController {

    private final PersonalizedRecommendationService personalizedRecommendationService;

    /**
     * 获取用户个性化推荐
     */
    @GetMapping("/users/{userId}")
    @Operation(summary = "获取个性化推荐", description = "基于用户学习历史、技能掌握情况和职业目标生成个性化推荐")
    public ResponseEntity<ApiResponse<PersonalizedRecommendationService.PersonalizedRecommendation>> 
            getPersonalizedRecommendation(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户个性化推荐: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation));
        } catch (IllegalArgumentException e) {
            log.warn("获取个性化推荐失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取个性化推荐时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取个性化推荐失败"));
        }
    }

    /**
     * 获取用户画像分析
     */
    @GetMapping("/users/{userId}/profile")
    @Operation(summary = "获取用户画像", description = "分析用户的学习偏好、技能掌握情况等画像信息")
    public ResponseEntity<ApiResponse<PersonalizedRecommendationService.UserProfile>> 
            getUserProfile(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户画像: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation.getUserProfile()));
        } catch (IllegalArgumentException e) {
            log.warn("获取用户画像失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取用户画像时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取用户画像失败"));
        }
    }

    /**
     * 获取推荐的学习路径
     */
    @GetMapping("/users/{userId}/paths")
    @Operation(summary = "获取推荐路径", description = "获取为用户推荐的学习路径列表")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedRecommendationService.RecommendedPath>>> 
            getRecommendedPaths(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取推荐学习路径: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation.getRecommendedPaths()));
        } catch (IllegalArgumentException e) {
            log.warn("获取推荐路径失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取推荐路径时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取推荐路径失败"));
        }
    }

    /**
     * 获取推荐的技能
     */
    @GetMapping("/users/{userId}/skills")
    @Operation(summary = "获取推荐技能", description = "获取为用户推荐的技能列表")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedRecommendationService.RecommendedSkill>>> 
            getRecommendedSkills(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取推荐技能: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation.getRecommendedSkills()));
        } catch (IllegalArgumentException e) {
            log.warn("获取推荐技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取推荐技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取推荐技能失败"));
        }
    }

    /**
     * 获取学习建议
     */
    @GetMapping("/users/{userId}/advice")
    @Operation(summary = "获取学习建议", description = "获取为用户生成的个性化学习建议")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedRecommendationService.LearningAdvice>>> 
            getLearningAdvice(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取学习建议: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation.getLearningAdvices()));
        } catch (IllegalArgumentException e) {
            log.warn("获取学习建议失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取学习建议时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取学习建议失败"));
        }
    }

    /**
     * 刷新用户推荐
     */
    @PostMapping("/users/{userId}/refresh")
    @Operation(summary = "刷新推荐", description = "重新生成用户的个性化推荐")
    public ResponseEntity<ApiResponse<PersonalizedRecommendationService.PersonalizedRecommendation>> 
            refreshRecommendation(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("刷新用户推荐: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            return ResponseEntity.ok(ApiResponse.success(recommendation));
        } catch (IllegalArgumentException e) {
            log.warn("刷新推荐失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("刷新推荐时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("刷新推荐失败"));
        }
    }

    /**
     * 获取推荐解释
     */
    @GetMapping("/users/{userId}/explanation")
    @Operation(summary = "获取推荐解释", description = "获取推荐算法的解释说明")
    public ResponseEntity<ApiResponse<RecommendationExplanation>> 
            getRecommendationExplanation(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取推荐解释: userId={}", userId);
        
        try {
            PersonalizedRecommendationService.PersonalizedRecommendation recommendation = 
                    personalizedRecommendationService.generatePersonalizedRecommendation(userId);
            
            RecommendationExplanation explanation = new RecommendationExplanation();
            explanation.setUserId(userId);
            explanation.setAlgorithmDescription("基于用户学习历史、技能掌握情况和职业目标的协同过滤推荐算法");
            explanation.setFactorsConsidered(java.util.Arrays.asList(
                    "用户技能掌握情况", "职业目标匹配度", "学习历史偏好", "技能关系图谱", "学习难度适配"));
            explanation.setRecommendationCount(recommendation.getRecommendedSkills().size());
            explanation.setConfidenceScore(calculateConfidenceScore(recommendation));
            
            return ResponseEntity.ok(ApiResponse.success(explanation));
        } catch (IllegalArgumentException e) {
            log.warn("获取推荐解释失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取推荐解释时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取推荐解释失败"));
        }
    }

    /**
     * 计算推荐置信度
     */
    private double calculateConfidenceScore(PersonalizedRecommendationService.PersonalizedRecommendation recommendation) {
        // 基于用户数据完整性计算置信度
        double score = 0.0;
        
        // 职业目标数据
        if (!recommendation.getUserProfile().getCareerGoals().isEmpty()) {
            score += 0.3;
        }
        
        // 技能掌握数据
        if (!recommendation.getUserProfile().getSkillMasteries().isEmpty()) {
            score += 0.4;
        }
        
        // 学习历史数据
        if (!recommendation.getUserProfile().getLearningProgress().isEmpty()) {
            score += 0.3;
        }
        
        return Math.min(score, 1.0);
    }

    // 内部数据结构
    public static class RecommendationExplanation {
        private Long userId;
        private String algorithmDescription;
        private java.util.List<String> factorsConsidered;
        private int recommendationCount;
        private double confidenceScore;
        
        // getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getAlgorithmDescription() { return algorithmDescription; }
        public void setAlgorithmDescription(String algorithmDescription) { this.algorithmDescription = algorithmDescription; }
        
        public java.util.List<String> getFactorsConsidered() { return factorsConsidered; }
        public void setFactorsConsidered(java.util.List<String> factorsConsidered) { this.factorsConsidered = factorsConsidered; }
        
        public int getRecommendationCount() { return recommendationCount; }
        public void setRecommendationCount(int recommendationCount) { this.recommendationCount = recommendationCount; }
        
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
    }
}
