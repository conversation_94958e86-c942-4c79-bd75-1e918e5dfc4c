package com.itbook.algorithm;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.dto.PersonalizationFactors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个性化推荐引擎
 * 基于用户画像、学习历史和行为模式提供个性化的技能推荐和路径优化
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Component
public class PersonalizationEngine {

    @Autowired
    private UserAtomicSkillMasteryRepository masteryRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private SkillRelationshipRepository skillRelationshipRepository;

    @Autowired
    private DynamicLearningPathRepository dynamicPathRepository;

    /**
     * 分析用户个性化因子
     */
    public PersonalizationFactors analyzePersonalizationFactors(Long userId) {
        PersonalizationFactors factors = new PersonalizationFactors();
        factors.setUserId(userId);

        // 获取用户基本画像
        UserProfile profile = userProfileRepository.findByUserId(userId).orElse(null);
        if (profile != null) {
            factors.setCurrentSkillLevel(profile.getCurrentSkillLevel());
            factors.setLearningStyle(profile.getLearningStyle());
            factors.setAvailableTimePerWeek(profile.getAvailableTimePerWeek());
            factors.setPreferredLearningPace(profile.getPreferredLearningPace());
            factors.setHasProgrammingExperience(profile.getHasProgrammingExperience());
        }

        // 分析学习历史
        List<UserAtomicSkillMastery> masteries = masteryRepository.findByUserId(userId);
        analyzeLearningHistory(factors, masteries);

        // 分析学习模式
        analyzeLearningPatterns(factors, masteries);

        // 推断偏好
        inferPreferences(factors, masteries);

        return factors;
    }

    /**
     * 分析学习历史
     */
    private void analyzeLearningHistory(PersonalizationFactors factors, List<UserAtomicSkillMastery> masteries) {
        if (masteries.isEmpty()) {
            factors.setTotalSkillsMastered(0);
            factors.setAverageCompletionRate(BigDecimal.ZERO);
            return;
        }

        // 统计掌握的技能数量
        long masteredCount = masteries.stream()
                .filter(m -> m.getMasteryLevel() != UserAtomicSkillMastery.MasteryLevel.NONE)
                .count();
        factors.setTotalSkillsMastered((int) masteredCount);

        // 计算平均完成率
        BigDecimal totalScore = masteries.stream()
                .map(UserAtomicSkillMastery::getMasteryScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal averageScore = totalScore.divide(new BigDecimal(masteries.size()), 2, RoundingMode.HALF_UP);
        factors.setAverageCompletionRate(averageScore);

        // 分析强弱领域
        analyzeSkillCategories(factors, masteries);
    }

    /**
     * 分析技能类别强弱
     */
    private void analyzeSkillCategories(PersonalizationFactors factors, List<UserAtomicSkillMastery> masteries) {
        Map<String, List<BigDecimal>> categoryScores = new HashMap<>();

        for (UserAtomicSkillMastery mastery : masteries) {
            if (mastery.getAtomicSkill() != null) {
                String category = mastery.getAtomicSkill().getCategory();
                categoryScores.computeIfAbsent(category, k -> new ArrayList<>())
                        .add(mastery.getMasteryScore());
            }
        }

        List<String> strongCategories = new ArrayList<>();
        List<String> weakCategories = new ArrayList<>();

        for (Map.Entry<String, List<BigDecimal>> entry : categoryScores.entrySet()) {
            String category = entry.getKey();
            List<BigDecimal> scores = entry.getValue();

            BigDecimal avgScore = scores.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(scores.size()), 2, RoundingMode.HALF_UP);

            if (avgScore.compareTo(new BigDecimal("75")) >= 0) {
                strongCategories.add(category);
            } else if (avgScore.compareTo(new BigDecimal("50")) < 0) {
                weakCategories.add(category);
            }
        }

        factors.setStrongCategories(strongCategories);
        factors.setWeakCategories(weakCategories);
    }

    /**
     * 分析学习模式
     */
    private void analyzeLearningPatterns(PersonalizationFactors factors, List<UserAtomicSkillMastery> masteries) {
        // 分析学习频率
        Map<Integer, Integer> hourlyActivity = analyzeHourlyActivity(masteries);
        factors.setPreferredStudyTimes(getTopStudyHours(hourlyActivity, 3));

        // 分析学习持续时间
        double avgSessionTime = calculateAverageSessionTime(masteries);
        factors.setDailyStudyTime((int) avgSessionTime);

        // 分析难度偏好
        AtomicSkill.DifficultyLevel preferredDifficulty = inferPreferredDifficulty(masteries);
        factors.setPreferredDifficulty(preferredDifficulty.name());
    }

    /**
     * 分析每小时活动模式
     */
    private Map<Integer, Integer> analyzeHourlyActivity(List<UserAtomicSkillMastery> masteries) {
        Map<Integer, Integer> hourlyActivity = new HashMap<>();

        for (UserAtomicSkillMastery mastery : masteries) {
            if (mastery.getLastPracticedAt() != null) {
                int hour = mastery.getLastPracticedAt().getHour();
                hourlyActivity.put(hour, hourlyActivity.getOrDefault(hour, 0) + 1);
            }
        }

        return hourlyActivity;
    }

    /**
     * 获取最活跃的学习时间
     */
    private int[] getTopStudyHours(Map<Integer, Integer> hourlyActivity, int topN) {
        return hourlyActivity.entrySet().stream()
                .sorted(Map.Entry.<Integer, Integer>comparingByValue().reversed())
                .limit(topN)
                .mapToInt(Map.Entry::getKey)
                .toArray();
    }

    /**
     * 计算平均学习时长
     */
    private double calculateAverageSessionTime(List<UserAtomicSkillMastery> masteries) {
        return masteries.stream()
                .filter(m -> m.getLearningHours() != null)
                .mapToDouble(m -> m.getLearningHours().doubleValue())
                .average()
                .orElse(60.0); // 默认60分钟
    }

    /**
     * 推断偏好难度
     */
    private AtomicSkill.DifficultyLevel inferPreferredDifficulty(List<UserAtomicSkillMastery> masteries) {
        Map<AtomicSkill.DifficultyLevel, Long> difficultyCount = masteries.stream()
                .filter(m -> m.getAtomicSkill() != null)
                .filter(m -> m.getMasteryLevel() != UserAtomicSkillMastery.MasteryLevel.NONE)
                .collect(Collectors.groupingBy(
                        m -> m.getAtomicSkill().getDifficultyLevel(),
                        Collectors.counting()
                ));

        return difficultyCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(AtomicSkill.DifficultyLevel.BEGINNER);
    }

    /**
     * 推断用户偏好
     */
    private void inferPreferences(PersonalizationFactors factors, List<UserAtomicSkillMastery> masteries) {
        // 推断学习动机
        int motivationLevel = calculateMotivationLevel(masteries);
        factors.setMotivationLevel(motivationLevel);

        // 推断学习目标
        List<String> learningGoals = inferLearningGoals(masteries);
        factors.setLearningGoals(learningGoals);

        // 推断内容类型偏好
        List<String> preferredContentTypes = inferContentTypePreferences(factors);
        factors.setPreferredContentTypes(preferredContentTypes);
    }

    /**
     * 计算动机水平
     */
    private int calculateMotivationLevel(List<UserAtomicSkillMastery> masteries) {
        if (masteries.isEmpty()) return 5;

        // 基于学习频率和完成率计算动机
        long recentActivity = masteries.stream()
                .filter(m -> m.getLastPracticedAt() != null)
                .filter(m -> ChronoUnit.DAYS.between(m.getLastPracticedAt(), LocalDateTime.now()) <= 7)
                .count();

        double completionRate = masteries.stream()
                .mapToDouble(m -> m.getMasteryScore().doubleValue())
                .average()
                .orElse(0.0);

        // 简单的动机计算公式
        int motivationScore = (int) Math.min(10, (recentActivity * 2) + (completionRate / 10));
        return Math.max(1, motivationScore);
    }

    /**
     * 推断学习目标
     */
    private List<String> inferLearningGoals(List<UserAtomicSkillMastery> masteries) {
        List<String> goals = new ArrayList<>();

        // 基于技能类别推断目标
        Map<String, Long> categoryCount = masteries.stream()
                .filter(m -> m.getAtomicSkill() != null)
                .collect(Collectors.groupingBy(
                        m -> m.getAtomicSkill().getCategory(),
                        Collectors.counting()
                ));

        categoryCount.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(3)
                .forEach(entry -> goals.add("掌握" + entry.getKey() + "技能"));

        return goals;
    }

    /**
     * 推断内容类型偏好
     */
    private List<String> inferContentTypePreferences(PersonalizationFactors factors) {
        List<String> preferences = new ArrayList<>();

        // 基于学习风格推断内容偏好
        if (factors.getLearningStyle() != null) {
            switch (factors.getLearningStyle()) {
                case THEORETICAL:
                    preferences.addAll(Arrays.asList("text", "documentation", "book"));
                    break;
                case PRACTICAL:
                    preferences.addAll(Arrays.asList("interactive", "practice", "hands-on"));
                    break;
                case PROJECT_DRIVEN:
                    preferences.addAll(Arrays.asList("project", "case-study", "real-world"));
                    break;
                case MIXED:
                default:
                    preferences.addAll(Arrays.asList("video", "interactive", "text", "project"));
                    break;
            }
        }

        return preferences;
    }

    /**
     * 计算技能推荐分数
     */
    public double calculateSkillRecommendationScore(Long userId, AtomicSkill skill, PersonalizationFactors factors) {
        double score = 0.0;

        // 1. 基础适配度 (30%)
        score += calculateBasicFitScore(skill, factors) * 0.3;

        // 2. 难度匹配度 (25%)
        score += calculateDifficultyMatchScore(skill, factors) * 0.25;

        // 3. 职业相关性 (20%)
        score += calculateCareerRelevanceScore(skill, factors) * 0.2;

        // 4. 学习路径连贯性 (15%)
        score += calculatePathContinuityScore(userId, skill) * 0.15;

        // 5. 个人兴趣匹配 (10%)
        score += calculateInterestMatchScore(skill, factors) * 0.1;

        return Math.min(1.0, Math.max(0.0, score));
    }

    /**
     * 计算基础适配度
     */
    private double calculateBasicFitScore(AtomicSkill skill, PersonalizationFactors factors) {
        double score = 0.5; // 基础分

        // 技能类型匹配
        if (skill.getSkillType() == AtomicSkill.SkillType.CORE) {
            score += 0.3;
        }

        // 预计时长匹配
        if (skill.getEstimatedHours() != null && factors.getAvailableTimePerWeek() > 0) {
            double timeRatio = skill.getEstimatedHours().doubleValue() / factors.getAvailableTimePerWeek();
            if (timeRatio <= 1.0) {
                score += 0.2;
            } else if (timeRatio <= 2.0) {
                score += 0.1;
            }
        }

        return Math.min(1.0, score);
    }

    /**
     * 计算难度匹配度
     */
    private double calculateDifficultyMatchScore(AtomicSkill skill, PersonalizationFactors factors) {
        if (factors.getPreferredDifficulty() == null) {
            return 0.5;
        }

        String preferredDifficulty = factors.getPreferredDifficulty();
        String skillDifficulty = skill.getDifficultyLevel().name();

        if (preferredDifficulty.equals(skillDifficulty)) {
            return 1.0;
        }

        // 相邻难度级别给予部分分数
        List<String> difficultyOrder = Arrays.asList("BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT");
        int preferredIndex = difficultyOrder.indexOf(preferredDifficulty);
        int skillIndex = difficultyOrder.indexOf(skillDifficulty);

        if (Math.abs(preferredIndex - skillIndex) == 1) {
            return 0.7;
        } else if (Math.abs(preferredIndex - skillIndex) == 2) {
            return 0.4;
        }

        return 0.1;
    }

    /**
     * 计算职业相关性
     */
    private double calculateCareerRelevanceScore(AtomicSkill skill, PersonalizationFactors factors) {
        // 检查技能类别是否在用户强项中
        if (factors.getStrongCategories() != null && 
            factors.getStrongCategories().contains(skill.getCategory())) {
            return 0.8;
        }

        // 检查是否在弱项中（需要提升）
        if (factors.getWeakCategories() != null && 
            factors.getWeakCategories().contains(skill.getCategory())) {
            return 0.6;
        }

        return 0.5;
    }

    /**
     * 计算学习路径连贯性
     */
    private double calculatePathContinuityScore(Long userId, AtomicSkill skill) {
        // 检查用户是否已掌握前置技能
        List<SkillRelationship> prerequisites = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skill.getId(), SkillRelationship.RelationshipType.PREREQUISITE);

        if (prerequisites.isEmpty()) {
            return 1.0; // 无前置要求
        }

        int masteredPrerequisites = 0;
        for (SkillRelationship prereq : prerequisites) {
            Optional<UserAtomicSkillMastery> mastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, prereq.getSourceSkillId());
            
            if (mastery.isPresent() && 
                mastery.get().getMasteryLevel() != UserAtomicSkillMastery.MasteryLevel.NONE) {
                masteredPrerequisites++;
            }
        }

        return (double) masteredPrerequisites / prerequisites.size();
    }

    /**
     * 计算兴趣匹配度
     */
    private double calculateInterestMatchScore(AtomicSkill skill, PersonalizationFactors factors) {
        // 基于学习目标匹配
        if (factors.getLearningGoals() != null) {
            for (String goal : factors.getLearningGoals()) {
                if (goal.contains(skill.getCategory()) || goal.contains(skill.getName())) {
                    return 1.0;
                }
            }
        }

        return 0.5;
    }

    /**
     * 生成个性化学习建议
     */
    public List<String> generatePersonalizedSuggestions(PersonalizationFactors factors) {
        List<String> suggestions = new ArrayList<>();

        // 基于学习风格的建议
        if (factors.getLearningStyle() != null) {
            switch (factors.getLearningStyle()) {
                case THEORETICAL:
                    suggestions.add("建议多阅读技术文档和理论资料");
                    suggestions.add("可以参加在线课程系统学习");
                    break;
                case PRACTICAL:
                    suggestions.add("建议多做实践练习和编程题目");
                    suggestions.add("可以参与开源项目获得实战经验");
                    break;
                case PROJECT_DRIVEN:
                    suggestions.add("建议通过完整项目来学习技能");
                    suggestions.add("可以尝试构建个人作品集");
                    break;
            }
        }

        // 基于时间约束的建议
        if (factors.getAvailableTimePerWeek() > 0) {
            if (factors.getAvailableTimePerWeek() < 5) {
                suggestions.add("时间有限，建议专注于核心技能");
                suggestions.add("可以利用碎片时间进行学习");
            } else if (factors.getAvailableTimePerWeek() > 20) {
                suggestions.add("时间充裕，可以尝试更有挑战性的技能");
                suggestions.add("建议制定详细的学习计划");
            }
        }

        // 基于技能水平的建议
        if (factors.getCurrentSkillLevel() <= 2) {
            suggestions.add("建议从基础技能开始，打好基础");
            suggestions.add("不要急于求成，循序渐进很重要");
        } else if (factors.getCurrentSkillLevel() >= 4) {
            suggestions.add("可以尝试更高级的技能和技术");
            suggestions.add("建议关注行业前沿技术");
        }

        return suggestions;
    }
}
