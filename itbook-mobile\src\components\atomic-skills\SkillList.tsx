import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { AtomicSkill, DifficultyLevel, SkillType, MasteryLevel } from '../../types/atomicSkill';
import { AtomicSkillCard } from './AtomicSkillCard';

interface SkillListProps {
  skills: AtomicSkill[];
  masteryData?: Record<number, { level: MasteryLevel; score: number }>;
  loading?: boolean;
  refreshing?: boolean;
  hasMore?: boolean;
  showProgress?: boolean;
  showPrerequisites?: boolean;
  emptyMessage?: string;
  emptyIcon?: string;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  onSkillPress?: (skill: AtomicSkill) => void;
  onMasteryPress?: (skill: AtomicSkill) => void;
  style?: any;
}

/**
 * 原子技能列表组件
 * 支持分页加载、下拉刷新、掌握度显示等功能
 */
export const SkillList: React.FC<SkillListProps> = ({
  skills,
  masteryData = {},
  loading = false,
  refreshing = false,
  hasMore = false,
  showProgress = true,
  showPrerequisites = false,
  emptyMessage = '暂无技能数据',
  emptyIcon = 'library-outline',
  onRefresh,
  onLoadMore,
  onSkillPress,
  onMasteryPress,
  style,
}) => {
  const colors = useThemeColors();
  const [loadingMore, setLoadingMore] = useState(false);

  // 处理加载更多
  const handleLoadMore = useCallback(async () => {
    if (hasMore && !loadingMore && !loading && onLoadMore) {
      setLoadingMore(true);
      try {
        await onLoadMore();
      } finally {
        setLoadingMore(false);
      }
    }
  }, [hasMore, loadingMore, loading, onLoadMore]);

  // 渲染技能卡片
  const renderSkillCard = useCallback(
    ({ item: skill }: { item: AtomicSkill }) => {
      const mastery = masteryData[skill.id];
      
      return (
        <AtomicSkillCard
          skill={skill}
          masteryLevel={mastery?.level}
          masteryScore={mastery?.score}
          showProgress={showProgress}
          showPrerequisites={showPrerequisites}
          onPress={() => onSkillPress?.(skill)}
          onMasteryPress={() => onMasteryPress?.(skill)}
        />
      );
    },
    [masteryData, showProgress, showPrerequisites, onSkillPress, onMasteryPress]
  );

  // 渲染加载更多指示器
  const renderFooter = useCallback(() => {
    if (!hasMore) return null;
    
    return (
      <View style={styles.footerContainer}>
        {loadingMore ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <TouchableOpacity
            style={[styles.loadMoreButton, { backgroundColor: colors.surface }]}
            onPress={handleLoadMore}
            activeOpacity={0.7}
          >
            <Text style={[styles.loadMoreText, { color: colors.primary }]}>
              加载更多
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [hasMore, loadingMore, colors, handleLoadMore]);

  // 渲染空状态
  const renderEmptyState = useCallback(() => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            加载中...
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.centerContainer}>
        <Ionicons
          name={emptyIcon as any}
          size={64}
          color={colors.textTertiary}
          style={styles.emptyIcon}
        />
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          {emptyMessage}
        </Text>
      </View>
    );
  }, [loading, colors, emptyIcon, emptyMessage]);

  // 优化的keyExtractor
  const keyExtractor = useCallback((item: AtomicSkill) => `skill-${item.id}`, []);

  // 计算列表统计信息
  const listStats = useMemo(() => {
    const total = skills.length;
    const masteredCount = skills.filter(skill => {
      const mastery = masteryData[skill.id];
      return mastery && mastery.level !== MasteryLevel.NONE;
    }).length;
    
    return { total, masteredCount };
  }, [skills, masteryData]);

  return (
    <View style={[styles.container, style]}>
      {/* 列表统计信息 */}
      {skills.length > 0 && showProgress && (
        <View style={[styles.statsContainer, { backgroundColor: colors.surface }]}>
          <View style={styles.statItem}>
            <Ionicons name="library-outline" size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: colors.text }]}>
              总计 {listStats.total} 个技能
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="checkmark-circle-outline" size={16} color={colors.success} />
            <Text style={[styles.statText, { color: colors.text }]}>
              已掌握 {listStats.masteredCount} 个
            </Text>
          </View>
        </View>
      )}

      {/* 技能列表 */}
      <FlatList
        data={skills}
        renderItem={renderSkillCard}
        keyExtractor={keyExtractor}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderFooter}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          ) : undefined
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContent,
          skills.length === 0 && styles.emptyListContent,
        ]}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        getItemLayout={(data, index) => ({
          length: 200, // 估算的卡片高度
          offset: 200 * index,
          index,
        })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: tokens.spacing('sm'),
    paddingHorizontal: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginHorizontal: tokens.spacing('md'),
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('xs'),
  },
  listContent: {
    paddingHorizontal: tokens.spacing('md'),
    paddingBottom: tokens.spacing('lg'),
  },
  emptyListContent: {
    flexGrow: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: tokens.spacing('xl'),
  },
  emptyIcon: {
    marginBottom: tokens.spacing('md'),
  },
  emptyText: {
    fontSize: tokens.fontSize('body'),
    textAlign: 'center',
    maxWidth: 200,
  },
  loadingText: {
    fontSize: tokens.fontSize('body'),
    marginTop: tokens.spacing('md'),
  },
  footerContainer: {
    paddingVertical: tokens.spacing('md'),
    alignItems: 'center',
  },
  loadMoreButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    borderWidth: 1,
    borderColor: 'transparent',
  },
  loadMoreText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
});
