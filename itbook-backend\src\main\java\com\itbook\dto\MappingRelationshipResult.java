package com.itbook.dto;

import com.itbook.entity.CareerSkillMapping;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 映射关系建立结果DTO
 * 用于返回映射关系建立的详细结果
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
public class MappingRelationshipResult {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 成功消息
     */
    private String message;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 职业技能总数
     */
    private int totalCareerSkills;

    /**
     * 原子技能总数
     */
    private int totalAtomicSkills;

    /**
     * 已处理的职业技能数
     */
    private int processedCareerSkills;

    /**
     * 成功映射的技能数
     */
    private int successfulMappings;

    /**
     * 失败映射的技能数
     */
    private int failedMappings;

    /**
     * 清理的旧映射数
     */
    private int cleanedMappings;

    /**
     * 创建的映射关系总数
     */
    private int totalMappings;

    /**
     * 高置信度映射数量（>= 0.8）
     */
    private int highConfidenceMappings;

    /**
     * 中等置信度映射数量（0.6 - 0.8）
     */
    private int mediumConfidenceMappings;

    /**
     * 低置信度映射数量（0.4 - 0.6）
     */
    private int lowConfidenceMappings;

    /**
     * 平均置信度分数
     */
    private double averageConfidence;

    /**
     * 按重要程度统计
     */
    private Map<CareerSkillMapping.Importance, Long> importanceStatistics;

    /**
     * 按要求掌握水平统计
     */
    private Map<CareerSkillMapping.RequiredMasteryLevel, Long> masteryLevelStatistics;

    // 构造函数
    public MappingRelationshipResult() {
        this.status = "PENDING";
    }

    // Getter和Setter方法
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public int getTotalCareerSkills() {
        return totalCareerSkills;
    }

    public void setTotalCareerSkills(int totalCareerSkills) {
        this.totalCareerSkills = totalCareerSkills;
    }

    public int getTotalAtomicSkills() {
        return totalAtomicSkills;
    }

    public void setTotalAtomicSkills(int totalAtomicSkills) {
        this.totalAtomicSkills = totalAtomicSkills;
    }

    public int getProcessedCareerSkills() {
        return processedCareerSkills;
    }

    public void setProcessedCareerSkills(int processedCareerSkills) {
        this.processedCareerSkills = processedCareerSkills;
    }

    public int getSuccessfulMappings() {
        return successfulMappings;
    }

    public void setSuccessfulMappings(int successfulMappings) {
        this.successfulMappings = successfulMappings;
    }

    public int getFailedMappings() {
        return failedMappings;
    }

    public void setFailedMappings(int failedMappings) {
        this.failedMappings = failedMappings;
    }

    public int getCleanedMappings() {
        return cleanedMappings;
    }

    public void setCleanedMappings(int cleanedMappings) {
        this.cleanedMappings = cleanedMappings;
    }

    public int getTotalMappings() {
        return totalMappings;
    }

    public void setTotalMappings(int totalMappings) {
        this.totalMappings = totalMappings;
    }

    public int getHighConfidenceMappings() {
        return highConfidenceMappings;
    }

    public void setHighConfidenceMappings(int highConfidenceMappings) {
        this.highConfidenceMappings = highConfidenceMappings;
    }

    public int getMediumConfidenceMappings() {
        return mediumConfidenceMappings;
    }

    public void setMediumConfidenceMappings(int mediumConfidenceMappings) {
        this.mediumConfidenceMappings = mediumConfidenceMappings;
    }

    public int getLowConfidenceMappings() {
        return lowConfidenceMappings;
    }

    public void setLowConfidenceMappings(int lowConfidenceMappings) {
        this.lowConfidenceMappings = lowConfidenceMappings;
    }

    public double getAverageConfidence() {
        return averageConfidence;
    }

    public void setAverageConfidence(double averageConfidence) {
        this.averageConfidence = averageConfidence;
    }

    public Map<CareerSkillMapping.Importance, Long> getImportanceStatistics() {
        return importanceStatistics;
    }

    public void setImportanceStatistics(Map<CareerSkillMapping.Importance, Long> importanceStatistics) {
        this.importanceStatistics = importanceStatistics;
    }

    public Map<CareerSkillMapping.RequiredMasteryLevel, Long> getMasteryLevelStatistics() {
        return masteryLevelStatistics;
    }

    public void setMasteryLevelStatistics(Map<CareerSkillMapping.RequiredMasteryLevel, Long> masteryLevelStatistics) {
        this.masteryLevelStatistics = masteryLevelStatistics;
    }

    /**
     * 计算执行时长（毫秒）
     */
    public long getExecutionTimeMs() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, endTime).toMillis();
    }

    /**
     * 获取格式化的执行时间
     */
    public String getFormattedExecutionTime() {
        long ms = getExecutionTimeMs();
        if (ms < 1000) {
            return ms + "ms";
        } else if (ms < 60000) {
            return String.format("%.2fs", ms / 1000.0);
        } else {
            long minutes = ms / 60000;
            long seconds = (ms % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (processedCareerSkills == 0) {
            return 0.0;
        }
        return (double) successfulMappings / processedCareerSkills * 100;
    }

    /**
     * 计算覆盖率
     */
    public double getCoverageRate() {
        if (totalCareerSkills == 0) {
            return 0.0;
        }
        return (double) successfulMappings / totalCareerSkills * 100;
    }

    /**
     * 计算映射质量分数
     */
    public double getQualityScore() {
        if (totalMappings == 0) {
            return 0.0;
        }
        
        double highWeight = 1.0;
        double mediumWeight = 0.7;
        double lowWeight = 0.4;
        
        double weightedScore = (highConfidenceMappings * highWeight + 
                               mediumConfidenceMappings * mediumWeight + 
                               lowConfidenceMappings * lowWeight) / totalMappings;
        
        return weightedScore * 100;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 获取结果摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("映射关系建立结果摘要:\n");
        summary.append("状态: ").append(status).append("\n");
        
        if (startTime != null) {
            summary.append("开始时间: ").append(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        }
        
        if (endTime != null) {
            summary.append("结束时间: ").append(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            summary.append("执行时长: ").append(getFormattedExecutionTime()).append("\n");
        }
        
        summary.append("职业技能总数: ").append(totalCareerSkills).append("\n");
        summary.append("原子技能总数: ").append(totalAtomicSkills).append("\n");
        summary.append("处理成功: ").append(successfulMappings).append("/").append(processedCareerSkills).append("\n");
        summary.append("创建映射: ").append(totalMappings).append("个\n");
        summary.append("成功率: ").append(String.format("%.1f%%", getSuccessRate())).append("\n");
        summary.append("覆盖率: ").append(String.format("%.1f%%", getCoverageRate())).append("\n");
        summary.append("平均置信度: ").append(String.format("%.3f", averageConfidence)).append("\n");
        summary.append("质量分数: ").append(String.format("%.1f", getQualityScore())).append("\n");
        
        summary.append("置信度分布:\n");
        summary.append("  高置信度(>=0.8): ").append(highConfidenceMappings).append("个\n");
        summary.append("  中等置信度(0.6-0.8): ").append(mediumConfidenceMappings).append("个\n");
        summary.append("  低置信度(0.4-0.6): ").append(lowConfidenceMappings).append("个\n");
        
        if (errorMessage != null) {
            summary.append("错误信息: ").append(errorMessage).append("\n");
        }
        
        return summary.toString();
    }

    @Override
    public String toString() {
        return "MappingRelationshipResult{" +
                "status='" + status + '\'' +
                ", totalCareerSkills=" + totalCareerSkills +
                ", totalAtomicSkills=" + totalAtomicSkills +
                ", totalMappings=" + totalMappings +
                ", successRate=" + String.format("%.1f%%", getSuccessRate()) +
                ", coverageRate=" + String.format("%.1f%%", getCoverageRate()) +
                ", averageConfidence=" + String.format("%.3f", averageConfidence) +
                ", qualityScore=" + String.format("%.1f", getQualityScore()) +
                ", executionTime='" + getFormattedExecutionTime() + '\'' +
                '}';
    }
}
