package com.itbook.dto;

import java.util.List;
import java.util.Map;

/**
 * 技能掌握度分析DTO
 */
public class SkillMasteryAnalysisDTO {
    private Long userId;
    private String userName;
    private OverallAnalysis overall;
    private List<CategoryAnalysis> categoryAnalyses;
    private List<SkillGapAnalysis> skillGaps;
    private List<RecommendationDTO> recommendations;
    private Map<String, Object> metadata;

    public SkillMasteryAnalysisDTO() {}

    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getUserName() { return userName; }
    public void setUserName(String userName) { this.userName = userName; }

    public OverallAnalysis getOverall() { return overall; }
    public void setOverall(OverallAnalysis overall) { this.overall = overall; }

    public List<CategoryAnalysis> getCategoryAnalyses() { return categoryAnalyses; }
    public void setCategoryAnalyses(List<CategoryAnalysis> categoryAnalyses) { this.categoryAnalyses = categoryAnalyses; }

    public List<SkillGapAnalysis> getSkillGaps() { return skillGaps; }
    public void setSkillGaps(List<SkillGapAnalysis> skillGaps) { this.skillGaps = skillGaps; }

    public List<RecommendationDTO> getRecommendations() { return recommendations; }
    public void setRecommendations(List<RecommendationDTO> recommendations) { this.recommendations = recommendations; }

    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }

    /**
     * 整体分析
     */
    public static class OverallAnalysis {
        private Integer totalSkills;
        private Integer masteredSkills;
        private Double averageMasteryScore;
        private Double totalLearningHours;
        private String strongestCategory;
        private String weakestCategory;

        // Getters and Setters
        public Integer getTotalSkills() { return totalSkills; }
        public void setTotalSkills(Integer totalSkills) { this.totalSkills = totalSkills; }

        public Integer getMasteredSkills() { return masteredSkills; }
        public void setMasteredSkills(Integer masteredSkills) { this.masteredSkills = masteredSkills; }

        public Double getAverageMasteryScore() { return averageMasteryScore; }
        public void setAverageMasteryScore(Double averageMasteryScore) { this.averageMasteryScore = averageMasteryScore; }

        public Double getTotalLearningHours() { return totalLearningHours; }
        public void setTotalLearningHours(Double totalLearningHours) { this.totalLearningHours = totalLearningHours; }

        public String getStrongestCategory() { return strongestCategory; }
        public void setStrongestCategory(String strongestCategory) { this.strongestCategory = strongestCategory; }

        public String getWeakestCategory() { return weakestCategory; }
        public void setWeakestCategory(String weakestCategory) { this.weakestCategory = weakestCategory; }
    }

    /**
     * 分类分析
     */
    public static class CategoryAnalysis {
        private String category;
        private Integer skillCount;
        private Double averageScore;
        private String masteryLevel;
        private Double completionRate;

        // Getters and Setters
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public Integer getSkillCount() { return skillCount; }
        public void setSkillCount(Integer skillCount) { this.skillCount = skillCount; }

        public Double getAverageScore() { return averageScore; }
        public void setAverageScore(Double averageScore) { this.averageScore = averageScore; }

        public String getMasteryLevel() { return masteryLevel; }
        public void setMasteryLevel(String masteryLevel) { this.masteryLevel = masteryLevel; }

        public Double getCompletionRate() { return completionRate; }
        public void setCompletionRate(Double completionRate) { this.completionRate = completionRate; }
    }

    /**
     * 技能差距分析
     */
    public static class SkillGapAnalysis {
        private Long skillId;
        private String skillName;
        private String category;
        private String currentLevel;
        private String targetLevel;
        private Double gapScore;
        private String priority;

        // Getters and Setters
        public Long getSkillId() { return skillId; }
        public void setSkillId(Long skillId) { this.skillId = skillId; }

        public String getSkillName() { return skillName; }
        public void setSkillName(String skillName) { this.skillName = skillName; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public String getCurrentLevel() { return currentLevel; }
        public void setCurrentLevel(String currentLevel) { this.currentLevel = currentLevel; }

        public String getTargetLevel() { return targetLevel; }
        public void setTargetLevel(String targetLevel) { this.targetLevel = targetLevel; }

        public Double getGapScore() { return gapScore; }
        public void setGapScore(Double gapScore) { this.gapScore = gapScore; }

        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }
    }

    /**
     * 推荐DTO
     */
    public static class RecommendationDTO {
        private String type;
        private String title;
        private String description;
        private String priority;
        private Map<String, Object> details;

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }

        public Map<String, Object> getDetails() { return details; }
        public void setDetails(Map<String, Object> details) { this.details = details; }
    }
}
