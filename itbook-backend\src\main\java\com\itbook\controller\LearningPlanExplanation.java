package com.itbook.controller;

/*
import com.itbook.common.ApiResponse;
import com.itbook.service.PersonalizedLearningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 个性化学习控制器
 * 提供基于用户画像的个性化学习建议API
 *
 * <AUTHOR> Team
 * @since 2025-07-22
 */
/*
//@RestController
//@RequestMapping("/personalized-learning")
//@RequiredArgsConstructor
//@Slf4j
//@Tag(name = "个性化学习", description = "基于用户画像的个性化学习建议功能")
public class PersonalizedLearningController {

    private final PersonalizedLearningService personalizedLearningService;

    /**
     * 获取用户个性化学习计划
     */
    @GetMapping("/users/{userId}")
    @Operation(summary = "获取个性化学习计划", description = "基于用户学习历史、技能掌握情况和职业目标生成个性化学习计划")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> 
            getPersonalizedLearningPlan(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户个性化学习计划: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (IllegalArgumentException e) {
            log.warn("获取个性化学习计划失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取个性化学习计划时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取个性化学习计划失败"));
        }
    }

    /**
     * 获取用户画像分析
     */
    @GetMapping("/users/{userId}/profile")
    @Operation(summary = "获取用户画像", description = "分析用户的学习偏好、技能掌握情况等画像信息")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.UserProfile>> 
            getUserProfile(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户画像: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan.getUserProfile()));
        } catch (IllegalArgumentException e) {
            log.warn("获取用户画像失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取用户画像时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取用户画像失败"));
        }
    }

    /**
     * 获取建议的学习路径
     */
    @GetMapping("/users/{userId}/paths")
    @Operation(summary = "获取建议路径", description = "获取为用户建议的学习路径列表")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedLearningService.SuggestedPath>>> 
            getSuggestedPaths(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取建议学习路径: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan.getSuggestedPaths()));
        } catch (IllegalArgumentException e) {
            log.warn("获取建议路径失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取建议路径时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取建议路径失败"));
        }
    }

    /**
     * 获取建议的技能
     */
    @GetMapping("/users/{userId}/skills")
    @Operation(summary = "获取建议技能", description = "获取为用户建议的技能列表")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedLearningService.SuggestedSkill>>> 
            getSuggestedSkills(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取建议技能: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan.getSuggestedSkills()));
        } catch (IllegalArgumentException e) {
            log.warn("获取建议技能失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取建议技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取建议技能失败"));
        }
    }

    /**
     * 获取学习建议
     */
    @GetMapping("/users/{userId}/advice")
    @Operation(summary = "获取学习建议", description = "获取为用户生成的个性化学习建议")
    public ResponseEntity<ApiResponse<java.util.List<PersonalizedLearningService.LearningAdvice>>> 
            getLearningAdvice(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取学习建议: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan.getLearningAdvices()));
        } catch (IllegalArgumentException e) {
            log.warn("获取学习建议失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取学习建议时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取学习建议失败"));
        }
    }

    /**
     * 刷新用户学习计划
     */
    @PostMapping("/users/{userId}/refresh")
    @Operation(summary = "刷新学习计划", description = "重新生成用户的个性化学习计划")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> 
            refreshLearningPlan(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("刷新用户学习计划: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (IllegalArgumentException e) {
            log.warn("刷新学习计划失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("刷新学习计划时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("刷新学习计划失败"));
        }
    }

    /**
     * 获取学习计划解释
     */
    @GetMapping("/users/{userId}/explanation")
    @Operation(summary = "获取学习计划解释", description = "获取个性化学习算法的解释说明")
    public ResponseEntity<ApiResponse<LearningPlanExplanation>> 
            getLearningPlanExplanation(@Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取学习计划解释: userId={}", userId);
        
        try {
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                    personalizedLearningService.generatePersonalizedLearningPlan(userId);
            
            LearningPlanExplanation explanation = new LearningPlanExplanation();
            explanation.setUserId(userId);
            explanation.setAlgorithmDescription("基于用户学习历史、技能掌握情况和职业目标的个性化学习算法");
            explanation.setFactorsConsidered(java.util.Arrays.asList(
                    "用户技能掌握情况", "职业目标匹配度", "学习历史偏好", "技能关系图谱", "学习难度适配"));
            explanation.setSuggestionCount(plan.getSuggestedSkills().size());
            explanation.setConfidenceScore(calculateConfidenceScore(plan));
            
            return ResponseEntity.ok(ApiResponse.success(explanation));
        } catch (IllegalArgumentException e) {
            log.warn("获取学习计划解释失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("获取学习计划解释时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取学习计划解释失败"));
        }
    }

    /**
     * 计算学习计划置信度
     */
    private double calculateConfidenceScore(PersonalizedLearningService.PersonalizedLearningPlan plan) {
        // 基于用户数据完整性计算置信度
        double score = 0.0;
        
        // 职业目标数据
        if (!plan.getUserProfile().getCareerGoals().isEmpty()) {
            score += 0.3;
        }
        
        // 技能掌握数据
        if (!plan.getUserProfile().getSkillMasteries().isEmpty()) {
            score += 0.4;
        }
        
        // 学习历史数据
        if (!plan.getUserProfile().getLearningProgress().isEmpty()) {
            score += 0.3;
        }
        
        return Math.min(score, 1.0);
    }

    // 内部数据结构
    public static class LearningPlanExplanation {
        private Long userId;
        private String algorithmDescription;
        private java.util.List<String> factorsConsidered;
        private int suggestionCount;
        private double confidenceScore;
        
        // getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getAlgorithmDescription() { return algorithmDescription; }
        public void setAlgorithmDescription(String algorithmDescription) { this.algorithmDescription = algorithmDescription; }
        
        public java.util.List<String> getFactorsConsidered() { return factorsConsidered; }
        public void setFactorsConsidered(java.util.List<String> factorsConsidered) { this.factorsConsidered = factorsConsidered; }
        
        public int getSuggestionCount() { return suggestionCount; }
        public void setSuggestionCount(int suggestionCount) { this.suggestionCount = suggestionCount; }
        
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
    }
}
