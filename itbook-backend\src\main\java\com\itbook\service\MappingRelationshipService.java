package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.dto.MappingRelationshipResult;
import com.itbook.common.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 映射关系建立服务
 * 负责建立career_skill与atomic_skill之间的映射关系
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class MappingRelationshipService {

    private static final Logger logger = LoggerFactory.getLogger(MappingRelationshipService.class);

    @Autowired
    private CareerSkillRepository careerSkillRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private CareerSkillMappingRepository mappingRepository;

    @Autowired
    private TextSimilarityService textSimilarityService;

    @Autowired
    private CareerSkillMappingService careerSkillMappingService;

    // 映射阈值配置
    private static final double HIGH_CONFIDENCE_THRESHOLD = 0.8;
    private static final double MEDIUM_CONFIDENCE_THRESHOLD = 0.6;
    private static final double LOW_CONFIDENCE_THRESHOLD = 0.4;

    /**
     * 建立所有职业技能的映射关系
     */
    public ApiResponse<MappingRelationshipResult> establishAllMappingRelationships() {
        MappingRelationshipResult result = new MappingRelationshipResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            logger.info("开始建立职业技能映射关系...");
            
            // 获取所有职业技能
            List<CareerSkill> careerSkills = careerSkillRepository.findAll();
            result.setTotalCareerSkills(careerSkills.size());
            
            // 获取所有原子技能
            List<AtomicSkill> atomicSkills = atomicSkillRepository.findAll();
            result.setTotalAtomicSkills(atomicSkills.size());
            
            if (careerSkills.isEmpty()) {
                return ApiResponse.error("没有找到职业技能数据");
            }
            
            if (atomicSkills.isEmpty()) {
                return ApiResponse.error("没有找到原子技能数据");
            }
            
            // 清理现有映射关系（可选）
            int existingMappings = cleanExistingMappings();
            result.setCleanedMappings(existingMappings);
            
            // 为每个职业技能建立映射关系
            List<CareerSkillMapping> allMappings = new ArrayList<>();
            int processedCount = 0;
            int successCount = 0;
            int failedCount = 0;
            
            for (CareerSkill careerSkill : careerSkills) {
                try {
                    List<CareerSkillMapping> mappings = createMappingsForCareerSkill(careerSkill, atomicSkills);
                    allMappings.addAll(mappings);
                    successCount++;
                    
                    logger.debug("为职业技能 {} 创建了 {} 个映射关系", 
                               careerSkill.getSkillName(), mappings.size());
                    
                } catch (Exception e) {
                    logger.error("为职业技能 {} 创建映射关系失败", careerSkill.getSkillName(), e);
                    failedCount++;
                }
                
                processedCount++;
                
                // 每处理10个技能记录一次进度
                if (processedCount % 10 == 0) {
                    logger.info("映射关系建立进度: {}/{}", processedCount, careerSkills.size());
                }
            }
            
            // 批量保存映射关系
            List<CareerSkillMapping> savedMappings = mappingRepository.saveAll(allMappings);
            
            // 计算统计信息
            calculateMappingStatistics(result, savedMappings);
            
            result.setProcessedCareerSkills(processedCount);
            result.setSuccessfulMappings(successCount);
            result.setFailedMappings(failedCount);
            result.setTotalMappings(savedMappings.size());
            result.setEndTime(LocalDateTime.now());
            result.setStatus("SUCCESS");
            result.setMessage("映射关系建立成功");
            
            logger.info("映射关系建立完成，共创建 {} 个映射关系", savedMappings.size());
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            logger.error("建立映射关系失败", e);
            result.setEndTime(LocalDateTime.now());
            result.setStatus("FAILED");
            result.setErrorMessage(e.getMessage());
            
            return ApiResponse.error("建立映射关系失败: " + e.getMessage(), result);
        }
    }

    /**
     * 为单个职业技能创建映射关系
     */
    private List<CareerSkillMapping> createMappingsForCareerSkill(CareerSkill careerSkill, List<AtomicSkill> atomicSkills) {
        List<CareerSkillMapping> mappings = new ArrayList<>();
        
        // 构建职业技能的文本描述
        String careerSkillText = buildCareerSkillText(careerSkill);
        
        // 构建原子技能的文本描述列表
        List<String> atomicSkillTexts = atomicSkills.stream()
                .map(this::buildAtomicSkillText)
                .collect(Collectors.toList());
        
        // 计算相似度
        List<TextSimilarityService.SimilarityResult> similarityResults = 
                textSimilarityService.calculateSimilarities(careerSkillText, atomicSkillTexts);
        
        // 筛选符合阈值的映射关系
        for (TextSimilarityService.SimilarityResult result : similarityResults) {
            if (result.getSimilarity() >= LOW_CONFIDENCE_THRESHOLD) {
                AtomicSkill atomicSkill = atomicSkills.get(result.getIndex());
                CareerSkillMapping mapping = createMapping(careerSkill, atomicSkill, result.getSimilarity());
                mappings.add(mapping);
            }
        }
        
        // 如果没有找到任何映射，创建一个低置信度的最佳匹配
        if (mappings.isEmpty() && !similarityResults.isEmpty()) {
            TextSimilarityService.SimilarityResult bestMatch = similarityResults.get(0);
            AtomicSkill atomicSkill = atomicSkills.get(bestMatch.getIndex());
            CareerSkillMapping mapping = createMapping(careerSkill, atomicSkill, bestMatch.getSimilarity());
            mapping.setMappingReason("无高置信度匹配，选择最佳匹配");
            mappings.add(mapping);
        }
        
        return mappings;
    }

    /**
     * 构建职业技能的文本描述
     */
    private String buildCareerSkillText(CareerSkill careerSkill) {
        StringBuilder text = new StringBuilder();
        text.append(careerSkill.getSkillName());
        
        if (careerSkill.getSkillCategory() != null) {
            text.append(" ").append(careerSkill.getSkillCategory());
        }
        
        if (careerSkill.getDescription() != null) {
            text.append(" ").append(careerSkill.getDescription());
        }
        
        return text.toString();
    }

    /**
     * 构建原子技能的文本描述
     */
    private String buildAtomicSkillText(AtomicSkill atomicSkill) {
        StringBuilder text = new StringBuilder();
        text.append(atomicSkill.getName());
        
        if (atomicSkill.getCategory() != null) {
            text.append(" ").append(atomicSkill.getCategory());
        }
        
        if (atomicSkill.getSubcategory() != null) {
            text.append(" ").append(atomicSkill.getSubcategory());
        }
        
        if (atomicSkill.getDescription() != null) {
            text.append(" ").append(atomicSkill.getDescription());
        }
        
        if (atomicSkill.getKeywords() != null) {
            text.append(" ").append(atomicSkill.getKeywords());
        }
        
        return text.toString();
    }

    /**
     * 创建映射关系
     */
    private CareerSkillMapping createMapping(CareerSkill careerSkill, AtomicSkill atomicSkill, double similarity) {
        CareerSkillMapping mapping = new CareerSkillMapping();
        
        mapping.setCareerSkillId(careerSkill.getId());
        mapping.setAtomicSkillId(atomicSkill.getId());
        mapping.setWeight(BigDecimal.valueOf(similarity).setScale(4, RoundingMode.HALF_UP));
        mapping.setConfidenceScore(BigDecimal.valueOf(similarity).setScale(4, RoundingMode.HALF_UP));
        
        // 根据相似度设置重要程度
        mapping.setImportance(determineImportance(similarity, careerSkill.getImportance()));
        
        // 映射要求掌握水平
        mapping.setRequiredMasteryLevel(mapRequiredMasteryLevel(careerSkill.getTargetLevel()));
        
        // 设置映射来源和原因
        mapping.setMappingSource(CareerSkillMapping.MappingSource.AUTO_GENERATED);
        mapping.setMappingReason(String.format("基于文本相似度自动生成，相似度: %.4f", similarity));
        
        return mapping;
    }

    /**
     * 根据相似度和原始重要程度确定映射重要程度
     */
    private CareerSkillMapping.Importance determineImportance(double similarity, String originalImportance) {
        // 如果相似度很高，保持原始重要程度
        if (similarity >= HIGH_CONFIDENCE_THRESHOLD) {
            return mapOriginalImportance(originalImportance);
        }
        
        // 如果相似度中等，降低一级重要程度
        if (similarity >= MEDIUM_CONFIDENCE_THRESHOLD) {
            CareerSkillMapping.Importance original = mapOriginalImportance(originalImportance);
            return lowerImportance(original);
        }
        
        // 如果相似度较低，设为nice-to-have
        return CareerSkillMapping.Importance.NICE_TO_HAVE;
    }

    /**
     * 映射原始重要程度
     */
    private CareerSkillMapping.Importance mapOriginalImportance(String originalImportance) {
        if (originalImportance == null) {
            return CareerSkillMapping.Importance.IMPORTANT;
        }
        
        switch (originalImportance.toLowerCase()) {
            case "critical":
                return CareerSkillMapping.Importance.CRITICAL;
            case "important":
                return CareerSkillMapping.Importance.IMPORTANT;
            case "nice-to-have":
                return CareerSkillMapping.Importance.NICE_TO_HAVE;
            default:
                return CareerSkillMapping.Importance.IMPORTANT;
        }
    }

    /**
     * 降低重要程度
     */
    private CareerSkillMapping.Importance lowerImportance(CareerSkillMapping.Importance importance) {
        switch (importance) {
            case CRITICAL:
                return CareerSkillMapping.Importance.IMPORTANT;
            case IMPORTANT:
                return CareerSkillMapping.Importance.NICE_TO_HAVE;
            case NICE_TO_HAVE:
                return CareerSkillMapping.Importance.NICE_TO_HAVE;
            default:
                return CareerSkillMapping.Importance.NICE_TO_HAVE;
        }
    }

    /**
     * 映射要求掌握水平
     */
    private CareerSkillMapping.RequiredMasteryLevel mapRequiredMasteryLevel(String targetLevel) {
        if (targetLevel == null) {
            return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
        }
        
        switch (targetLevel.toLowerCase()) {
            case "beginner":
                return CareerSkillMapping.RequiredMasteryLevel.BASIC;
            case "intermediate":
                return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
            case "advanced":
                return CareerSkillMapping.RequiredMasteryLevel.ADVANCED;
            case "expert":
                return CareerSkillMapping.RequiredMasteryLevel.EXPERT;
            default:
                return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
        }
    }

    /**
     * 清理现有映射关系
     */
    private int cleanExistingMappings() {
        try {
            List<CareerSkillMapping> existingMappings = mappingRepository.findAll();
            int count = existingMappings.size();
            
            if (count > 0) {
                logger.info("清理现有的 {} 个映射关系", count);
                mappingRepository.deleteAll();
            }
            
            return count;
        } catch (Exception e) {
            logger.error("清理现有映射关系失败", e);
            return 0;
        }
    }

    /**
     * 计算映射统计信息
     */
    private void calculateMappingStatistics(MappingRelationshipResult result, List<CareerSkillMapping> mappings) {
        if (mappings.isEmpty()) {
            return;
        }
        
        // 按置信度分类统计
        long highConfidenceCount = mappings.stream()
                .mapToLong(m -> m.getConfidenceScore().doubleValue() >= HIGH_CONFIDENCE_THRESHOLD ? 1 : 0)
                .sum();
        
        long mediumConfidenceCount = mappings.stream()
                .mapToLong(m -> {
                    double confidence = m.getConfidenceScore().doubleValue();
                    return confidence >= MEDIUM_CONFIDENCE_THRESHOLD && confidence < HIGH_CONFIDENCE_THRESHOLD ? 1 : 0;
                })
                .sum();
        
        long lowConfidenceCount = mappings.stream()
                .mapToLong(m -> {
                    double confidence = m.getConfidenceScore().doubleValue();
                    return confidence >= LOW_CONFIDENCE_THRESHOLD && confidence < MEDIUM_CONFIDENCE_THRESHOLD ? 1 : 0;
                })
                .sum();
        
        result.setHighConfidenceMappings((int) highConfidenceCount);
        result.setMediumConfidenceMappings((int) mediumConfidenceCount);
        result.setLowConfidenceMappings((int) lowConfidenceCount);
        
        // 计算平均置信度
        double avgConfidence = mappings.stream()
                .mapToDouble(m -> m.getConfidenceScore().doubleValue())
                .average()
                .orElse(0.0);
        result.setAverageConfidence(avgConfidence);
        
        // 按重要程度统计
        Map<CareerSkillMapping.Importance, Long> importanceStats = mappings.stream()
                .collect(Collectors.groupingBy(
                        CareerSkillMapping::getImportance,
                        Collectors.counting()
                ));
        result.setImportanceStatistics(importanceStats);
        
        // 按要求掌握水平统计
        Map<CareerSkillMapping.RequiredMasteryLevel, Long> masteryStats = mappings.stream()
                .collect(Collectors.groupingBy(
                        CareerSkillMapping::getRequiredMasteryLevel,
                        Collectors.counting()
                ));
        result.setMasteryLevelStatistics(masteryStats);
    }

    /**
     * 验证映射关系质量
     */
    public ApiResponse<Map<String, Object>> validateMappingQuality() {
        try {
            List<CareerSkillMapping> allMappings = mappingRepository.findAll();
            
            if (allMappings.isEmpty()) {
                return ApiResponse.error("没有找到映射关系数据");
            }
            
            Map<String, Object> validation = new HashMap<>();
            
            // 覆盖率验证
            long totalCareerSkills = careerSkillRepository.count();
            long mappedCareerSkills = allMappings.stream()
                    .map(CareerSkillMapping::getCareerSkillId)
                    .distinct()
                    .count();
            double coverageRate = totalCareerSkills > 0 ? (double) mappedCareerSkills / totalCareerSkills : 0.0;
            
            validation.put("totalCareerSkills", totalCareerSkills);
            validation.put("mappedCareerSkills", mappedCareerSkills);
            validation.put("coverageRate", coverageRate);
            
            // 质量分布
            long highQuality = allMappings.stream()
                    .mapToLong(m -> m.getConfidenceScore().doubleValue() >= HIGH_CONFIDENCE_THRESHOLD ? 1 : 0)
                    .sum();
            long mediumQuality = allMappings.stream()
                    .mapToLong(m -> {
                        double confidence = m.getConfidenceScore().doubleValue();
                        return confidence >= MEDIUM_CONFIDENCE_THRESHOLD && confidence < HIGH_CONFIDENCE_THRESHOLD ? 1 : 0;
                    })
                    .sum();
            long lowQuality = allMappings.size() - highQuality - mediumQuality;
            
            validation.put("highQualityMappings", highQuality);
            validation.put("mediumQualityMappings", mediumQuality);
            validation.put("lowQualityMappings", lowQuality);
            validation.put("qualityScore", calculateQualityScore(highQuality, mediumQuality, lowQuality));
            
            // 平均置信度
            double avgConfidence = allMappings.stream()
                    .mapToDouble(m -> m.getConfidenceScore().doubleValue())
                    .average()
                    .orElse(0.0);
            validation.put("averageConfidence", avgConfidence);
            
            validation.put("isValid", coverageRate >= 0.8 && avgConfidence >= 0.5);
            validation.put("validationTime", LocalDateTime.now());
            
            return ApiResponse.success(validation);
            
        } catch (Exception e) {
            return ApiResponse.error("验证映射关系质量失败: " + e.getMessage());
        }
    }

    /**
     * 计算质量分数
     */
    private double calculateQualityScore(long high, long medium, long low) {
        long total = high + medium + low;
        if (total == 0) {
            return 0.0;
        }
        
        double score = (high * 1.0 + medium * 0.7 + low * 0.4) / total;
        return score * 100;
    }

    /**
     * 获取映射关系摘要
     */
    public ApiResponse<Map<String, Object>> getMappingSummary() {
        try {
            Map<String, Object> summary = new HashMap<>();
            
            // 基础统计
            long totalMappings = mappingRepository.count();
            long totalCareerSkills = careerSkillRepository.count();
            long totalAtomicSkills = atomicSkillRepository.count();
            
            summary.put("totalMappings", totalMappings);
            summary.put("totalCareerSkills", totalCareerSkills);
            summary.put("totalAtomicSkills", totalAtomicSkills);
            
            if (totalMappings > 0) {
                // 平均置信度
                BigDecimal avgConfidence = mappingRepository.getAverageConfidenceScore();
                summary.put("averageConfidence", avgConfidence != null ? avgConfidence.doubleValue() : 0.0);
                
                // 覆盖率
                long mappedCareerSkills = mappingRepository.findAll().stream()
                        .map(CareerSkillMapping::getCareerSkillId)
                        .distinct()
                        .count();
                double coverageRate = totalCareerSkills > 0 ? (double) mappedCareerSkills / totalCareerSkills : 0.0;
                summary.put("coverageRate", coverageRate);
                
                // 状态
                summary.put("status", "COMPLETED");
                summary.put("message", "映射关系已建立");
            } else {
                summary.put("status", "NOT_STARTED");
                summary.put("message", "尚未建立映射关系");
            }
            
            summary.put("lastUpdated", LocalDateTime.now());
            
            return ApiResponse.success(summary);
            
        } catch (Exception e) {
            return ApiResponse.error("获取映射关系摘要失败: " + e.getMessage());
        }
    }
}
