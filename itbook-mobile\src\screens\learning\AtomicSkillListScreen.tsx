import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { 
  AtomicSkill, 
  SkillSearchFilter,
  DifficultyLevel,
  SkillType,
  MasteryLevel 
} from '../../types/atomicSkill';
import { AtomicSkillService } from '../../services/AtomicSkillService';
import { SkillList, SkillSearch, SkillFilter } from '../../components/atomic-skills';

type RootStackParamList = {
  AtomicSkillDetail: { skillId: number };
};

type AtomicSkillListScreenNavigationProp = StackNavigationProp<RootStackParamList>;

/**
 * 原子技能列表页面
 * 提供技能搜索、筛选和浏览功能
 */
export const AtomicSkillListScreen: React.FC = () => {
  const colors = useThemeColors();
  const navigation = useNavigation<AtomicSkillListScreenNavigationProp>();
  
  const [skills, setSkills] = useState<AtomicSkill[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [masteryData, setMasteryData] = useState<Record<number, { level: MasteryLevel; score: number }>>({});
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [currentFilter, setCurrentFilter] = useState<SkillSearchFilter>({});
  const [searchKeyword, setSearchKeyword] = useState('');

  // 加载技能列表
  const loadSkills = useCallback(async (
    page: number = 0, 
    filter: SkillSearchFilter = {}, 
    keyword: string = '',
    append: boolean = false
  ) => {
    try {
      if (!append) {
        setLoading(true);
      }
      
      let response;
      
      if (keyword.trim()) {
        // 搜索模式
        response = await AtomicSkillService.searchAtomicSkills(keyword, {
          page,
          size: 20,
        });
      } else if (filter.category) {
        // 分类筛选模式
        response = await AtomicSkillService.getSkillsByCategory(filter.category, {
          page,
          size: 20,
        });
      } else if (filter.difficultyLevel) {
        // 难度筛选模式
        response = await AtomicSkillService.getSkillsByDifficulty(filter.difficultyLevel, {
          page,
          size: 20,
        });
      } else if (filter.skillType) {
        // 类型筛选模式
        response = await AtomicSkillService.getSkillsByType(filter.skillType, {
          page,
          size: 20,
        });
      } else {
        // 默认分页查询
        response = await AtomicSkillService.getAtomicSkills({
          page,
          size: 20,
          ...filter,
        });
      }
      
      const newSkills = response.content;
      
      if (append) {
        setSkills(prev => [...prev, ...newSkills]);
      } else {
        setSkills(newSkills);
      }
      
      setHasMore(response.number < response.totalPages - 1);
      setCurrentPage(response.number);
      
    } catch (error) {
      console.error('Failed to load skills:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 加载技能分类
  const loadCategories = useCallback(async () => {
    try {
      const categoriesData = await AtomicSkillService.getSkillCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    loadSkills();
    loadCategories();
  }, [loadSkills, loadCategories]);

  // 处理搜索
  const handleSearch = useCallback((filter: SkillSearchFilter) => {
    const keyword = filter.keyword || '';
    setSearchKeyword(keyword);
    setCurrentFilter(filter);
    loadSkills(0, filter, keyword, false);
  }, [loadSkills]);

  // 处理清除搜索
  const handleClearSearch = useCallback(() => {
    setSearchKeyword('');
    setCurrentFilter({});
    loadSkills(0, {}, '', false);
  }, [loadSkills]);

  // 处理筛选变更
  const handleFilterChange = useCallback((filter: SkillSearchFilter) => {
    setCurrentFilter(filter);
    loadSkills(0, filter, searchKeyword, false);
  }, [loadSkills, searchKeyword]);

  // 处理下拉刷新
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadSkills(0, currentFilter, searchKeyword, false);
  }, [loadSkills, currentFilter, searchKeyword]);

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (hasMore && !loading) {
      loadSkills(currentPage + 1, currentFilter, searchKeyword, true);
    }
  }, [hasMore, loading, currentPage, currentFilter, searchKeyword, loadSkills]);

  // 处理技能点击
  const handleSkillPress = useCallback((skill: AtomicSkill) => {
    navigation.navigate('AtomicSkillDetail', { skillId: skill.id });
  }, [navigation]);

  // 处理掌握度点击
  const handleMasteryPress = useCallback((skill: AtomicSkill) => {
    // TODO: 导航到技能评估或掌握度管理页面
    console.log('Mastery press for skill:', skill.id);
  }, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 搜索组件 */}
      <SkillSearch
        onSearch={handleSearch}
        onClear={handleClearSearch}
        placeholder="搜索技能名称、关键词..."
        showAdvancedFilter={true}
        initialFilter={currentFilter}
        categories={categories}
      />

      {/* 快速筛选组件 */}
      <SkillFilter
        categories={categories}
        selectedFilter={currentFilter}
        onFilterChange={handleFilterChange}
      />

      {/* 技能列表 */}
      <SkillList
        skills={skills}
        masteryData={masteryData}
        loading={loading}
        refreshing={refreshing}
        hasMore={hasMore}
        showProgress={true}
        showPrerequisites={false}
        emptyMessage="暂无技能数据"
        emptyIcon="library-outline"
        onRefresh={handleRefresh}
        onLoadMore={handleLoadMore}
        onSkillPress={handleSkillPress}
        onMasteryPress={handleMasteryPress}
        style={styles.skillList}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  skillList: {
    flex: 1,
  },
});
