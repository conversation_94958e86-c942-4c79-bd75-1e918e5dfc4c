package com.itbook.migration;

/**
 * 数据迁移状态枚举
 * 定义迁移过程中的各种状态
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
public enum MigrationStatus {

    /**
     * 未开始
     */
    NOT_STARTED("未开始", "迁移尚未开始"),

    /**
     * 进行中
     */
    IN_PROGRESS("进行中", "迁移正在执行中"),

    /**
     * 备份阶段
     */
    BACKING_UP("备份中", "正在备份原始数据"),

    /**
     * 清洗阶段
     */
    CLEANING("清洗中", "正在清洗和标准化数据"),

    /**
     * 映射阶段
     */
    MAPPING("映射中", "正在生成原子技能和映射关系"),

    /**
     * 验证阶段
     */
    VALIDATING("验证中", "正在验证迁移结果"),

    /**
     * 成功完成
     */
    SUCCESS("成功", "迁移成功完成"),

    /**
     * 部分成功
     */
    PARTIAL("部分成功", "迁移部分完成，存在一些问题"),

    /**
     * 失败
     */
    FAILED("失败", "迁移执行失败"),

    /**
     * 回滚中
     */
    ROLLING_BACK("回滚中", "正在回滚到迁移前状态"),

    /**
     * 已回滚
     */
    ROLLED_BACK("已回滚", "已回滚到迁移前状态"),

    /**
     * 未知状态
     */
    UNKNOWN("未知", "无法确定迁移状态");

    private final String name;
    private final String description;

    MigrationStatus(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == IN_PROGRESS || this == BACKING_UP || 
               this == CLEANING || this == MAPPING || this == VALIDATING;
    }

    /**
     * 判断是否为完成状态（成功或失败）
     */
    public boolean isCompleted() {
        return this == SUCCESS || this == PARTIAL || this == FAILED || 
               this == ROLLED_BACK;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccessful() {
        return this == SUCCESS || this == PARTIAL;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 判断是否需要回滚
     */
    public boolean needsRollback() {
        return this == FAILED;
    }

    /**
     * 获取下一个状态
     */
    public MigrationStatus getNextStatus() {
        switch (this) {
            case NOT_STARTED:
                return BACKING_UP;
            case BACKING_UP:
                return CLEANING;
            case CLEANING:
                return MAPPING;
            case MAPPING:
                return VALIDATING;
            case VALIDATING:
                return SUCCESS;
            case FAILED:
                return ROLLING_BACK;
            case ROLLING_BACK:
                return ROLLED_BACK;
            default:
                return this;
        }
    }

    /**
     * 获取进度百分比
     */
    public int getProgressPercentage() {
        switch (this) {
            case NOT_STARTED:
                return 0;
            case BACKING_UP:
                return 10;
            case CLEANING:
                return 30;
            case MAPPING:
                return 60;
            case VALIDATING:
                return 90;
            case SUCCESS:
            case PARTIAL:
                return 100;
            case FAILED:
                return -1; // 表示失败
            case ROLLING_BACK:
                return 50; // 回滚进度
            case ROLLED_BACK:
                return 0; // 回到初始状态
            default:
                return 0;
        }
    }

    /**
     * 根据名称获取状态
     */
    public static MigrationStatus fromName(String name) {
        for (MigrationStatus status : values()) {
            if (status.name.equals(name)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    @Override
    public String toString() {
        return name;
    }
}
