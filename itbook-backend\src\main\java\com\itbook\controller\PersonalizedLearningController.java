package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.PersonalizedLearningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 个性化学习控制器
 * 提供基于用户画像的个性化学习计划API
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/personalized-learning")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "个性化学习", description = "基于用户画像的个性化学习计划功能")
public class PersonalizedLearningController {

    private final PersonalizedLearningService personalizedLearningService;

    /**
     * 获取用户个性化学习计划
     */
    @GetMapping("/plan/{userId}")
    @Operation(summary = "获取个性化学习计划", description = "根据用户画像生成个性化学习计划")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> getPersonalizedLearningPlan(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取用户个性化学习计划，用户ID: {}", userId);
            PersonalizedLearningService.PersonalizedLearningPlan plan = 
                personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            log.error("获取个性化学习计划失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取个性化学习计划失败: " + e.getMessage()));
        }
    }

    /**
     * 获取技能建议
     */
    @GetMapping("/skills/suggestions/{userId}")
    @Operation(summary = "获取技能建议", description = "基于用户当前技能水平推荐需要提升的技能")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> getSkillSuggestions(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取技能建议，用户ID: {}", userId);
            PersonalizedLearningService.PersonalizedLearningPlan plan =
                personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            log.error("获取技能建议失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取技能建议失败: " + e.getMessage()));
        }
    }

    /**
     * 获取学习路径建议
     */
    @GetMapping("/paths/suggestions/{userId}")
    @Operation(summary = "获取学习路径建议", description = "基于用户职业目标推荐合适的学习路径")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> getPathSuggestions(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取学习路径建议，用户ID: {}", userId);
            PersonalizedLearningService.PersonalizedLearningPlan plan =
                personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            log.error("获取学习路径建议失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取学习路径建议失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户学习分析
     */
    @GetMapping("/analysis/{userId}")
    @Operation(summary = "获取学习分析", description = "分析用户学习行为和偏好")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.UserProfile>> getLearningAnalysis(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取学习分析，用户ID: {}", userId);
            PersonalizedLearningService.UserProfile profile =
                personalizedLearningService.buildUserProfile(userId);
            return ResponseEntity.ok(ApiResponse.success(profile));
        } catch (Exception e) {
            log.error("获取学习分析失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取学习分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户画像
     */
    @GetMapping("/profile/{userId}")
    @Operation(summary = "获取用户画像", description = "获取用户的学习画像信息")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> getUserProfile(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取用户画像，用户ID: {}", userId);
            PersonalizedLearningService.PersonalizedLearningPlan plan =
                personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            log.error("获取用户画像失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取用户画像失败: " + e.getMessage()));
        }
    }

    /**
     * 获取学习统计
     */
    @GetMapping("/stats/{userId}")
    @Operation(summary = "获取学习统计", description = "获取用户的学习统计信息")
    public ResponseEntity<ApiResponse<PersonalizedLearningService.PersonalizedLearningPlan>> getLearningStats(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            log.info("获取学习统计，用户ID: {}", userId);
            PersonalizedLearningService.PersonalizedLearningPlan plan =
                personalizedLearningService.generatePersonalizedLearningPlan(userId);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            log.error("获取学习统计失败，用户ID: {}", userId, e);
            return ResponseEntity.ok(ApiResponse.error("获取学习统计失败: " + e.getMessage()));
        }
    }
}
