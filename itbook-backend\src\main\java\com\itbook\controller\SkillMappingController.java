package com.itbook.controller;

import com.itbook.service.SkillMappingService;
import com.itbook.service.TextSimilarityService;
import com.itbook.dto.SkillMappingResult;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 技能映射控制器
 * 提供技能映射相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/skill-mapping")
@Tag(name = "技能映射管理", description = "现有技能到原子技能的智能映射API")
public class SkillMappingController {

    @Autowired
    private SkillMappingService skillMappingService;

    @Autowired
    private TextSimilarityService textSimilarityService;

    /**
     * 执行完整的技能映射流程
     */
    @PostMapping("/execute")
    @Operation(summary = "执行技能映射", description = "执行完整的技能映射流程，将现有技能转换为原子技能")
    public ApiResponse<SkillMappingResult> executeMapping() {
        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        ApiResponse<SkillMappingResult> response = skillMappingService.executeFullMapping();
        
        if (response.getCode() == 20000 && response.getData() != null) {
            SkillMappingResult result = response.getData();
            result.setStartTime(startTimeStr);
            result.setEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
        }
        
        return response;
    }

    /**
     * 从career_skill生成原子技能
     */
    @PostMapping("/generate-from-career-skills")
    @Operation(summary = "从职业技能生成原子技能", description = "基于career_skill表生成原子技能")
    public ApiResponse<Map<String, Object>> generateFromCareerSkills() {
        try {
            var atomicSkills = skillMappingService.generateAtomicSkillsFromCareerSkills();
            
            Map<String, Object> result = Map.of(
                "generatedCount", atomicSkills.size(),
                "message", "成功从career_skill生成" + atomicSkills.size() + "个原子技能",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("从career_skill生成原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 从learning_path生成原子技能
     */
    @PostMapping("/generate-from-learning-paths")
    @Operation(summary = "从学习路径生成原子技能", description = "基于learning_path的skill_tags生成原子技能")
    public ApiResponse<Map<String, Object>> generateFromLearningPaths() {
        try {
            var atomicSkills = skillMappingService.generateAtomicSkillsFromLearningPaths();
            
            Map<String, Object> result = Map.of(
                "generatedCount", atomicSkills.size(),
                "message", "成功从learning_path生成" + atomicSkills.size() + "个原子技能",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("从learning_path生成原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 从course生成原子技能
     */
    @PostMapping("/generate-from-courses")
    @Operation(summary = "从课程生成原子技能", description = "基于course的tags生成原子技能")
    public ApiResponse<Map<String, Object>> generateFromCourses() {
        try {
            var atomicSkills = skillMappingService.generateAtomicSkillsFromCourses();
            
            Map<String, Object> result = Map.of(
                "generatedCount", atomicSkills.size(),
                "message", "成功从course生成" + atomicSkills.size() + "个原子技能",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("从course生成原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 计算文本相似度
     */
    @PostMapping("/similarity")
    @Operation(summary = "计算文本相似度", description = "计算两个文本之间的相似度分数")
    public ApiResponse<Map<String, Object>> calculateSimilarity(
            @Parameter(description = "第一个文本") @RequestParam String text1,
            @Parameter(description = "第二个文本") @RequestParam String text2) {
        try {
            double similarity = textSimilarityService.calculateSimilarity(text1, text2);
            
            Map<String, Object> result = Map.of(
                "text1", text1,
                "text2", text2,
                "similarity", similarity,
                "similarityPercentage", String.format("%.2f%%", similarity * 100),
                "confidenceLevel", getConfidenceLevel(similarity)
            );
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("计算文本相似度失败: " + e.getMessage());
        }
    }

    /**
     * 批量计算相似度
     */
    @PostMapping("/similarity/batch")
    @Operation(summary = "批量计算相似度", description = "计算目标文本与候选文本列表的相似度")
    public ApiResponse<List<TextSimilarityService.SimilarityResult>> calculateBatchSimilarity(
            @Parameter(description = "目标文本") @RequestParam String targetText,
            @RequestBody List<String> candidateTexts) {
        try {
            List<TextSimilarityService.SimilarityResult> results = 
                textSimilarityService.calculateSimilarities(targetText, candidateTexts);
            
            return ApiResponse.success(results);
        } catch (Exception e) {
            return ApiResponse.error("批量计算相似度失败: " + e.getMessage());
        }
    }

    /**
     * 获取映射统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取映射统计", description = "获取技能映射的统计信息")
    public ApiResponse<Map<String, Object>> getMappingStatistics() {
        try {
            // 这里可以调用相关的统计方法
            Map<String, Object> statistics = Map.of(
                "message", "映射统计功能开发中",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取映射统计失败: " + e.getMessage());
        }
    }

    /**
     * 验证映射质量
     */
    @PostMapping("/validate")
    @Operation(summary = "验证映射质量", description = "验证技能映射的质量和准确性")
    public ApiResponse<Map<String, Object>> validateMapping() {
        try {
            // 这里可以实现映射质量验证逻辑
            Map<String, Object> validation = Map.of(
                "message", "映射验证功能开发中",
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return ApiResponse.success(validation);
        } catch (Exception e) {
            return ApiResponse.error("验证映射质量失败: " + e.getMessage());
        }
    }

    /**
     * 获取置信度级别描述
     */
    private String getConfidenceLevel(double similarity) {
        if (similarity >= 0.8) {
            return "高置信度";
        } else if (similarity >= 0.6) {
            return "中等置信度";
        } else if (similarity >= 0.4) {
            return "低置信度";
        } else {
            return "极低置信度";
        }
    }
}
