import { apiService } from './ApiService';

/**
 * 用户学习行为分析结果接口
 */
export interface LearningBehaviorAnalysis {
  userId: number;
  analysisTime: string;
  
  // 学习活跃度分析
  learningActivityScore: number; // 0-100
  totalLearningDays: number;
  continuousLearningDays: number;
  averageDailyLearningTime: number; // 小时
  
  // 学习偏好分析
  preferredLearningTime: string; // morning, afternoon, evening, night
  preferredSkillCategories: string[];
  learningPatternType: string; // consistent, burst, irregular
  
  // 技能掌握分析
  skillMasteryGrowthRate: number; // 技能掌握增长率
  strongSkillAreas: string[]; // 擅长技能领域
  weakSkillAreas: string[]; // 薄弱技能领域
  totalMasteredSkills: number;
  
  // 学习效率分析
  learningEfficiencyScore: number; // 0-100
  averageCompletionRate: number; // 平均完成率
  abandonedPathsCount: number; // 放弃的学习路径数量
  
  // 社交学习分析
  communityInteractionScore: number; // 社区互动分数
  isActiveCommunityMember: boolean;
  interactionTypes: string[]; // like, comment, share, help
  
  // 目标导向分析
  hasCareerGoal: boolean;
  careerGoalAlignment: string; // high, medium, low
  goalProgressRate: number; // 目标进度率
  
  // 学习挑战分析
  identifiedChallenges: string[]; // 识别的学习挑战
  recommendedImprovements: string[]; // 推荐改进建议
}

/**
 * 学习活跃度概览接口
 */
export interface LearningActivityOverview {
  userId: number;
  learningActivityScore: number;
  totalLearningDays: number;
  continuousLearningDays: number;
  averageDailyLearningTime: number;
  totalMasteredSkills: number;
  learningEfficiencyScore: number;
  analysisTime: string;
}

/**
 * 技能掌握分析接口
 */
export interface SkillMasteryAnalysis {
  userId: number;
  skillMasteryGrowthRate: number;
  strongSkillAreas: string[];
  weakSkillAreas: string[];
  totalMasteredSkills: number;
  preferredSkillCategories: string[];
  analysisTime: string;
}

/**
 * 学习偏好分析接口
 */
export interface LearningPreferences {
  userId: number;
  preferredLearningTime: string;
  preferredSkillCategories: string[];
  learningPatternType: string;
  averageDailyLearningTime: number;
  analysisTime: string;
}

/**
 * 学习洞察接口
 */
export interface LearningInsights {
  userId: number;
  identifiedChallenges: string[];
  recommendedImprovements: string[];
  hasCareerGoal: boolean;
  careerGoalAlignment: string;
  goalProgressRate: number;
  communityInteractionScore: number;
  isActiveCommunityMember: boolean;
  analysisTime: string;
}

/**
 * 学习效率分析接口
 */
export interface LearningEfficiency {
  userId: number;
  learningEfficiencyScore: number;
  averageCompletionRate: number;
  abandonedPathsCount: number;
  totalMasteredSkills: number;
  skillMasteryGrowthRate: number;
  analysisTime: string;
}

/**
 * 用户画像综合评分接口
 */
export interface UserProfileScore {
  userId: number;
  totalScore: number;
  level: string;
  activityScore: number;
  efficiencyScore: number;
  masteryScore: number;
  communityScore: number;
  analysisTime: string;
}

/**
 * 用户行为分析服务类
 * 提供用户学习行为分析相关的API调用功能
 */
class UserBehaviorAnalysisService {
  private readonly baseUrl = '/user-behavior-analysis';

  /**
   * 获取用户学习行为分析报告
   * @param userId 用户ID
   * @returns 完整的学习行为分析结果
   */
  async getLearningBehaviorAnalysis(userId: number): Promise<LearningBehaviorAnalysis> {
    console.log('🔍 获取用户学习行为分析:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/learning-behavior`);
      
      if (response.code === 20000) {
        console.log('✅ 用户学习行为分析获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取用户学习行为分析失败');
      }
    } catch (error) {
      console.error('❌ 获取用户学习行为分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户学习活跃度概览
   * @param userId 用户ID
   * @returns 学习活跃度概览信息
   */
  async getLearningActivityOverview(userId: number): Promise<LearningActivityOverview> {
    console.log('📊 获取用户学习活跃度概览:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/activity-overview`);
      
      if (response.code === 20000) {
        console.log('✅ 学习活跃度概览获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取学习活跃度概览失败');
      }
    } catch (error) {
      console.error('❌ 获取学习活跃度概览失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户技能掌握分析
   * @param userId 用户ID
   * @returns 技能掌握分析结果
   */
  async getSkillMasteryAnalysis(userId: number): Promise<SkillMasteryAnalysis> {
    console.log('🎯 获取用户技能掌握分析:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/skill-mastery-analysis`);
      
      if (response.code === 20000) {
        console.log('✅ 技能掌握分析获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取技能掌握分析失败');
      }
    } catch (error) {
      console.error('❌ 获取技能掌握分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户学习偏好分析
   * @param userId 用户ID
   * @returns 学习偏好分析结果
   */
  async getLearningPreferences(userId: number): Promise<LearningPreferences> {
    console.log('⚙️ 获取用户学习偏好分析:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/learning-preferences`);
      
      if (response.code === 20000) {
        console.log('✅ 学习偏好分析获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取学习偏好分析失败');
      }
    } catch (error) {
      console.error('❌ 获取学习偏好分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户学习洞察
   * @param userId 用户ID
   * @returns 学习挑战和改进建议
   */
  async getLearningInsights(userId: number): Promise<LearningInsights> {
    console.log('💡 获取用户学习洞察:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/learning-insights`);
      
      if (response.code === 20000) {
        console.log('✅ 学习洞察获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取学习洞察失败');
      }
    } catch (error) {
      console.error('❌ 获取学习洞察失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户学习效率分析
   * @param userId 用户ID
   * @returns 学习效率分析结果
   */
  async getLearningEfficiency(userId: number): Promise<LearningEfficiency> {
    console.log('⚡ 获取用户学习效率分析:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/learning-efficiency`);
      
      if (response.code === 20000) {
        console.log('✅ 学习效率分析获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取学习效率分析失败');
      }
    } catch (error) {
      console.error('❌ 获取学习效率分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户画像综合评分
   * @param userId 用户ID
   * @returns 用户画像综合评分和等级
   */
  async getUserProfileScore(userId: number): Promise<UserProfileScore> {
    console.log('🏆 获取用户画像综合评分:', userId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/${userId}/profile-score`);
      
      if (response.code === 20000) {
        console.log('✅ 用户画像综合评分获取成功');
        return response.data;
      } else {
        throw new Error(response.message || '获取用户画像综合评分失败');
      }
    } catch (error) {
      console.error('❌ 获取用户画像综合评分失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户画像完整报告
   * @param userId 用户ID
   * @returns 包含所有分析维度的完整报告
   */
  async getCompleteUserProfileReport(userId: number) {
    console.log('📋 获取用户画像完整报告:', userId);
    
    try {
      const [
        behaviorAnalysis,
        activityOverview,
        skillMastery,
        preferences,
        insights,
        efficiency,
        profileScore
      ] = await Promise.all([
        this.getLearningBehaviorAnalysis(userId),
        this.getLearningActivityOverview(userId),
        this.getSkillMasteryAnalysis(userId),
        this.getLearningPreferences(userId),
        this.getLearningInsights(userId),
        this.getLearningEfficiency(userId),
        this.getUserProfileScore(userId)
      ]);

      const completeReport = {
        userId,
        reportGeneratedAt: new Date().toISOString(),
        behaviorAnalysis,
        activityOverview,
        skillMastery,
        preferences,
        insights,
        efficiency,
        profileScore
      };

      console.log('✅ 用户画像完整报告生成成功');
      return completeReport;
      
    } catch (error) {
      console.error('❌ 获取用户画像完整报告失败:', error);
      throw error;
    }
  }

  /**
   * 获取学习时间偏好的友好显示名称
   * @param timePreference 时间偏好代码
   * @returns 友好显示名称
   */
  getTimePreferenceName(timePreference: string): string {
    const timeMap: { [key: string]: string } = {
      'morning': '上午',
      'afternoon': '下午',
      'evening': '晚上',
      'night': '深夜'
    };
    return timeMap[timePreference] || timePreference;
  }

  /**
   * 获取学习模式的友好显示名称
   * @param patternType 学习模式代码
   * @returns 友好显示名称
   */
  getLearningPatternName(patternType: string): string {
    const patternMap: { [key: string]: string } = {
      'consistent': '稳定型',
      'burst': '爆发型',
      'irregular': '不规律型'
    };
    return patternMap[patternType] || patternType;
  }

  /**
   * 获取职业目标对齐度的友好显示名称
   * @param alignment 对齐度代码
   * @returns 友好显示名称
   */
  getCareerGoalAlignmentName(alignment: string): string {
    const alignmentMap: { [key: string]: string } = {
      'high': '高度匹配',
      'medium': '中等匹配',
      'low': '较低匹配',
      'none': '未设置目标'
    };
    return alignmentMap[alignment] || alignment;
  }

  /**
   * 根据分数获取等级颜色
   * @param score 分数
   * @returns 颜色代码
   */
  getScoreColor(score: number): string {
    if (score >= 80) return '#4CAF50'; // 绿色
    if (score >= 60) return '#FF9800'; // 橙色
    if (score >= 40) return '#2196F3'; // 蓝色
    return '#757575'; // 灰色
  }

  /**
   * 根据等级获取等级图标
   * @param level 等级名称
   * @returns 图标名称
   */
  getLevelIcon(level: string): string {
    const iconMap: { [key: string]: string } = {
      '优秀学习者': 'trophy-outline',
      '积极学习者': 'trending-up-outline',
      '普通学习者': 'person-outline',
      '新手学习者': 'school-outline'
    };
    return iconMap[level] || 'person-outline';
  }
}

// 导出服务实例
export const userBehaviorAnalysisService = new UserBehaviorAnalysisService();
export default userBehaviorAnalysisService;
