package com.itbook.repository;

import com.itbook.entity.CareerSkillMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 职业技能映射数据访问接口
 * 提供职业技能与原子技能映射关系的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface CareerSkillMappingRepository extends JpaRepository<CareerSkillMapping, Long> {

    /**
     * 根据职业技能ID查找映射关系
     */
    List<CareerSkillMapping> findByCareerSkillId(Long careerSkillId);

    /**
     * 根据原子技能ID查找映射关系
     */
    List<CareerSkillMapping> findByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据职业技能ID和原子技能ID查找映射关系
     */
    Optional<CareerSkillMapping> findByCareerSkillIdAndAtomicSkillId(Long careerSkillId, Long atomicSkillId);

    /**
     * 检查映射关系是否存在
     */
    boolean existsByCareerSkillIdAndAtomicSkillId(Long careerSkillId, Long atomicSkillId);

    /**
     * 根据重要程度查找映射关系
     */
    List<CareerSkillMapping> findByImportance(CareerSkillMapping.Importance importance);

    /**
     * 根据要求掌握水平查找映射关系
     */
    List<CareerSkillMapping> findByRequiredMasteryLevel(CareerSkillMapping.RequiredMasteryLevel requiredMasteryLevel);

    /**
     * 根据映射来源查找映射关系
     */
    List<CareerSkillMapping> findByMappingSource(CareerSkillMapping.MappingSource mappingSource);

    /**
     * 根据职业技能ID按权重排序查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.careerSkillId = :careerSkillId " +
           "ORDER BY csm.weight DESC, csm.importance ASC")
    List<CareerSkillMapping> findByCareerSkillIdOrderByWeight(@Param("careerSkillId") Long careerSkillId);

    /**
     * 根据原子技能ID按权重排序查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.atomicSkillId = :atomicSkillId " +
           "ORDER BY csm.weight DESC, csm.importance ASC")
    List<CareerSkillMapping> findByAtomicSkillIdOrderByWeight(@Param("atomicSkillId") Long atomicSkillId);

    /**
     * 根据职业目标ID查找相关的原子技能映射
     */
    @Query("SELECT csm FROM CareerSkillMapping csm " +
           "JOIN CareerSkill cs ON csm.careerSkillId = cs.id " +
           "WHERE cs.careerGoalId = :careerGoalId " +
           "ORDER BY csm.weight DESC, csm.importance ASC")
    List<CareerSkillMapping> findByCareerGoalId(@Param("careerGoalId") Long careerGoalId);

    /**
     * 根据职业目标ID和重要程度查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm " +
           "JOIN CareerSkill cs ON csm.careerSkillId = cs.id " +
           "WHERE cs.careerGoalId = :careerGoalId AND csm.importance = :importance " +
           "ORDER BY csm.weight DESC")
    List<CareerSkillMapping> findByCareerGoalIdAndImportance(@Param("careerGoalId") Long careerGoalId,
                                                            @Param("importance") CareerSkillMapping.Importance importance);

    /**
     * 根据权重范围查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.weight BETWEEN :minWeight AND :maxWeight " +
           "ORDER BY csm.weight DESC")
    List<CareerSkillMapping> findByWeightRange(@Param("minWeight") BigDecimal minWeight,
                                              @Param("maxWeight") BigDecimal maxWeight);

    /**
     * 根据置信度范围查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.confidenceScore BETWEEN :minConfidence AND :maxConfidence " +
           "ORDER BY csm.confidenceScore DESC")
    List<CareerSkillMapping> findByConfidenceRange(@Param("minConfidence") BigDecimal minConfidence,
                                                  @Param("maxConfidence") BigDecimal maxConfidence);

    /**
     * 查找高置信度映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.confidenceScore >= :minConfidence " +
           "ORDER BY csm.confidenceScore DESC, csm.weight DESC")
    List<CareerSkillMapping> findHighConfidenceMappings(@Param("minConfidence") BigDecimal minConfidence);

    /**
     * 查找关键技能映射
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.importance = 'CRITICAL' " +
           "ORDER BY csm.weight DESC, csm.confidenceScore DESC")
    List<CareerSkillMapping> findCriticalMappings();

    /**
     * 根据职业技能ID获取映射统计
     */
    @Query("SELECT csm.importance, COUNT(csm) FROM CareerSkillMapping csm WHERE " +
           "csm.careerSkillId = :careerSkillId GROUP BY csm.importance ORDER BY csm.importance")
    List<Object[]> getMappingStatisticsByCareerSkill(@Param("careerSkillId") Long careerSkillId);

    /**
     * 根据原子技能ID获取映射统计
     */
    @Query("SELECT csm.importance, COUNT(csm) FROM CareerSkillMapping csm WHERE " +
           "csm.atomicSkillId = :atomicSkillId GROUP BY csm.importance ORDER BY csm.importance")
    List<Object[]> getMappingStatisticsByAtomicSkill(@Param("atomicSkillId") Long atomicSkillId);

    /**
     * 获取重要程度统计
     */
    @Query("SELECT csm.importance, COUNT(csm) FROM CareerSkillMapping csm " +
           "GROUP BY csm.importance ORDER BY COUNT(csm) DESC")
    List<Object[]> getImportanceStatistics();

    /**
     * 获取要求掌握水平统计
     */
    @Query("SELECT csm.requiredMasteryLevel, COUNT(csm) FROM CareerSkillMapping csm " +
           "GROUP BY csm.requiredMasteryLevel ORDER BY csm.requiredMasteryLevel")
    List<Object[]> getRequiredMasteryLevelStatistics();

    /**
     * 获取映射来源统计
     */
    @Query("SELECT csm.mappingSource, COUNT(csm) FROM CareerSkillMapping csm " +
           "GROUP BY csm.mappingSource ORDER BY COUNT(csm) DESC")
    List<Object[]> getMappingSourceStatistics();

    /**
     * 根据职业技能ID查找未映射的原子技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.id NOT IN (SELECT csm.atomicSkillId FROM CareerSkillMapping csm WHERE csm.careerSkillId = :careerSkillId) AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.averageRating DESC, a.learnerCount DESC")
    List<com.itbook.entity.AtomicSkill> findUnmappedAtomicSkillsByCareerSkill(@Param("careerSkillId") Long careerSkillId);

    /**
     * 根据原子技能ID查找未映射的职业技能
     */
    @Query("SELECT cs FROM CareerSkill cs WHERE " +
           "cs.id NOT IN (SELECT csm.careerSkillId FROM CareerSkillMapping csm WHERE csm.atomicSkillId = :atomicSkillId) " +
           "ORDER BY cs.id")
    List<com.itbook.entity.CareerSkill> findUnmappedCareerSkillsByAtomicSkill(@Param("atomicSkillId") Long atomicSkillId);

    /**
     * 根据职业技能ID集合查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.careerSkillId IN :careerSkillIds " +
           "ORDER BY csm.weight DESC")
    List<CareerSkillMapping> findByCareerSkillIdIn(@Param("careerSkillIds") Set<Long> careerSkillIds);

    /**
     * 根据原子技能ID集合查找映射关系
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.atomicSkillId IN :atomicSkillIds " +
           "ORDER BY csm.weight DESC")
    List<CareerSkillMapping> findByAtomicSkillIdIn(@Param("atomicSkillIds") Set<Long> atomicSkillIds);

    /**
     * 批量更新权重
     */
    @Query("UPDATE CareerSkillMapping csm SET " +
           "csm.weight = :weight, " +
           "csm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE csm.id IN :mappingIds")
    int updateWeightBatch(@Param("mappingIds") Set<Long> mappingIds,
                         @Param("weight") BigDecimal weight);

    /**
     * 批量更新重要程度
     */
    @Query("UPDATE CareerSkillMapping csm SET " +
           "csm.importance = :importance, " +
           "csm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE csm.id IN :mappingIds")
    int updateImportanceBatch(@Param("mappingIds") Set<Long> mappingIds,
                             @Param("importance") CareerSkillMapping.Importance importance);

    /**
     * 批量更新置信度分数
     */
    @Query("UPDATE CareerSkillMapping csm SET " +
           "csm.confidenceScore = :confidenceScore, " +
           "csm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE csm.id IN :mappingIds")
    int updateConfidenceScoreBatch(@Param("mappingIds") Set<Long> mappingIds,
                                  @Param("confidenceScore") BigDecimal confidenceScore);

    /**
     * 删除职业技能的所有映射关系
     */
    void deleteByCareerSkillId(Long careerSkillId);

    /**
     * 删除原子技能的所有映射关系
     */
    void deleteByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据创建者查找映射关系
     */
    List<CareerSkillMapping> findByCreatedBy(Long createdBy);

    /**
     * 获取平均权重
     */
    @Query("SELECT AVG(csm.weight) FROM CareerSkillMapping csm")
    BigDecimal getAverageWeight();

    /**
     * 获取平均置信度分数
     */
    @Query("SELECT AVG(csm.confidenceScore) FROM CareerSkillMapping csm")
    BigDecimal getAverageConfidenceScore();

    /**
     * 查找需要验证的映射关系（低置信度）
     */
    @Query("SELECT csm FROM CareerSkillMapping csm WHERE " +
           "csm.confidenceScore < :maxConfidence AND " +
           "csm.mappingSource = 'AUTO_GENERATED' " +
           "ORDER BY csm.confidenceScore ASC")
    List<CareerSkillMapping> findMappingsNeedingVerification(@Param("maxConfidence") BigDecimal maxConfidence);

    /**
     * 查找重复的映射关系
     */
    @Query("SELECT csm1 FROM CareerSkillMapping csm1, CareerSkillMapping csm2 WHERE " +
           "csm1.id < csm2.id AND " +
           "csm1.careerSkillId = csm2.careerSkillId AND " +
           "csm1.atomicSkillId = csm2.atomicSkillId")
    List<CareerSkillMapping> findDuplicateMappings();

    /**
     * 根据职业目标ID获取推荐的原子技能
     */
    @Query("SELECT csm.atomicSkillId, SUM(csm.weight) as totalWeight FROM CareerSkillMapping csm " +
           "JOIN CareerSkill cs ON csm.careerSkillId = cs.id " +
           "WHERE cs.careerGoalId = :careerGoalId " +
           "GROUP BY csm.atomicSkillId " +
           "ORDER BY totalWeight DESC")
    List<Object[]> getRecommendedAtomicSkillsByCareerGoal(@Param("careerGoalId") Long careerGoalId);

}
