package com.itbook.dto;

import com.itbook.entity.UserAtomicSkillMastery;
import java.time.LocalDateTime;

/**
 * 用户技能掌握度DTO
 */
public class UserSkillMasteryDTO {
    private Long id;
    private Long userId;
    private Long atomicSkillId;
    private String skillName;
    private String skillCode;
    private String category;
    private String masteryLevel;
    private Double masteryScore;
    private Double confidenceLevel;
    private Double learningHours;
    private Integer practiceCount;
    private Integer assessmentCount;
    private Double lastAssessmentScore;
    private LocalDateTime firstLearnedAt;
    private LocalDateTime lastPracticedAt;
    private LocalDateTime lastAssessedAt;
    private LocalDateTime masteryAchievedAt;
    private Boolean isCertified;
    private LocalDateTime certificationDate;
    private Boolean needsRefresh;
    private Double decayFactor;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public UserSkillMasteryDTO() {}

    public UserSkillMasteryDTO(UserAtomicSkillMastery mastery) {
        this.id = mastery.getId();
        this.userId = mastery.getUserId();
        this.atomicSkillId = mastery.getAtomicSkillId();
        this.masteryLevel = mastery.getMasteryLevel().name();
        this.masteryScore = mastery.getMasteryScore();
        this.confidenceLevel = mastery.getConfidenceLevel();
        this.learningHours = mastery.getLearningHours();
        this.practiceCount = mastery.getPracticeCount();
        this.assessmentCount = mastery.getAssessmentCount();
        this.lastAssessmentScore = mastery.getLastAssessmentScore();
        this.firstLearnedAt = mastery.getFirstLearnedAt();
        this.lastPracticedAt = mastery.getLastPracticedAt();
        this.lastAssessedAt = mastery.getLastAssessedAt();
        this.masteryAchievedAt = mastery.getMasteryAchievedAt();
        this.isCertified = mastery.getIsCertified();
        this.certificationDate = mastery.getCertificationDate();
        this.needsRefresh = mastery.getNeedsRefresh();
        this.decayFactor = mastery.getDecayFactor();
        this.createdAt = mastery.getCreatedAt();
        this.updatedAt = mastery.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public String getSkillName() { return skillName; }
    public void setSkillName(String skillName) { this.skillName = skillName; }

    public String getSkillCode() { return skillCode; }
    public void setSkillCode(String skillCode) { this.skillCode = skillCode; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getMasteryLevel() { return masteryLevel; }
    public void setMasteryLevel(String masteryLevel) { this.masteryLevel = masteryLevel; }

    public Double getMasteryScore() { return masteryScore; }
    public void setMasteryScore(Double masteryScore) { this.masteryScore = masteryScore; }

    public Double getConfidenceLevel() { return confidenceLevel; }
    public void setConfidenceLevel(Double confidenceLevel) { this.confidenceLevel = confidenceLevel; }

    public Double getLearningHours() { return learningHours; }
    public void setLearningHours(Double learningHours) { this.learningHours = learningHours; }

    public Integer getPracticeCount() { return practiceCount; }
    public void setPracticeCount(Integer practiceCount) { this.practiceCount = practiceCount; }

    public Integer getAssessmentCount() { return assessmentCount; }
    public void setAssessmentCount(Integer assessmentCount) { this.assessmentCount = assessmentCount; }

    public Double getLastAssessmentScore() { return lastAssessmentScore; }
    public void setLastAssessmentScore(Double lastAssessmentScore) { this.lastAssessmentScore = lastAssessmentScore; }

    public LocalDateTime getFirstLearnedAt() { return firstLearnedAt; }
    public void setFirstLearnedAt(LocalDateTime firstLearnedAt) { this.firstLearnedAt = firstLearnedAt; }

    public LocalDateTime getLastPracticedAt() { return lastPracticedAt; }
    public void setLastPracticedAt(LocalDateTime lastPracticedAt) { this.lastPracticedAt = lastPracticedAt; }

    public LocalDateTime getLastAssessedAt() { return lastAssessedAt; }
    public void setLastAssessedAt(LocalDateTime lastAssessedAt) { this.lastAssessedAt = lastAssessedAt; }

    public LocalDateTime getMasteryAchievedAt() { return masteryAchievedAt; }
    public void setMasteryAchievedAt(LocalDateTime masteryAchievedAt) { this.masteryAchievedAt = masteryAchievedAt; }

    public Boolean getIsCertified() { return isCertified; }
    public void setIsCertified(Boolean isCertified) { this.isCertified = isCertified; }

    public LocalDateTime getCertificationDate() { return certificationDate; }
    public void setCertificationDate(LocalDateTime certificationDate) { this.certificationDate = certificationDate; }

    public Boolean getNeedsRefresh() { return needsRefresh; }
    public void setNeedsRefresh(Boolean needsRefresh) { this.needsRefresh = needsRefresh; }

    public Double getDecayFactor() { return decayFactor; }
    public void setDecayFactor(Double decayFactor) { this.decayFactor = decayFactor; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
