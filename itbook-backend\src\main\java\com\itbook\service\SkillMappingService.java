package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.dto.SkillMappingResult;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能映射服务
 * 实现现有技能到原子技能的智能映射算法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class SkillMappingService {

    @Autowired
    private TextSimilarityService textSimilarityService;

    @Autowired
    private CareerSkillRepository careerSkillRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private CareerSkillMappingRepository mappingRepository;

    @Autowired
    private LearningPathRepository learningPathRepository;

    @Autowired
    private CourseRepository courseRepository;

    // 映射阈值配置
    private static final double HIGH_CONFIDENCE_THRESHOLD = 0.8;
    private static final double MEDIUM_CONFIDENCE_THRESHOLD = 0.6;
    private static final double LOW_CONFIDENCE_THRESHOLD = 0.4;

    /**
     * 执行完整的技能映射流程
     */
    public ApiResponse<SkillMappingResult> executeFullMapping() {
        try {
            SkillMappingResult result = new SkillMappingResult();
            
            // 阶段1：从career_skill生成原子技能
            List<AtomicSkill> careerBasedSkills = generateAtomicSkillsFromCareerSkills();
            result.setCareerBasedSkillCount(careerBasedSkills.size());
            
            // 阶段2：从learning_path技能标签提取原子技能
            List<AtomicSkill> pathBasedSkills = generateAtomicSkillsFromLearningPaths();
            result.setPathBasedSkillCount(pathBasedSkills.size());
            
            // 阶段3：从course标签提取原子技能
            List<AtomicSkill> courseBasedSkills = generateAtomicSkillsFromCourses();
            result.setCourseBasedSkillCount(courseBasedSkills.size());
            
            // 阶段4：合并和去重
            List<AtomicSkill> mergedSkills = mergeAndDeduplicateSkills(
                careerBasedSkills, pathBasedSkills, courseBasedSkills);
            result.setTotalAtomicSkillCount(mergedSkills.size());
            
            // 阶段5：建立映射关系
            List<CareerSkillMapping> mappings = createCareerSkillMappings();
            result.setMappingCount(mappings.size());
            
            // 阶段6：计算映射质量统计
            calculateMappingQualityStats(result);
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("技能映射执行失败: " + e.getMessage());
        }
    }

    /**
     * 从career_skill表生成原子技能
     */
    public List<AtomicSkill> generateAtomicSkillsFromCareerSkills() {
        List<CareerSkill> careerSkills = careerSkillRepository.findAll();
        List<AtomicSkill> atomicSkills = new ArrayList<>();
        
        for (CareerSkill careerSkill : careerSkills) {
            // 主技能映射
            AtomicSkill mainSkill = createAtomicSkillFromCareerSkill(careerSkill);
            atomicSkills.add(mainSkill);
            
            // 子技能拆分（基于描述分析）
            List<AtomicSkill> subSkills = extractSubSkillsFromDescription(careerSkill);
            atomicSkills.addAll(subSkills);
        }
        
        return atomicSkillRepository.saveAll(atomicSkills);
    }

    /**
     * 从learning_path的skill_tags生成原子技能
     */
    public List<AtomicSkill> generateAtomicSkillsFromLearningPaths() {
        List<LearningPath> learningPaths = learningPathRepository.findAll();
        Set<AtomicSkill> uniqueSkills = new HashSet<>();
        
        for (LearningPath path : learningPaths) {
            if (path.getSkillTags() != null) {
                List<AtomicSkill> pathSkills = extractSkillsFromJson(
                    path.getSkillTags(), "learning_path", path.getDifficultyLevel());
                uniqueSkills.addAll(pathSkills);
            }
        }
        
        return atomicSkillRepository.saveAll(new ArrayList<>(uniqueSkills));
    }

    /**
     * 从course的tags生成原子技能
     */
    public List<AtomicSkill> generateAtomicSkillsFromCourses() {
        List<Course> courses = courseRepository.findAll();
        Set<AtomicSkill> uniqueSkills = new HashSet<>();
        
        for (Course course : courses) {
            if (course.getTags() != null) {
                List<AtomicSkill> courseSkills = extractSkillsFromJson(
                    course.getTags(), "course", course.getDifficulty().toString());
                uniqueSkills.addAll(courseSkills);
            }
        }
        
        return atomicSkillRepository.saveAll(new ArrayList<>(uniqueSkills));
    }

    /**
     * 从career_skill创建原子技能
     */
    private AtomicSkill createAtomicSkillFromCareerSkill(CareerSkill careerSkill) {
        AtomicSkill atomicSkill = new AtomicSkill();
        
        // 生成技能编码
        atomicSkill.setSkillCode(generateSkillCode(careerSkill.getSkillName(), careerSkill.getSkillCategory()));
        atomicSkill.setName(careerSkill.getSkillName());
        atomicSkill.setDescription(careerSkill.getDescription());
        atomicSkill.setCategory(normalizeCategory(careerSkill.getSkillCategory()));
        atomicSkill.setSubcategory(extractSubcategory(careerSkill.getSkillName()));
        
        // 映射难度级别
        atomicSkill.setDifficultyLevel(mapDifficultyLevel(careerSkill.getTargetLevel()));
        
        // 映射技能类型
        atomicSkill.setSkillType(mapSkillType(careerSkill.getSkillType()));
        
        // 设置默认值
        atomicSkill.setEstimatedHours(estimateHours(careerSkill));
        atomicSkill.setAverageRating(BigDecimal.valueOf(4.0));
        atomicSkill.setLearnerCount(0L);
        atomicSkill.setCompletionRate(BigDecimal.valueOf(0.8));
        atomicSkill.setStatus(AtomicSkill.Status.PUBLISHED);
        atomicSkill.setIsActive(true);
        
        // 生成关键词
        atomicSkill.setKeywords(generateKeywords(careerSkill));
        
        return atomicSkill;
    }

    /**
     * 从描述中提取子技能
     */
    private List<AtomicSkill> extractSubSkillsFromDescription(CareerSkill careerSkill) {
        List<AtomicSkill> subSkills = new ArrayList<>();
        
        if (careerSkill.getDescription() == null || careerSkill.getDescription().trim().isEmpty()) {
            return subSkills;
        }
        
        // 基于关键词和模式识别子技能
        String description = careerSkill.getDescription().toLowerCase();
        
        // 技能分解规则
        Map<String, List<String>> skillDecomposition = Map.of(
            "java", Arrays.asList("java语法", "面向对象编程", "集合框架", "异常处理", "多线程"),
            "spring", Arrays.asList("依赖注入", "aop", "mvc", "数据访问", "事务管理"),
            "数据库", Arrays.asList("sql查询", "数据建模", "索引优化", "事务处理", "备份恢复"),
            "web", Arrays.asList("html", "css", "javascript", "http协议", "前端框架")
        );
        
        for (Map.Entry<String, List<String>> entry : skillDecomposition.entrySet()) {
            if (description.contains(entry.getKey())) {
                for (String subSkillName : entry.getValue()) {
                    AtomicSkill subSkill = createSubAtomicSkill(careerSkill, subSkillName);
                    subSkills.add(subSkill);
                }
            }
        }
        
        return subSkills;
    }

    /**
     * 创建子原子技能
     */
    private AtomicSkill createSubAtomicSkill(CareerSkill parentSkill, String subSkillName) {
        AtomicSkill subSkill = new AtomicSkill();
        
        subSkill.setSkillCode(generateSkillCode(subSkillName, parentSkill.getSkillCategory()));
        subSkill.setName(subSkillName);
        subSkill.setDescription("从" + parentSkill.getSkillName() + "中提取的子技能");
        subSkill.setCategory(normalizeCategory(parentSkill.getSkillCategory()));
        subSkill.setSubcategory(parentSkill.getSkillName());
        
        // 子技能难度通常比父技能低一级
        AtomicSkill.DifficultyLevel parentDifficulty = mapDifficultyLevel(parentSkill.getTargetLevel());
        subSkill.setDifficultyLevel(lowerDifficultyLevel(parentDifficulty));
        
        subSkill.setSkillType(AtomicSkill.SkillType.TECHNICAL);
        subSkill.setEstimatedHours(estimateHours(parentSkill) / 5); // 子技能时长为父技能的1/5
        subSkill.setAverageRating(BigDecimal.valueOf(4.0));
        subSkill.setLearnerCount(0L);
        subSkill.setCompletionRate(BigDecimal.valueOf(0.8));
        subSkill.setStatus(AtomicSkill.Status.PUBLISHED);
        subSkill.setIsActive(true);
        subSkill.setKeywords(subSkillName + "," + parentSkill.getSkillName());
        
        return subSkill;
    }

    /**
     * 从JSON标签中提取技能
     */
    private List<AtomicSkill> extractSkillsFromJson(String jsonTags, String source, String difficulty) {
        List<AtomicSkill> skills = new ArrayList<>();
        
        try {
            // 简化的JSON解析（实际项目中应使用Jackson或Gson）
            String[] tags = jsonTags.replaceAll("[\\[\\]\"{}]", "").split(",");
            
            for (String tag : tags) {
                String skillName = tag.trim();
                if (!skillName.isEmpty() && skillName.length() > 1) {
                    AtomicSkill skill = createAtomicSkillFromTag(skillName, source, difficulty);
                    skills.add(skill);
                }
            }
        } catch (Exception e) {
            // 日志记录解析错误，但不中断流程
            System.err.println("JSON解析错误: " + e.getMessage());
        }
        
        return skills;
    }

    /**
     * 从标签创建原子技能
     */
    private AtomicSkill createAtomicSkillFromTag(String tagName, String source, String difficulty) {
        AtomicSkill skill = new AtomicSkill();
        
        skill.setSkillCode(generateSkillCode(tagName, "general"));
        skill.setName(tagName);
        skill.setDescription("从" + source + "标签中提取的技能");
        skill.setCategory("general");
        skill.setSubcategory(source);
        skill.setDifficultyLevel(parseDifficultyLevel(difficulty));
        skill.setSkillType(AtomicSkill.SkillType.TECHNICAL);
        skill.setEstimatedHours(20); // 默认20小时
        skill.setAverageRating(BigDecimal.valueOf(4.0));
        skill.setLearnerCount(0L);
        skill.setCompletionRate(BigDecimal.valueOf(0.8));
        skill.setStatus(AtomicSkill.Status.PUBLISHED);
        skill.setIsActive(true);
        skill.setKeywords(tagName);
        
        return skill;
    }

    /**
     * 合并和去重技能
     */
    private List<AtomicSkill> mergeAndDeduplicateSkills(List<AtomicSkill>... skillLists) {
        Map<String, AtomicSkill> uniqueSkills = new HashMap<>();
        
        for (List<AtomicSkill> skillList : skillLists) {
            for (AtomicSkill skill : skillList) {
                String key = normalizeSkillName(skill.getName());
                
                if (uniqueSkills.containsKey(key)) {
                    // 合并重复技能的信息
                    AtomicSkill existingSkill = uniqueSkills.get(key);
                    mergeSkillInfo(existingSkill, skill);
                } else {
                    uniqueSkills.put(key, skill);
                }
            }
        }
        
        return new ArrayList<>(uniqueSkills.values());
    }

    /**
     * 创建职业技能映射关系
     */
    private List<CareerSkillMapping> createCareerSkillMappings() {
        List<CareerSkillMapping> mappings = new ArrayList<>();
        List<CareerSkill> careerSkills = careerSkillRepository.findAll();
        List<AtomicSkill> atomicSkills = atomicSkillRepository.findAll();
        
        for (CareerSkill careerSkill : careerSkills) {
            List<AtomicSkill> matchedSkills = findMatchingAtomicSkills(careerSkill, atomicSkills);
            
            for (AtomicSkill atomicSkill : matchedSkills) {
                CareerSkillMapping mapping = createMapping(careerSkill, atomicSkill);
                mappings.add(mapping);
            }
        }
        
        return mappingRepository.saveAll(mappings);
    }

    /**
     * 查找匹配的原子技能
     */
    private List<AtomicSkill> findMatchingAtomicSkills(CareerSkill careerSkill, List<AtomicSkill> atomicSkills) {
        String careerSkillText = careerSkill.getSkillName() + " " + 
                                (careerSkill.getDescription() != null ? careerSkill.getDescription() : "");
        
        List<String> atomicSkillTexts = atomicSkills.stream()
                .map(skill -> skill.getName() + " " + 
                            (skill.getDescription() != null ? skill.getDescription() : ""))
                .collect(Collectors.toList());
        
        List<TextSimilarityService.SimilarityResult> results = 
                textSimilarityService.calculateSimilarities(careerSkillText, atomicSkillTexts);
        
        return results.stream()
                .filter(result -> result.getSimilarity() >= LOW_CONFIDENCE_THRESHOLD)
                .map(result -> atomicSkills.get(result.getIndex()))
                .collect(Collectors.toList());
    }

    /**
     * 创建映射关系
     */
    private CareerSkillMapping createMapping(CareerSkill careerSkill, AtomicSkill atomicSkill) {
        CareerSkillMapping mapping = new CareerSkillMapping();
        
        mapping.setCareerSkillId(careerSkill.getId());
        mapping.setAtomicSkillId(atomicSkill.getId());
        
        // 计算权重和置信度
        double similarity = textSimilarityService.calculateSimilarity(
            careerSkill.getSkillName(), atomicSkill.getName());
        
        mapping.setWeight(BigDecimal.valueOf(similarity).setScale(2, RoundingMode.HALF_UP));
        mapping.setConfidenceScore(BigDecimal.valueOf(similarity).setScale(2, RoundingMode.HALF_UP));
        
        // 映射重要程度
        mapping.setImportance(mapImportance(careerSkill.getImportance()));
        mapping.setRequiredMasteryLevel(mapRequiredMasteryLevel(careerSkill.getTargetLevel()));
        mapping.setMappingSource(CareerSkillMapping.MappingSource.AUTO_GENERATED);
        mapping.setMappingReason("基于文本相似度自动生成，相似度: " + String.format("%.2f", similarity));
        
        return mapping;
    }

    // 辅助方法
    private String generateSkillCode(String skillName, String category) {
        String categoryPrefix = getCategoryPrefix(category);
        String nameAbbr = generateAbbreviation(skillName);
        String sequence = String.format("%03d", (int)(Math.random() * 1000));
        return String.format("%s_%s_%s", categoryPrefix, nameAbbr, sequence);
    }

    private String getCategoryPrefix(String category) {
        Map<String, String> prefixMap = Map.of(
            "编程语言", "PROG",
            "开发框架", "FRAM", 
            "数据库", "DB",
            "Web技术", "WEB",
            "架构设计", "ARCH",
            "general", "GEN"
        );
        return prefixMap.getOrDefault(category, "MISC");
    }

    private String generateAbbreviation(String text) {
        return text.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z]", "")
                   .toUpperCase()
                   .substring(0, Math.min(text.length(), 8));
    }

    private String normalizeCategory(String category) {
        Map<String, String> categoryMap = Map.of(
            "编程语言", "programming_language",
            "开发框架", "development_framework",
            "数据库", "database", 
            "Web技术", "web_technology",
            "架构设计", "architecture_design"
        );
        return categoryMap.getOrDefault(category, "general");
    }

    private AtomicSkill.DifficultyLevel mapDifficultyLevel(String targetLevel) {
        switch (targetLevel.toLowerCase()) {
            case "beginner": return AtomicSkill.DifficultyLevel.BEGINNER;
            case "intermediate": return AtomicSkill.DifficultyLevel.INTERMEDIATE;
            case "advanced": return AtomicSkill.DifficultyLevel.ADVANCED;
            case "expert": return AtomicSkill.DifficultyLevel.EXPERT;
            default: return AtomicSkill.DifficultyLevel.INTERMEDIATE;
        }
    }

    private AtomicSkill.SkillType mapSkillType(String skillType) {
        return "core".equals(skillType) ? AtomicSkill.SkillType.TECHNICAL : AtomicSkill.SkillType.SOFT;
    }

    private CareerSkillMapping.Importance mapImportance(String importance) {
        switch (importance.toLowerCase()) {
            case "critical": return CareerSkillMapping.Importance.CRITICAL;
            case "important": return CareerSkillMapping.Importance.IMPORTANT;
            case "nice-to-have": return CareerSkillMapping.Importance.NICE_TO_HAVE;
            default: return CareerSkillMapping.Importance.IMPORTANT;
        }
    }

    private CareerSkillMapping.RequiredMasteryLevel mapRequiredMasteryLevel(String targetLevel) {
        switch (targetLevel.toLowerCase()) {
            case "beginner": return CareerSkillMapping.RequiredMasteryLevel.BASIC;
            case "intermediate": return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
            case "advanced": return CareerSkillMapping.RequiredMasteryLevel.ADVANCED;
            case "expert": return CareerSkillMapping.RequiredMasteryLevel.EXPERT;
            default: return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
        }
    }

    private Integer estimateHours(CareerSkill careerSkill) {
        // 基于技能复杂度和目标水平估算学习时长
        int baseHours = 40;
        String targetLevel = careerSkill.getTargetLevel().toLowerCase();
        
        switch (targetLevel) {
            case "beginner": return baseHours;
            case "intermediate": return baseHours * 2;
            case "advanced": return baseHours * 3;
            case "expert": return baseHours * 4;
            default: return baseHours;
        }
    }

    private String generateKeywords(CareerSkill careerSkill) {
        List<String> keywords = new ArrayList<>();
        keywords.add(careerSkill.getSkillName().toLowerCase());
        if (careerSkill.getSkillCategory() != null) {
            keywords.add(careerSkill.getSkillCategory().toLowerCase());
        }
        return String.join(",", keywords);
    }

    private String extractSubcategory(String skillName) {
        // 简单的子分类提取逻辑
        if (skillName.contains("Java")) return "Java";
        if (skillName.contains("Spring")) return "Spring";
        if (skillName.contains("MySQL")) return "MySQL";
        if (skillName.contains("React")) return "React";
        return "general";
    }

    private AtomicSkill.DifficultyLevel lowerDifficultyLevel(AtomicSkill.DifficultyLevel level) {
        switch (level) {
            case EXPERT: return AtomicSkill.DifficultyLevel.ADVANCED;
            case ADVANCED: return AtomicSkill.DifficultyLevel.INTERMEDIATE;
            case INTERMEDIATE: return AtomicSkill.DifficultyLevel.BEGINNER;
            case BEGINNER: return AtomicSkill.DifficultyLevel.BEGINNER;
            default: return AtomicSkill.DifficultyLevel.BEGINNER;
        }
    }

    private AtomicSkill.DifficultyLevel parseDifficultyLevel(String difficulty) {
        try {
            return AtomicSkill.DifficultyLevel.valueOf(difficulty.toUpperCase());
        } catch (Exception e) {
            return AtomicSkill.DifficultyLevel.INTERMEDIATE;
        }
    }

    private String normalizeSkillName(String name) {
        return name.toLowerCase().replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
    }

    private void mergeSkillInfo(AtomicSkill existing, AtomicSkill newSkill) {
        // 合并技能信息，保留更完整的描述
        if (existing.getDescription() == null || existing.getDescription().length() < newSkill.getDescription().length()) {
            existing.setDescription(newSkill.getDescription());
        }
        
        // 合并关键词
        String existingKeywords = existing.getKeywords() != null ? existing.getKeywords() : "";
        String newKeywords = newSkill.getKeywords() != null ? newSkill.getKeywords() : "";
        Set<String> allKeywords = new HashSet<>();
        allKeywords.addAll(Arrays.asList(existingKeywords.split(",")));
        allKeywords.addAll(Arrays.asList(newKeywords.split(",")));
        existing.setKeywords(String.join(",", allKeywords));
    }

    private void calculateMappingQualityStats(SkillMappingResult result) {
        List<CareerSkillMapping> allMappings = mappingRepository.findAll();
        
        long highConfidenceCount = allMappings.stream()
                .mapToLong(m -> m.getConfidenceScore().doubleValue() >= HIGH_CONFIDENCE_THRESHOLD ? 1 : 0)
                .sum();
        
        long mediumConfidenceCount = allMappings.stream()
                .mapToLong(m -> {
                    double confidence = m.getConfidenceScore().doubleValue();
                    return confidence >= MEDIUM_CONFIDENCE_THRESHOLD && confidence < HIGH_CONFIDENCE_THRESHOLD ? 1 : 0;
                })
                .sum();
        
        long lowConfidenceCount = allMappings.stream()
                .mapToLong(m -> {
                    double confidence = m.getConfidenceScore().doubleValue();
                    return confidence >= LOW_CONFIDENCE_THRESHOLD && confidence < MEDIUM_CONFIDENCE_THRESHOLD ? 1 : 0;
                })
                .sum();
        
        result.setHighConfidenceMappings((int) highConfidenceCount);
        result.setMediumConfidenceMappings((int) mediumConfidenceCount);
        result.setLowConfidenceMappings((int) lowConfidenceCount);
        
        double avgConfidence = allMappings.stream()
                .mapToDouble(m -> m.getConfidenceScore().doubleValue())
                .average()
                .orElse(0.0);
        result.setAverageConfidence(avgConfidence);
    }
}
