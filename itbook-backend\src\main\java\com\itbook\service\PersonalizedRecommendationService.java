package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个性化推荐引擎服务
 * 基于用户学习历史、技能掌握情况和职业目标提供个性化的学习路径推荐
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonalizedRecommendationService {

    private final UserRepository userRepository;
    private final UserCareerGoalRepository userCareerGoalRepository;
    private final UserAtomicSkillMasteryRepository userAtomicSkillMasteryRepository;
    private final UserLearningPathProgressRepository userLearningPathProgressRepository;
    private final AtomicSkillRepository atomicSkillRepository;
    private final SkillRelationshipRepository skillRelationshipRepository;
    private final CareerSkillMappingRepository careerSkillMappingRepository;
    private final SkillGraphService skillGraphService;
    private final SkillRelationshipService skillRelationshipService;

    /**
     * 为用户生成个性化学习路径推荐
     */
    @Transactional(readOnly = true)
    public PersonalizedRecommendation generatePersonalizedRecommendation(Long userId) {
        log.debug("为用户生成个性化推荐: userId={}", userId);
        
        // 获取用户信息
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        // 分析用户画像
        UserProfile userProfile = analyzeUserProfile(userId);
        
        // 获取推荐的学习路径
        List<RecommendedPath> recommendedPaths = generateRecommendedPaths(userProfile);
        
        // 获取推荐的技能
        List<RecommendedSkill> recommendedSkills = generateRecommendedSkills(userProfile);
        
        // 获取学习建议
        List<LearningAdvice> learningAdvices = generateLearningAdvices(userProfile);
        
        PersonalizedRecommendation recommendation = new PersonalizedRecommendation();
        recommendation.setUserId(userId);
        recommendation.setUserProfile(userProfile);
        recommendation.setRecommendedPaths(recommendedPaths);
        recommendation.setRecommendedSkills(recommendedSkills);
        recommendation.setLearningAdvices(learningAdvices);
        recommendation.setGeneratedAt(LocalDateTime.now());
        
        return recommendation;
    }

    /**
     * 分析用户画像
     */
    private UserProfile analyzeUserProfile(Long userId) {
        log.debug("分析用户画像: userId={}", userId);
        
        UserProfile profile = new UserProfile();
        profile.setUserId(userId);
        
        // 分析职业目标
        List<UserCareerGoal> careerGoals = userCareerGoalRepository.findByUserId(userId);
        profile.setCareerGoals(careerGoals);
        
        // 分析技能掌握情况
        List<UserAtomicSkillMastery> skillMasteries = userAtomicSkillMasteryRepository.findByUserId(userId);
        profile.setSkillMasteries(skillMasteries);
        
        // 计算技能掌握统计
        SkillMasteryStats masteryStats = calculateSkillMasteryStats(skillMasteries);
        profile.setMasteryStats(masteryStats);
        
        // 分析学习历史
        List<UserLearningPathProgress> learningProgress = userLearningPathProgressRepository.findByUserId(userId);
        profile.setLearningProgress(learningProgress);
        
        // 计算学习偏好
        LearningPreferences preferences = analyzeLearningPreferences(learningProgress, skillMasteries);
        profile.setLearningPreferences(preferences);
        
        return profile;
    }

    /**
     * 生成推荐的学习路径
     */
    private List<RecommendedPath> generateRecommendedPaths(UserProfile userProfile) {
        log.debug("生成推荐学习路径: userId={}", userProfile.getUserId());
        
        List<RecommendedPath> recommendedPaths = new ArrayList<>();
        
        // 基于职业目标推荐路径
        for (UserCareerGoal careerGoal : userProfile.getCareerGoals()) {
            List<RecommendedPath> careerBasedPaths = generateCareerBasedPaths(careerGoal, userProfile);
            recommendedPaths.addAll(careerBasedPaths);
        }
        
        // 基于技能缺口推荐路径
        List<RecommendedPath> skillGapPaths = generateSkillGapBasedPaths(userProfile);
        recommendedPaths.addAll(skillGapPaths);
        
        // 基于学习偏好推荐路径
        List<RecommendedPath> preferencePaths = generatePreferenceBasedPaths(userProfile);
        recommendedPaths.addAll(preferencePaths);
        
        // 排序和去重
        return recommendedPaths.stream()
                .sorted((a, b) -> b.getRecommendationScore().compareTo(a.getRecommendationScore()))
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 生成推荐的技能
     */
    private List<RecommendedSkill> generateRecommendedSkills(UserProfile userProfile) {
        log.debug("生成推荐技能: userId={}", userProfile.getUserId());
        
        List<RecommendedSkill> recommendedSkills = new ArrayList<>();
        
        // 获取用户已掌握的技能ID
        Set<Long> masteredSkillIds = userProfile.getSkillMasteries().stream()
                .filter(mastery -> mastery.getMasteryLevel() >= 0.7) // 掌握度70%以上认为已掌握
                .map(UserAtomicSkillMastery::getSkillId)
                .collect(Collectors.toSet());
        
        // 基于已掌握技能推荐相关技能
        for (Long masteredSkillId : masteredSkillIds) {
            List<SkillGraphService.SkillRecommendation> graphRecommendations = 
                    skillGraphService.recommendRelatedSkills(masteredSkillId, 5);
            
            for (SkillGraphService.SkillRecommendation graphRec : graphRecommendations) {
                if (!masteredSkillIds.contains(graphRec.getSkill().getId())) {
                    RecommendedSkill skill = new RecommendedSkill();
                    skill.setSkill(graphRec.getSkill());
                    skill.setRecommendationScore(graphRec.getScore());
                    skill.setRecommendationReason("基于已掌握技能 " + 
                            atomicSkillRepository.findById(masteredSkillId)
                                    .map(AtomicSkill::getName).orElse("未知技能") + " 的关联推荐");
                    skill.setRecommendationType("SKILL_BASED");
                    recommendedSkills.add(skill);
                }
            }
        }
        
        // 基于职业目标推荐技能
        for (UserCareerGoal careerGoal : userProfile.getCareerGoals()) {
            List<CareerSkillMapping> careerSkills = careerSkillMappingRepository
                    .findByCareerGoalId(careerGoal.getCareerGoalId());
            
            for (CareerSkillMapping mapping : careerSkills) {
                if (!masteredSkillIds.contains(mapping.getSkillId())) {
                    AtomicSkill skill = atomicSkillRepository.findById(mapping.getSkillId()).orElse(null);
                    if (skill != null) {
                        RecommendedSkill recommendedSkill = new RecommendedSkill();
                        recommendedSkill.setSkill(skillRelationshipService.createCleanSkill(skill));
                        recommendedSkill.setRecommendationScore(
                                mapping.getImportanceLevel().equals("HIGH") ? new BigDecimal("0.9") :
                                mapping.getImportanceLevel().equals("MEDIUM") ? new BigDecimal("0.7") :
                                new BigDecimal("0.5"));
                        recommendedSkill.setRecommendationReason("职业目标所需技能");
                        recommendedSkill.setRecommendationType("CAREER_BASED");
                        recommendedSkills.add(recommendedSkill);
                    }
                }
            }
        }
        
        // 排序和去重
        Map<Long, RecommendedSkill> uniqueSkills = new HashMap<>();
        for (RecommendedSkill skill : recommendedSkills) {
            Long skillId = skill.getSkill().getId();
            if (!uniqueSkills.containsKey(skillId) || 
                skill.getRecommendationScore().compareTo(uniqueSkills.get(skillId).getRecommendationScore()) > 0) {
                uniqueSkills.put(skillId, skill);
            }
        }
        
        return uniqueSkills.values().stream()
                .sorted((a, b) -> b.getRecommendationScore().compareTo(a.getRecommendationScore()))
                .limit(15)
                .collect(Collectors.toList());
    }

    /**
     * 生成学习建议
     */
    private List<LearningAdvice> generateLearningAdvices(UserProfile userProfile) {
        log.debug("生成学习建议: userId={}", userProfile.getUserId());
        
        List<LearningAdvice> advices = new ArrayList<>();
        
        // 基于技能掌握情况的建议
        SkillMasteryStats stats = userProfile.getMasteryStats();
        if (stats.getAverageMasteryLevel() < 0.5) {
            advices.add(new LearningAdvice("SKILL_IMPROVEMENT", "建议加强基础技能学习", 
                    "您的平均技能掌握度较低，建议先巩固基础技能再学习高级技能", "HIGH"));
        }
        
        // 基于学习偏好的建议
        LearningPreferences preferences = userProfile.getLearningPreferences();
        if (preferences.getPreferredDifficulty() != null) {
            advices.add(new LearningAdvice("DIFFICULTY_MATCH", "匹配学习难度", 
                    "根据您的学习历史，建议选择" + preferences.getPreferredDifficulty() + "难度的课程", "MEDIUM"));
        }
        
        // 基于学习进度的建议
        if (userProfile.getLearningProgress().isEmpty()) {
            advices.add(new LearningAdvice("START_LEARNING", "开始学习之旅", 
                    "建议选择一个学习路径开始您的技能提升之旅", "HIGH"));
        } else {
            long inProgressCount = userProfile.getLearningProgress().stream()
                    .filter(progress -> progress.getStatus().equals("IN_PROGRESS"))
                    .count();
            if (inProgressCount > 3) {
                advices.add(new LearningAdvice("FOCUS_LEARNING", "专注学习", 
                        "您同时进行的学习路径较多，建议专注完成1-2个路径以提高学习效果", "MEDIUM"));
            }
        }
        
        return advices;
    }

    /**
     * 计算技能掌握统计
     */
    private SkillMasteryStats calculateSkillMasteryStats(List<UserAtomicSkillMastery> skillMasteries) {
        SkillMasteryStats stats = new SkillMasteryStats();
        
        if (skillMasteries.isEmpty()) {
            stats.setTotalSkills(0);
            stats.setMasteredSkills(0);
            stats.setAverageMasteryLevel(0.0);
            return stats;
        }
        
        stats.setTotalSkills(skillMasteries.size());
        stats.setMasteredSkills((int) skillMasteries.stream()
                .filter(mastery -> mastery.getMasteryLevel() >= 0.7)
                .count());
        stats.setAverageMasteryLevel(skillMasteries.stream()
                .mapToDouble(UserAtomicSkillMastery::getMasteryLevel)
                .average()
                .orElse(0.0));
        
        // 按类别统计
        Map<String, Long> categoryStats = skillMasteries.stream()
                .collect(Collectors.groupingBy(
                        mastery -> {
                            AtomicSkill skill = atomicSkillRepository.findById(mastery.getSkillId()).orElse(null);
                            return skill != null ? skill.getCategory() : "unknown";
                        },
                        Collectors.counting()));
        stats.setCategoryDistribution(categoryStats);
        
        return stats;
    }

    /**
     * 分析学习偏好
     */
    private LearningPreferences analyzeLearningPreferences(List<UserLearningPathProgress> learningProgress,
                                                          List<UserAtomicSkillMastery> skillMasteries) {
        LearningPreferences preferences = new LearningPreferences();
        
        // 分析偏好的难度级别
        if (!skillMasteries.isEmpty()) {
            Map<AtomicSkill.DifficultyLevel, Long> difficultyStats = skillMasteries.stream()
                    .filter(mastery -> mastery.getMasteryLevel() >= 0.7)
                    .collect(Collectors.groupingBy(
                            mastery -> {
                                AtomicSkill skill = atomicSkillRepository.findById(mastery.getSkillId()).orElse(null);
                                return skill != null ? skill.getDifficultyLevel() : AtomicSkill.DifficultyLevel.BEGINNER;
                            },
                            Collectors.counting()));
            
            AtomicSkill.DifficultyLevel preferredDifficulty = difficultyStats.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(AtomicSkill.DifficultyLevel.BEGINNER);
            preferences.setPreferredDifficulty(preferredDifficulty.toString());
        }
        
        // 分析偏好的技能类别
        if (!skillMasteries.isEmpty()) {
            Map<String, Long> categoryStats = skillMasteries.stream()
                    .collect(Collectors.groupingBy(
                            mastery -> {
                                AtomicSkill skill = atomicSkillRepository.findById(mastery.getSkillId()).orElse(null);
                                return skill != null ? skill.getCategory() : "unknown";
                            },
                            Collectors.counting()));
            
            String preferredCategory = categoryStats.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("frontend");
            preferences.setPreferredCategory(preferredCategory);
        }
        
        // 分析学习活跃度
        if (!learningProgress.isEmpty()) {
            long activePathsCount = learningProgress.stream()
                    .filter(progress -> progress.getStatus().equals("IN_PROGRESS"))
                    .count();
            preferences.setLearningActivity(activePathsCount > 2 ? "HIGH" : 
                                          activePathsCount > 0 ? "MEDIUM" : "LOW");
        } else {
            preferences.setLearningActivity("LOW");
        }
        
        return preferences;
    }

    // 其他辅助方法将在下一部分实现...
    
    private List<RecommendedPath> generateCareerBasedPaths(UserCareerGoal careerGoal, UserProfile userProfile) {
        // 实现基于职业目标的路径推荐
        return new ArrayList<>();
    }
    
    private List<RecommendedPath> generateSkillGapBasedPaths(UserProfile userProfile) {
        // 实现基于技能缺口的路径推荐
        return new ArrayList<>();
    }
    
    private List<RecommendedPath> generatePreferenceBasedPaths(UserProfile userProfile) {
        // 实现基于学习偏好的路径推荐
        return new ArrayList<>();
    }

    // 内部数据结构
    @Data
    public static class PersonalizedRecommendation {
        private Long userId;
        private UserProfile userProfile;
        private List<RecommendedPath> recommendedPaths;
        private List<RecommendedSkill> recommendedSkills;
        private List<LearningAdvice> learningAdvices;
        private LocalDateTime generatedAt;
    }

    @Data
    public static class UserProfile {
        private Long userId;
        private List<UserCareerGoal> careerGoals;
        private List<UserAtomicSkillMastery> skillMasteries;
        private SkillMasteryStats masteryStats;
        private List<UserLearningPathProgress> learningProgress;
        private LearningPreferences learningPreferences;
    }

    @Data
    public static class RecommendedPath {
        private Long pathId;
        private String pathName;
        private String description;
        private BigDecimal recommendationScore;
        private String recommendationReason;
        private String recommendationType;
        private int estimatedHours;
        private String difficultyLevel;
    }

    @Data
    public static class RecommendedSkill {
        private AtomicSkill skill;
        private BigDecimal recommendationScore;
        private String recommendationReason;
        private String recommendationType;
    }

    @Data
    public static class LearningAdvice {
        private String adviceType;
        private String title;
        private String description;
        private String priority;
        
        public LearningAdvice(String adviceType, String title, String description, String priority) {
            this.adviceType = adviceType;
            this.title = title;
            this.description = description;
            this.priority = priority;
        }
    }

    @Data
    public static class SkillMasteryStats {
        private int totalSkills;
        private int masteredSkills;
        private double averageMasteryLevel;
        private Map<String, Long> categoryDistribution;
    }

    @Data
    public static class LearningPreferences {
        private String preferredDifficulty;
        private String preferredCategory;
        private String learningActivity;
    }
}
