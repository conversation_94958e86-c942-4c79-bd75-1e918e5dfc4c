import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../hooks/useThemeColors';
import { tokens } from '../design-tokens';
import PrerequisiteAnalysisService, {
  PrerequisiteAnalysisResult,
  PrerequisiteStatistics,
  LearningPathStep,
} from '../services/PrerequisiteAnalysisService';

interface PrerequisiteAnalysisScreenProps {
  route: {
    params: {
      skillId: number;
      skillName?: string;
    };
  };
  navigation: any;
}

const PrerequisiteAnalysisScreen: React.FC<PrerequisiteAnalysisScreenProps> = ({
  route,
  navigation,
}) => {
  const colors = useThemeColors();
  const { skillId, skillName } = route.params;

  const [loading, setLoading] = useState(true);
  const [analysisResult, setAnalysisResult] = useState<PrerequisiteAnalysisResult | null>(null);
  const [statistics, setStatistics] = useState<PrerequisiteStatistics | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'path' | 'tree'>('overview');

  useEffect(() => {
    loadAnalysisData();
  }, [skillId]);

  const loadAnalysisData = async () => {
    try {
      setLoading(true);
      
      // 并行加载分析结果和统计信息
      const [analysisData, statsData] = await Promise.all([
        PrerequisiteAnalysisService.analyzePrerequisites(skillId),
        PrerequisiteAnalysisService.getPrerequisiteStatistics(skillId),
      ]);
      
      setAnalysisResult(analysisData);
      setStatistics(statsData);
    } catch (error) {
      console.error('加载前置技能分析数据失败:', error);
      Alert.alert('错误', '加载数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
        activeOpacity={0.7}
      >
        <Ionicons name="arrow-back" size={24} color={colors.text} />
      </TouchableOpacity>
      
      <View style={styles.headerContent}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          前置技能分析
        </Text>
        <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
          {analysisResult?.targetSkillName || skillName || `技能 ${skillId}`}
        </Text>
      </View>
      
      <TouchableOpacity
        style={styles.refreshButton}
        onPress={loadAnalysisData}
        activeOpacity={0.7}
      >
        <Ionicons name="refresh" size={24} color={colors.primary} />
      </TouchableOpacity>
    </View>
  );

  const renderTabs = () => (
    <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'overview' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('overview')}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.tabText,
          { color: activeTab === 'overview' ? colors.primary : colors.textSecondary }
        ]}>
          概览
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'path' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('path')}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.tabText,
          { color: activeTab === 'path' ? colors.primary : colors.textSecondary }
        ]}>
          学习路径
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'tree' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('tree')}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.tabText,
          { color: activeTab === 'tree' ? colors.primary : colors.textSecondary }
        ]}>
          依赖树
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOverview = () => {
    if (!statistics || !analysisResult) return null;

    return (
      <View style={styles.content}>
        {/* 统计面板 */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, { backgroundColor: colors.primary + '20' }]}>
            <Ionicons name="layers-outline" size={20} color={colors.primary} />
            <Text style={[styles.statLabel, { color: colors.primary }]}>前置技能</Text>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {statistics.totalPrerequisites}
            </Text>
          </View>
          
          <View style={[styles.statCard, { backgroundColor: colors.success + '20' }]}>
            <Ionicons name="time-outline" size={20} color={colors.success} />
            <Text style={[styles.statLabel, { color: colors.success }]}>学习时长</Text>
            <Text style={[styles.statValue, { color: colors.success }]}>
              {statistics.totalEstimatedHours}h
            </Text>
          </View>
          
          <View style={[styles.statCard, { backgroundColor: colors.warning + '20' }]}>
            <Ionicons name="trending-up-outline" size={20} color={colors.warning} />
            <Text style={[styles.statLabel, { color: colors.warning }]}>复杂度</Text>
            <Text style={[styles.statValue, { color: colors.warning }]}>
              {PrerequisiteAnalysisService.formatComplexity(statistics.complexity)}
            </Text>
          </View>
        </View>

        {/* 详细信息 */}
        <View style={[styles.infoCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>分析详情</Text>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>最大深度:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{statistics.maxDepth} 层</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>学习步骤:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{statistics.learningSteps} 步</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>循环依赖:</Text>
            <Text style={[
              styles.infoValue,
              { color: analysisResult.hasCycles ? colors.error : colors.success }
            ]}>
              {analysisResult.hasCycles ? '存在' : '无'}
            </Text>
          </View>
        </View>

        {/* 学习建议 */}
        <View style={[styles.adviceCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.adviceTitle, { color: colors.text }]}>学习建议</Text>
          {PrerequisiteAnalysisService.generateLearningAdvice(statistics).map((advice, index) => (
            <View key={index} style={styles.adviceItem}>
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              <Text style={[styles.adviceText, { color: colors.textSecondary }]}>{advice}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderLearningPath = () => {
    if (!analysisResult) return null;

    return (
      <View style={styles.content}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>推荐学习路径</Text>
        
        {analysisResult.recommendedPath.map((step, index) => (
          <View key={step.skillId} style={[styles.pathStep, { backgroundColor: colors.surface }]}>
            <View style={styles.stepHeader}>
              <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
                <Text style={[styles.stepNumberText, { color: colors.surface }]}>
                  {step.step}
                </Text>
              </View>
              
              <View style={styles.stepInfo}>
                <Text style={[styles.stepTitle, { color: colors.text }]}>
                  {step.skillName}
                </Text>
                <Text style={[styles.stepCategory, { color: colors.textSecondary }]}>
                  {step.category} · {PrerequisiteAnalysisService.getDifficultyText(step.difficultyLevel)}
                </Text>
              </View>
              
              <View style={styles.stepTime}>
                <Text style={[styles.stepHours, { color: colors.primary }]}>
                  {step.estimatedHours}h
                </Text>
              </View>
            </View>
            
            <Text style={[styles.stepReason, { color: colors.textSecondary }]}>
              {step.reason}
            </Text>
            
            {step.prerequisiteSkillIds.length > 0 && (
              <View style={styles.prerequisites}>
                <Text style={[styles.prerequisitesLabel, { color: colors.textSecondary }]}>
                  前置技能: {step.prerequisiteSkillIds.join(', ')}
                </Text>
              </View>
            )}
          </View>
        ))}
        
        <View style={[styles.pathSummary, { backgroundColor: colors.primary + '10' }]}>
          <Text style={[styles.pathSummaryText, { color: colors.primary }]}>
            总计 {analysisResult.recommendedPath.length} 个步骤，
            预计 {PrerequisiteAnalysisService.calculateTotalHours(analysisResult.recommendedPath)} 小时
          </Text>
        </View>
      </View>
    );
  };

  const renderDependencyTree = () => {
    if (!analysisResult) return null;

    const renderTreeNode = (node: any, depth: number = 0) => (
      <View key={`${node.skillId}-${depth}`} style={[styles.treeNode, { marginLeft: depth * 20 }]}>
        <View style={styles.treeNodeContent}>
          <Ionicons 
            name={depth === 0 ? "star" : "arrow-forward"} 
            size={16} 
            color={depth === 0 ? colors.primary : colors.textSecondary} 
          />
          <Text style={[styles.treeNodeText, { color: colors.text }]}>
            {node.skillName}
          </Text>
          <Text style={[styles.treeNodeLevel, { color: colors.textSecondary }]}>
            {PrerequisiteAnalysisService.getDifficultyText(node.difficultyLevel)}
          </Text>
        </View>
        
        {node.children && node.children.map((child: any) => renderTreeNode(child, depth + 1))}
      </View>
    );

    return (
      <View style={styles.content}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>技能依赖树</Text>
        
        {/* 根节点 */}
        <View style={[styles.treeContainer, { backgroundColor: colors.surface }]}>
          <View style={styles.treeNode}>
            <View style={styles.treeNodeContent}>
              <Ionicons name="star" size={16} color={colors.primary} />
              <Text style={[styles.treeNodeText, { color: colors.text }]}>
                {analysisResult.targetSkillName}
              </Text>
              <Text style={[styles.treeNodeLevel, { color: colors.primary }]}>目标技能</Text>
            </View>
          </View>
          
          {/* 前置技能树 */}
          {analysisResult.directPrerequisites.map(node => renderTreeNode(node, 1))}
        </View>
        
        {analysisResult.totalPrerequisites === 0 && (
          <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
            <Ionicons name="leaf-outline" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              这是基础技能，无需前置技能
            </Text>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          正在分析技能依赖...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}
      {renderTabs()}
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'path' && renderLearningPath()}
        {activeTab === 'tree' && renderDependencyTree()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing('md'),
    fontSize: tokens.fontSize('body'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: tokens.spacing('xs'),
  },
  headerContent: {
    flex: 1,
    marginLeft: tokens.spacing('sm'),
  },
  headerTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
  },
  headerSubtitle: {
    fontSize: tokens.fontSize('caption'),
    marginTop: 2,
  },
  refreshButton: {
    padding: tokens.spacing('xs'),
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: tokens.spacing('md'),
  },
  statsContainer: {
    flexDirection: 'row',
    gap: tokens.spacing('sm'),
    marginBottom: tokens.spacing('md'),
  },
  statCard: {
    flex: 1,
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    gap: tokens.spacing('xs'),
  },
  statLabel: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  statValue: {
    fontSize: tokens.fontSize('title-lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  infoCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('md'),
  },
  infoTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('sm'),
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: tokens.spacing('xs'),
  },
  infoLabel: {
    fontSize: tokens.fontSize('body'),
  },
  infoValue: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
  adviceCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  adviceTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('sm'),
  },
  adviceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: tokens.spacing('xs'),
    gap: tokens.spacing('xs'),
  },
  adviceText: {
    flex: 1,
    fontSize: tokens.fontSize('body'),
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  pathStep: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepNumberText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('bold'),
  },
  stepInfo: {
    flex: 1,
    marginLeft: tokens.spacing('sm'),
  },
  stepTitle: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
  stepCategory: {
    fontSize: tokens.fontSize('caption'),
    marginTop: 2,
  },
  stepTime: {
    alignItems: 'flex-end',
  },
  stepHours: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('bold'),
  },
  stepReason: {
    fontSize: tokens.fontSize('caption'),
    marginTop: tokens.spacing('xs'),
  },
  prerequisites: {
    marginTop: tokens.spacing('xs'),
  },
  prerequisitesLabel: {
    fontSize: tokens.fontSize('caption'),
  },
  pathSummary: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    marginTop: tokens.spacing('md'),
  },
  pathSummaryText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
    textAlign: 'center',
  },
  treeContainer: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  treeNode: {
    marginBottom: tokens.spacing('xs'),
  },
  treeNodeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing('xs'),
  },
  treeNodeText: {
    flex: 1,
    fontSize: tokens.fontSize('body'),
  },
  treeNodeLevel: {
    fontSize: tokens.fontSize('caption'),
  },
  emptyState: {
    padding: tokens.spacing('xl'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    gap: tokens.spacing('md'),
  },
  emptyStateText: {
    fontSize: tokens.fontSize('body'),
    textAlign: 'center',
  },
});

export default PrerequisiteAnalysisScreen;
