import React, { useEffect } from 'react';
import { ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import {
  View,
  Text,
  Card,
  Button
} from 'react-native-ui-lib';
import { Avatar } from '../components/common/CachedImage';
import { useFocusEffect } from '@react-navigation/native';

import { MainTabScreenProps } from '../navigation/types';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../components/common/Toast';
import { useThemeColors } from '../contexts/ThemeContext';
import { tokens, createStyle } from '../design-tokens';

type Props = MainTabScreenProps<'Profile'>;

export const ProfileScreen: React.FC<Props> = ({ navigation }) => {
  const { isAuthenticated, user, logout, loadStoredAuth } = useAuth();
  const { showSuccess } = useToast();
  // 使用主题颜色
  const colors = useThemeColors();

  // 功能分组数据
  const functionGroups = [
    {
      title: '学习管理',
      icon: 'school-outline',
      color: colors.primary,
      functions: [
        { icon: 'book-outline', title: '我的课程', onPress: () => {} },
        { icon: 'document-text-outline', title: '学习笔记', onPress: () => navigation.navigate('Notes', { userId: user?.id || 'guest' }) },
        { icon: 'calendar-outline', title: '学习计划', onPress: () => navigation.navigate('LearningPlan', { userId: user?.id || 'guest' }) },
        { icon: 'analytics-outline', title: '学习仪表板', onPress: () => navigation.navigate('LearningDashboard', { userId: user?.id || 'guest' }) },
      ]
    },
    {
      title: '职业发展',
      icon: 'rocket-outline',
      color: colors.success,
      functions: [
        { icon: 'rocket-outline', title: '职业目标', onPress: () => navigation.navigate('CareerManagement') },
        { icon: 'analytics-outline', title: '技能评估', onPress: () => navigation.navigate('UserSkillAssessment') },
        { icon: 'school-outline', title: '面试准备', onPress: () => navigation.navigate('InterviewPreparation') },
        { icon: 'briefcase-outline', title: '我的作品', onPress: () => navigation.navigate('MyPortfolio') },
      ]
    },
    {
      title: '个人中心',
      icon: 'person-outline',
      color: colors.warning,
      functions: [
        { icon: 'person-outline', title: '用户画像管理', onPress: () => navigation.navigate('UserProfileManagement') },
        { icon: 'analytics-outline', title: '学习画像分析', onPress: () => navigation.navigate('UserProfileAnalysis') },
        { icon: 'star-outline', title: '个性化评估', onPress: () => navigation.navigate('PersonalizedAssessment') },
        { icon: 'trophy-outline', title: '成就中心', onPress: () => navigation.navigate('AchievementCenter') },
        { icon: 'bookmark-outline', title: '我的收藏', onPress: () => {} },
      ]
    },
    {
      title: '工具与设置',
      icon: 'settings-outline',
      color: colors.accent,
      functions: [
        { icon: 'document-text-outline', title: '作品集生成', onPress: () => navigation.navigate('PortfolioGeneration') },
        { icon: 'code-working-outline', title: '在线编程', onPress: () => navigation.navigate('CodeEditor', { title: '在线代码编辑器', initialCode: '// 欢迎使用ITBook在线代码编辑器\nconsole.log("Hello, ITBook!");' }) },
        { icon: 'people-outline', title: '我的社区', onPress: () => navigation.navigate('UserProfile', { userId: user?.id || 'guest', isCurrentUser: true }) },
        { icon: 'color-palette-outline', title: '主题设置', onPress: () => navigation.navigate('ThemeSettings') },
      ]
    }
  ];

  // 页面获得焦点时重新加载认证状态
  useFocusEffect(
    React.useCallback(() => {
      loadStoredAuth();
    }, [loadStoredAuth])
  );

  const handleLogout = async () => {
    const result = await logout();
    if (result.success) {
      showSuccess('已成功登出');
    }
  };

  // 未登录状态
  if (!isAuthenticated) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
        <View flex backgroundColor={colors.surface}>
          {/* Header */}
          <View paddingH-16 paddingV-12 backgroundColor={colors.background}>
            <Text text20 $textSemiBold color={colors.text}>
              个人中心
            </Text>
          </View>

          {/* 登录提示 */}
          <View flex center padding-24>
            <View
              width={120}
              height={120}
              borderRadius={60}
              backgroundColor={colors.surface}
              center
              marginB-24
            >
              <Ionicons name="person-outline" size={60} color={colors.textSecondary} />
            </View>

            <Text text18 $textMedium color={colors.text} marginB-8>
              欢迎来到ITBook
            </Text>
            <Text text14 color={colors.textSecondary} center marginB-32>
              登录后可以查看学习进度、收藏课程、参与问答等更多功能
            </Text>

            <Button
              label="立即登录"
              backgroundColor={colors.primary}
              color={colors.background}
              borderRadius={8}
              paddingH-32
              onPress={() => navigation.navigate('Auth', { screen: 'Login' })}
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // 已登录状态
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <ScrollView showsVerticalScrollIndicator={false} style={{ backgroundColor: colors.background }}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.background }]}>
          <View style={styles.headerContent}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              个人中心
            </Text>
            <TouchableOpacity
              onPress={handleLogout}
              style={styles.headerButton}
              activeOpacity={0.7}
            >
              <Ionicons name="log-out-outline" size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
          {/* 用户信息区域 */}
          <View style={styles.userInfoSection}>
            {/* 用户基本信息 */}
            <View style={styles.userBasicInfo}>
              <Avatar
                uri={user?.avatar}
                size={80}
                name={user?.nickname || user?.username || 'U'}
                style={styles.avatar}
              />
              <View style={styles.userDetails}>
                <View style={styles.userNameRow}>
                  <Text style={[styles.userName, { color: colors.text }]}>
                    {user?.nickname || user?.username || '用户'}
                  </Text>
                  <TouchableOpacity
                    style={[styles.editButton, { backgroundColor: colors.primary + '20' }]}
                    onPress={() => navigation.navigate('EditProfile')}
                    activeOpacity={0.7}
                  >
                    <Ionicons name="create-outline" size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>
                <Text style={[styles.userBio, { color: colors.textSecondary }]}>
                  {(user as any)?.bio || '这个人很懒，什么都没留下'}
                </Text>
              </View>
            </View>

          </View>

          {/* 统计数据面板 */}
          <View style={styles.statsContainer}>
            <View style={[styles.statItem, { backgroundColor: colors.primary + '20' }]}>
              <View style={styles.statHeader}>
                <Ionicons name="book-outline" size={20} color={colors.primary} />
                <Text style={[styles.statLabel, { color: colors.primary }]}>已完成课程</Text>
              </View>
              <Text style={[styles.statValue, { color: colors.primary }]}>12</Text>
            </View>

            <View style={[styles.statItem, { backgroundColor: colors.success + '20' }]}>
              <View style={styles.statHeader}>
                <Ionicons name="time-outline" size={20} color={colors.success} />
                <Text style={[styles.statLabel, { color: colors.success }]}>学习时长</Text>
              </View>
              <Text style={[styles.statValue, { color: colors.success }]}>156h</Text>
            </View>

            <View style={[styles.statItem, { backgroundColor: colors.warning + '20' }]}>
              <View style={styles.statHeader}>
                <Ionicons name="flame-outline" size={20} color={colors.warning} />
                <Text style={[styles.statLabel, { color: colors.warning }]}>连续天数</Text>
              </View>
              <Text style={[styles.statValue, { color: colors.warning }]}>7</Text>
            </View>
          </View>

          {/* 功能菜单分组 */}
          <View style={styles.functionGroupsContainer}>
            {functionGroups.map((group, groupIndex) => (
              <View key={group.title} style={[styles.functionGroupCard, { backgroundColor: colors.surface }]}>
                {/* 分组标题 */}
                <View style={styles.groupHeader}>
                  <View style={[styles.groupIconContainer, { backgroundColor: group.color + '20' }]}>
                    <Ionicons name={group.icon as any} size={20} color={group.color} />
                  </View>
                  <Text style={[styles.groupTitle, { color: colors.text }]}>{group.title}</Text>
                </View>

                {/* 功能网格 */}
                <View style={styles.functionsGrid}>
                  {group.functions.map((func, funcIndex) => (
                    <TouchableOpacity
                      key={func.title}
                      style={styles.functionItem}
                      onPress={func.onPress}
                      activeOpacity={0.7}
                    >
                      <View style={[styles.functionIconContainer, { backgroundColor: colors.background }]}>
                        <Ionicons name={func.icon as any} size={20} color={group.color} />
                      </View>
                      <Text style={[styles.functionTitle, { color: colors.text }]} numberOfLines={2}>
                        {func.title}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            ))}

          </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// 样式定义
const styles = StyleSheet.create({
  // Header样式
  header: {
    ...createStyle.spacing({ paddingHorizontal: 'md', paddingVertical: 'sm' }),
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    ...createStyle.font('title-sm', 'bold'),
  },
  headerButton: {
    padding: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
  },

  // 用户信息区域样式
  userInfoSection: {
    paddingHorizontal: tokens.spacing('md'),
    paddingTop: tokens.spacing('md'),
    paddingBottom: tokens.spacing('sm'),
  },
  userBasicInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  avatar: {
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  userDetails: {
    flex: 1,
    marginLeft: 16,
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  userName: {
    ...createStyle.font('body-lg', 'semibold'),
    marginRight: tokens.spacing('sm'),
  },
  editButton: {
    width: 28,
    height: 28,
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: tokens.spacing('xs'),
  },
  userBio: {
    ...createStyle.font('body-sm'),
  },

  // 统计数据样式
  statsContainer: {
    flexDirection: 'row',
    gap: tokens.spacing('sm'),
    paddingHorizontal: tokens.spacing('md'),
    marginBottom: tokens.spacing('md'),
  },
  statItem: {
    flex: 1,
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  statLabel: {
    ...createStyle.font('caption', 'medium'),
    marginLeft: tokens.spacing('xs'),
  },
  statValue: {
    ...createStyle.font('title', 'bold'),
  },

  // 功能分组样式
  functionGroupsContainer: {
    paddingHorizontal: tokens.spacing('md'), // 使用与求职板块一致的16px边距
    marginBottom: tokens.spacing('xl'),
  },
  functionGroupCard: {
    ...createStyle.spacing({ padding: 'md', marginBottom: 'md' }),
    borderRadius: tokens.radius('md'),
    ...tokens.shadow('sm'),
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  groupIconContainer: {
    width: 32,
    height: 32,
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: tokens.spacing('sm'),
  },
  groupTitle: {
    ...createStyle.font('body', 'semibold'),
  },
  functionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: tokens.spacing('xs'), // 减小间距以适应4个项目
  },
  functionItem: {
    flex: 1, // 平分宽度，一行4个
    alignItems: 'center',
    padding: tokens.spacing('xs'), // 减小内边距
    borderRadius: tokens.radius('sm'),
    minWidth: 0, // 确保flex正常工作
  },
  functionIconContainer: {
    width: 40, // 减小图标容器尺寸
    height: 40,
    borderRadius: tokens.radius('sm'), // 使用较小的圆角
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: tokens.spacing('xs'),
    ...tokens.shadow('xs'),
  },
  functionTitle: {
    ...createStyle.font('caption', 'medium'),
    textAlign: 'center',
    fontSize: 11, // 稍微减小字体以适应布局
    lineHeight: 14, // 调整行高
  },
});

