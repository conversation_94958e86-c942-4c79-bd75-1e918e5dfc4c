package com.itbook.migration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据验证结果类
 * 记录数据验证的详细结果
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
public class ValidationResult {

    /**
     * 验证是否通过
     */
    private boolean valid;

    /**
     * 成功消息
     */
    private String message;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误列表
     */
    private List<String> errors;

    /**
     * 警告列表
     */
    private List<String> warnings;

    /**
     * 验证时间
     */
    private LocalDateTime validationTime;

    /**
     * 验证耗时（毫秒）
     */
    private long validationDurationMs;

    /**
     * 验证的详细项目
     */
    private List<ValidationItem> validationItems;

    // 构造函数
    public ValidationResult() {
        this.valid = false;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.validationItems = new ArrayList<>();
        this.validationTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public List<String> getWarnings() {
        return warnings;
    }

    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
    }

    public LocalDateTime getValidationTime() {
        return validationTime;
    }

    public void setValidationTime(LocalDateTime validationTime) {
        this.validationTime = validationTime;
    }

    public long getValidationDurationMs() {
        return validationDurationMs;
    }

    public void setValidationDurationMs(long validationDurationMs) {
        this.validationDurationMs = validationDurationMs;
    }

    public List<ValidationItem> getValidationItems() {
        return validationItems;
    }

    public void setValidationItems(List<ValidationItem> validationItems) {
        this.validationItems = validationItems;
    }

    /**
     * 添加错误
     */
    public void addError(String error) {
        this.errors.add(error);
        this.valid = false;
    }

    /**
     * 添加警告
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }

    /**
     * 添加验证项目
     */
    public void addValidationItem(String itemName, boolean passed, String description) {
        ValidationItem item = new ValidationItem();
        item.setItemName(itemName);
        item.setPassed(passed);
        item.setDescription(description);
        item.setValidationTime(LocalDateTime.now());
        this.validationItems.add(item);
    }

    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors.size();
    }

    /**
     * 获取警告数量
     */
    public int getWarningCount() {
        return warnings.size();
    }

    /**
     * 获取验证项目总数
     */
    public int getTotalValidationItems() {
        return validationItems.size();
    }

    /**
     * 获取通过的验证项目数
     */
    public int getPassedValidationItems() {
        return (int) validationItems.stream().mapToLong(item -> item.isPassed() ? 1 : 0).sum();
    }

    /**
     * 获取验证通过率
     */
    public double getPassRate() {
        if (validationItems.isEmpty()) {
            return 0.0;
        }
        return (double) getPassedValidationItems() / getTotalValidationItems() * 100;
    }

    /**
     * 获取格式化的验证时长
     */
    public String getFormattedValidationDuration() {
        if (validationDurationMs < 1000) {
            return validationDurationMs + "ms";
        } else {
            return String.format("%.2fs", validationDurationMs / 1000.0);
        }
    }

    /**
     * 获取验证摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("验证结果: ").append(valid ? "通过" : "失败").append("\n");
        summary.append("验证时间: ").append(validationTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        summary.append("验证耗时: ").append(getFormattedValidationDuration()).append("\n");
        summary.append("验证项目: ").append(getPassedValidationItems()).append("/").append(getTotalValidationItems()).append(" 通过\n");
        summary.append("通过率: ").append(String.format("%.1f%%", getPassRate())).append("\n");
        summary.append("错误数量: ").append(getErrorCount()).append("\n");
        summary.append("警告数量: ").append(getWarningCount()).append("\n");
        
        if (!errors.isEmpty()) {
            summary.append("\n错误详情:\n");
            for (int i = 0; i < errors.size(); i++) {
                summary.append("  ").append(i + 1).append(". ").append(errors.get(i)).append("\n");
            }
        }
        
        if (!warnings.isEmpty()) {
            summary.append("\n警告详情:\n");
            for (int i = 0; i < warnings.size(); i++) {
                summary.append("  ").append(i + 1).append(". ").append(warnings.get(i)).append("\n");
            }
        }
        
        return summary.toString();
    }

    /**
     * 获取详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 数据验证详细报告 ===\n\n");
        report.append(getSummary());
        
        if (!validationItems.isEmpty()) {
            report.append("\n=== 验证项目详情 ===\n");
            for (ValidationItem item : validationItems) {
                report.append(String.format("%-20s: %s - %s\n", 
                    item.getItemName(), 
                    item.isPassed() ? "通过" : "失败", 
                    item.getDescription()));
            }
        }
        
        return report.toString();
    }

    @Override
    public String toString() {
        return "ValidationResult{" +
                "valid=" + valid +
                ", errorCount=" + getErrorCount() +
                ", warningCount=" + getWarningCount() +
                ", passRate=" + String.format("%.1f%%", getPassRate()) +
                ", validationTime=" + (validationTime != null ? 
                    validationTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "null") +
                ", duration='" + getFormattedValidationDuration() + '\'' +
                '}';
    }

    /**
     * 验证项目内部类
     */
    public static class ValidationItem {
        private String itemName;
        private boolean passed;
        private String description;
        private LocalDateTime validationTime;
        private String errorMessage;

        // Getter和Setter方法
        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public boolean isPassed() {
            return passed;
        }

        public void setPassed(boolean passed) {
            this.passed = passed;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public LocalDateTime getValidationTime() {
            return validationTime;
        }

        public void setValidationTime(LocalDateTime validationTime) {
            this.validationTime = validationTime;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "ValidationItem{" +
                    "itemName='" + itemName + '\'' +
                    ", passed=" + passed +
                    ", description='" + description + '\'' +
                    (errorMessage != null ? ", errorMessage='" + errorMessage + '\'' : "") +
                    '}';
        }
    }
}
