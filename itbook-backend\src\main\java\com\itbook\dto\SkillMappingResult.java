package com.itbook.dto;

/**
 * 技能映射结果DTO
 * 用于返回技能映射执行的统计结果
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
public class SkillMappingResult {

    /**
     * 从career_skill生成的原子技能数量
     */
    private int careerBasedSkillCount;

    /**
     * 从learning_path生成的原子技能数量
     */
    private int pathBasedSkillCount;

    /**
     * 从course生成的原子技能数量
     */
    private int courseBasedSkillCount;

    /**
     * 合并后的原子技能总数
     */
    private int totalAtomicSkillCount;

    /**
     * 创建的映射关系数量
     */
    private int mappingCount;

    /**
     * 高置信度映射数量（>= 0.8）
     */
    private int highConfidenceMappings;

    /**
     * 中等置信度映射数量（0.6 - 0.8）
     */
    private int mediumConfidenceMappings;

    /**
     * 低置信度映射数量（0.4 - 0.6）
     */
    private int lowConfidenceMappings;

    /**
     * 平均置信度分数
     */
    private double averageConfidence;

    /**
     * 映射执行开始时间
     */
    private String startTime;

    /**
     * 映射执行结束时间
     */
    private String endTime;

    /**
     * 映射执行耗时（毫秒）
     */
    private long executionTimeMs;

    /**
     * 映射执行状态
     */
    private String status;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    // 构造函数
    public SkillMappingResult() {
        this.status = "SUCCESS";
    }

    // Getter和Setter方法
    public int getCareerBasedSkillCount() {
        return careerBasedSkillCount;
    }

    public void setCareerBasedSkillCount(int careerBasedSkillCount) {
        this.careerBasedSkillCount = careerBasedSkillCount;
    }

    public int getPathBasedSkillCount() {
        return pathBasedSkillCount;
    }

    public void setPathBasedSkillCount(int pathBasedSkillCount) {
        this.pathBasedSkillCount = pathBasedSkillCount;
    }

    public int getCourseBasedSkillCount() {
        return courseBasedSkillCount;
    }

    public void setCourseBasedSkillCount(int courseBasedSkillCount) {
        this.courseBasedSkillCount = courseBasedSkillCount;
    }

    public int getTotalAtomicSkillCount() {
        return totalAtomicSkillCount;
    }

    public void setTotalAtomicSkillCount(int totalAtomicSkillCount) {
        this.totalAtomicSkillCount = totalAtomicSkillCount;
    }

    public int getMappingCount() {
        return mappingCount;
    }

    public void setMappingCount(int mappingCount) {
        this.mappingCount = mappingCount;
    }

    public int getHighConfidenceMappings() {
        return highConfidenceMappings;
    }

    public void setHighConfidenceMappings(int highConfidenceMappings) {
        this.highConfidenceMappings = highConfidenceMappings;
    }

    public int getMediumConfidenceMappings() {
        return mediumConfidenceMappings;
    }

    public void setMediumConfidenceMappings(int mediumConfidenceMappings) {
        this.mediumConfidenceMappings = mediumConfidenceMappings;
    }

    public int getLowConfidenceMappings() {
        return lowConfidenceMappings;
    }

    public void setLowConfidenceMappings(int lowConfidenceMappings) {
        this.lowConfidenceMappings = lowConfidenceMappings;
    }

    public double getAverageConfidence() {
        return averageConfidence;
    }

    public void setAverageConfidence(double averageConfidence) {
        this.averageConfidence = averageConfidence;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public long getExecutionTimeMs() {
        return executionTimeMs;
    }

    public void setExecutionTimeMs(long executionTimeMs) {
        this.executionTimeMs = executionTimeMs;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 计算映射质量分数（0-100）
     */
    public double getQualityScore() {
        if (mappingCount == 0) {
            return 0.0;
        }
        
        double highWeight = 1.0;
        double mediumWeight = 0.7;
        double lowWeight = 0.4;
        
        double weightedScore = (highConfidenceMappings * highWeight + 
                               mediumConfidenceMappings * mediumWeight + 
                               lowConfidenceMappings * lowWeight) / mappingCount;
        
        return weightedScore * 100;
    }

    /**
     * 获取映射覆盖率（原子技能数量 / 原始技能数量）
     */
    public double getCoverageRate() {
        int originalSkillCount = careerBasedSkillCount; // 假设以career_skill为基准
        if (originalSkillCount == 0) {
            return 0.0;
        }
        return (double) totalAtomicSkillCount / originalSkillCount;
    }

    /**
     * 获取去重效率（去重前总数 / 去重后总数）
     */
    public double getDeduplicationEfficiency() {
        int totalBeforeDedup = careerBasedSkillCount + pathBasedSkillCount + courseBasedSkillCount;
        if (totalBeforeDedup == 0) {
            return 0.0;
        }
        return 1.0 - (double) totalAtomicSkillCount / totalBeforeDedup;
    }

    @Override
    public String toString() {
        return "SkillMappingResult{" +
                "careerBasedSkillCount=" + careerBasedSkillCount +
                ", pathBasedSkillCount=" + pathBasedSkillCount +
                ", courseBasedSkillCount=" + courseBasedSkillCount +
                ", totalAtomicSkillCount=" + totalAtomicSkillCount +
                ", mappingCount=" + mappingCount +
                ", highConfidenceMappings=" + highConfidenceMappings +
                ", mediumConfidenceMappings=" + mediumConfidenceMappings +
                ", lowConfidenceMappings=" + lowConfidenceMappings +
                ", averageConfidence=" + String.format("%.3f", averageConfidence) +
                ", qualityScore=" + String.format("%.2f", getQualityScore()) +
                ", coverageRate=" + String.format("%.2f", getCoverageRate()) +
                ", deduplicationEfficiency=" + String.format("%.2f", getDeduplicationEfficiency()) +
                ", status='" + status + '\'' +
                ", executionTimeMs=" + executionTimeMs +
                '}';
    }
}
