# ITBook开发指南

## 🚀 快速开始

### 环境要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Expo CLI**: >= 6.0.0
- **Git**: >= 2.30.0

### 安装步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd itbook-mobile

# 2. 安装依赖
npm install

# 3. 环境配置
npm run env:dev      # 切换到开发环境
npm run env:production # 切换到生产环境

# 4. 启动开发服务器
npm run web:dev      # Web开发 (Dev环境)
npm run start:dev    # 移动端开发 (Dev环境)
npm run android      # Android开发
npm run ios          # iOS开发
```

### 环境配置说明
ITBook项目支持两套环境配置：

#### Dev环境 (开发测试)
- 服务端口：8888
- 前端端口：8083
- 数据库：itbook_dev
- API地址：http://localhost:8888/api
- 调试模式：启用
- API文档：启用

#### Production环境 (生产部署)
- 服务端口：8888
- 数据库：itbook_prod
- API地址：https://api.itbook.com/api
- 调试模式：禁用
- API文档：禁用

### 前端开发工具配置
```bash
# 安装Expo CLI (如果未安装)
npm install -g @expo/cli

# 安装开发工具
npm install -g typescript
npm install -g eslint
npm install -g prettier
```

### 后端开发环境配置
```bash
# Java环境要求
Java: JDK 1.8+
Maven: 3.6+
MySQL: 8.0+

# 数据库配置
mysql -u root -p
CREATE DATABASE itbook_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE itbook_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 启动后端服务
cd itbook-backend
mvn spring-boot:run -Pdev

# 验证服务启动
curl http://localhost:8888/api/health
```

### API文档访问
```bash
# 开发环境API文档
http://localhost:8888/api/swagger-ui.html

# API健康检查
http://localhost:8888/api/health

# 完整API文档
参考: API_DOCUMENTATION.md
参考: API_QUICK_REFERENCE.md
```

## 📋 开发规范

### 代码规范
```typescript
// 1. 文件命名规范
// 组件文件: PascalCase.tsx
// 工具文件: camelCase.ts
// 常量文件: UPPER_CASE.ts

// 2. 组件定义规范
interface ComponentProps {
  title: string;
  onPress?: () => void;
  disabled?: boolean;
}

export const Component: React.FC<ComponentProps> = ({
  title,
  onPress,
  disabled = false,
}) => {
  // 组件实现
};

// 3. 类型定义规范
export interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// 4. 常量定义规范
export const API_ENDPOINTS = {
  USERS: '/api/users',
  COURSES: '/api/courses',
} as const;
```

### Git工作流
```bash
# 1. 功能开发流程
git checkout -b feature/new-feature
# 开发功能...
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
# 创建Pull Request

# 2. 提交信息规范
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

### 目录结构规范
```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── forms/          # 表单组件
│   └── [module]/       # 模块特定组件
├── screens/            # 页面组件
│   └── [module]/       # 按模块组织
├── navigation/         # 导航配置
├── types/             # TypeScript类型
├── data/              # 模拟数据
├── hooks/             # 自定义Hooks
├── utils/             # 工具函数
├── constants/         # 常量定义
└── assets/            # 静态资源
```

## 🛠️ 开发工具

### VSCode配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "typescript": "typescriptreact"
  }
}

// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss"
  ]
}
```

### 调试配置
```typescript
// 开发环境调试
if (__DEV__) {
  console.log('Debug info:', data);
}

// 性能监控
const startTime = performance.now();
// 执行代码...
const endTime = performance.now();
console.log(`执行时间: ${endTime - startTime}ms`);

// 错误边界
export class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: any) {
    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

## 🧪 测试策略

### 单元测试
```typescript
// __tests__/components/AchievementCard.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { AchievementCard } from '../AchievementCard';

describe('AchievementCard', () => {
  const mockAchievement = {
    id: '1',
    title: 'Test Achievement',
    description: 'Test Description',
    // ... 其他属性
  };

  it('renders correctly', () => {
    const { getByText } = render(
      <AchievementCard userAchievement={mockAchievement} />
    );
    
    expect(getByText('Test Achievement')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
  });

  it('handles press events', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <AchievementCard 
        userAchievement={mockAchievement} 
        onPress={onPress}
      />
    );
    
    fireEvent.press(getByTestId('achievement-card'));
    expect(onPress).toHaveBeenCalledWith(mockAchievement);
  });
});
```

### 集成测试
```typescript
// __tests__/screens/AchievementCenter.test.tsx
import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import { AchievementCenterScreen } from '../AchievementCenterScreen';

describe('AchievementCenterScreen', () => {
  it('loads and displays achievements', async () => {
    const { getByText } = render(<AchievementCenterScreen />);
    
    await waitFor(() => {
      expect(getByText('个人成就概览')).toBeTruthy();
    });
  });
});
```

## 📱 平台特定开发

### Web平台优化
```typescript
// 检测平台
import { Platform } from 'react-native';

const isWeb = Platform.OS === 'web';
const isNative = Platform.OS !== 'web';

// Web特定样式
const webStyles = StyleSheet.create({
  container: {
    ...Platform.select({
      web: {
        maxWidth: 1200,
        marginHorizontal: 'auto',
      },
      default: {},
    }),
  },
});

// Web特定功能
if (Platform.OS === 'web') {
  // Web特定代码
  document.title = 'ITBook学习平台';
}
```

### 移动端优化
```typescript
// 安全区域处理
import { SafeAreaView } from 'react-native-safe-area-context';

const MobileScreen = () => (
  <SafeAreaView style={{ flex: 1 }}>
    {/* 内容 */}
  </SafeAreaView>
);

// 键盘处理
import { KeyboardAvoidingView, Platform } from 'react-native';

const FormScreen = () => (
  <KeyboardAvoidingView
    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    style={{ flex: 1 }}
  >
    {/* 表单内容 */}
  </KeyboardAvoidingView>
);
```

## 📱 离线功能开发指南

### 离线功能架构
```typescript
// 1. 使用离线功能Hook
import { useOffline } from '../hooks/useOffline';

const MyComponent: React.FC = () => {
  const {
    isOnline,
    isOfflineMode,
    cacheContent,
    getCachedContent,
    isContentCached,
  } = useOffline();

  // 缓存内容
  const handleCacheContent = async () => {
    const cacheItem = {
      id: 'content-1',
      type: 'article' as const,
      title: '文章标题',
      priority: 'medium' as const,
    };

    const success = await cacheContent(cacheItem, '文章内容...');
    if (success) {
      console.log('内容已缓存');
    }
  };

  return (
    <View>
      {isOnline ? (
        <Text>在线模式</Text>
      ) : (
        <Text>离线模式</Text>
      )}
    </View>
  );
};
```

### 网络状态监控
```typescript
// 2. 网络状态检测
import { useNetworkStatus } from '../hooks/useNetworkStatus';

const NetworkIndicator: React.FC = () => {
  const { isOnline, networkStatus } = useNetworkStatus();

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      <View
        style={{
          width: 8,
          height: 8,
          borderRadius: 4,
          backgroundColor: isOnline ? '#4CAF50' : '#F44336',
        }}
      />
      <Text style={{ marginLeft: 8 }}>
        {isOnline ? '在线' : '离线'}
      </Text>
    </View>
  );
};
```

### 缓存管理最佳实践
```typescript
// 3. 缓存管理服务
import CacheManager from '../services/CacheManager';

class ContentService {
  private cacheManager = CacheManager.getInstance();

  // 智能缓存推荐
  async getRecommendations() {
    return await this.cacheManager.getSmartCacheRecommendations();
  }

  // 批量缓存
  async batchCache(items: CacheItem[]) {
    await this.cacheManager.batchCacheContent(items);
  }

  // 获取缓存统计
  async getCacheStats() {
    return await this.cacheManager.getCacheStats();
  }
}
```

### 离线数据同步
```typescript
// 4. 离线操作记录
import { useOffline } from '../hooks/useOffline';

const useOfflineOperations = () => {
  const { recordOfflineOperation, syncOfflineOperations } = useOffline();

  // 记录离线操作
  const recordProgress = async (contentId: string, progress: number) => {
    await recordOfflineOperation('progress', contentId, { progress });
  };

  // 记录笔记
  const recordNote = async (contentId: string, note: string) => {
    await recordOfflineOperation('note', contentId, { note });
  };

  return {
    recordProgress,
    recordNote,
    syncOfflineOperations,
  };
};
```

### 离线UI组件开发
```typescript
// 5. 离线状态组件
interface OfflineIndicatorProps {
  showCacheInfo?: boolean;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  showCacheInfo = false
}) => {
  const { isOnline, cacheStats } = useOffline();

  if (isOnline && !showCacheInfo) return null;

  return (
    <View style={styles.offlineIndicator}>
      <Ionicons
        name={isOnline ? 'wifi' : 'wifi-off'}
        size={16}
        color={isOnline ? '#4CAF50' : '#F44336'}
      />
      <Text style={styles.statusText}>
        {isOnline ? '在线' : '离线模式'}
      </Text>
      {showCacheInfo && (
        <Text style={styles.cacheInfo}>
          已缓存 {cacheStats.totalItems} 项
        </Text>
      )}
    </View>
  );
};
```

### 离线功能测试
```typescript
// 6. 离线功能测试
describe('离线功能测试', () => {
  test('应该能够缓存内容', async () => {
    const cacheManager = CacheManager.getInstance();
    const item = {
      id: 'test-1',
      type: 'article' as const,
      title: '测试文章',
      priority: 'high' as const,
    };

    const success = await cacheManager.cacheContent(item, '测试内容');
    expect(success).toBe(true);

    const cached = await cacheManager.isContentCached('test-1');
    expect(cached).toBe(true);
  });

  test('应该能够检测网络状态', () => {
    const { result } = renderHook(() => useNetworkStatus());
    expect(result.current.isOnline).toBeDefined();
  });
});
```

## 🔧 常见问题解决

### 依赖问题
```bash
# 清理缓存
npm start -- --clear
expo start -c

# 重新安装依赖
rm -rf node_modules
rm package-lock.json
npm install

# 修复Metro缓存问题
npx react-native start --reset-cache
```

### 构建问题
```bash
# Android构建问题
cd android
./gradlew clean
cd ..
npx react-native run-android

# iOS构建问题
cd ios
rm -rf build
pod install
cd ..
npx react-native run-ios
```

### 性能问题
```typescript
// 避免在render中创建对象
// ❌ 错误做法
const Component = () => (
  <View style={{ flex: 1, padding: 16 }}>
    {/* 内容 */}
  </View>
);

// ✅ 正确做法
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
});

const Component = () => (
  <View style={styles.container}>
    {/* 内容 */}
  </View>
);

// 使用useMemo优化计算
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 使用useCallback优化函数
const handlePress = useCallback(() => {
  onPress();
}, [onPress]);
```

## 📚 学习资源

### 官方文档
- [React Native官方文档](https://reactnative.dev/)
- [Expo官方文档](https://docs.expo.dev/)
- [React Navigation文档](https://reactnavigation.org/)
- [TypeScript官方文档](https://www.typescriptlang.org/)

### 推荐工具
- **开发工具**: VS Code, Flipper, React DevTools
- **设计工具**: Figma, Sketch, Adobe XD
- **测试工具**: Jest, Detox, Maestro
- **调试工具**: Chrome DevTools, React Native Debugger

### 社区资源
- [React Native社区](https://github.com/react-native-community)
- [Expo社区](https://forums.expo.dev/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native)

## 🚀 部署指南

### Web部署
```bash
# 构建Web版本
npm run build:web

# 部署到静态托管服务
# Vercel, Netlify, GitHub Pages等
```

### 移动端发布
```bash
# Android发布
expo build:android

# iOS发布
expo build:ios

# 使用EAS Build (推荐)
eas build --platform android
eas build --platform ios
```

## 📞 支持与反馈

### 问题报告
- 在GitHub Issues中报告bug
- 提供详细的复现步骤
- 包含错误日志和截图

### 功能请求
- 在GitHub Discussions中讨论新功能
- 提供详细的需求描述
- 说明使用场景和价值

### 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request
5. 等待代码审查

## 🎯 职业导向功能开发指南

### 职业目标数据模型开发

```typescript
// 1. 定义职业相关类型
// 文件: src/types/career.ts
interface CareerGoal {
  id: number;
  name: string;
  category: string;
  // ... 其他属性
}

// 2. 创建职业目标配置数据
// 文件: src/data/careerGoals.ts
export const javaBackendEngineer: CareerGoal = {
  id: 1,
  name: 'Java后端工程师',
  // ... 配置详情
};
```

### 服务层开发规范

```typescript
// 1. 服务类设计模式
class CareerService {
  private static instance: CareerService;

  public static getInstance(): CareerService {
    if (!CareerService.instance) {
      CareerService.instance = new CareerService();
    }
    return CareerService.instance;
  }

  // 异步方法使用Promise
  async getCareerById(careerId: number): Promise<CareerGoal | null> {
    try {
      // 实现逻辑
      return result;
    } catch (error) {
      console.error('获取职业目标失败:', error);
      return null;
    }
  }
}

// 2. 错误处理规范
// - 所有异步方法必须有try-catch
// - 错误信息要有中文描述
// - 返回值要考虑失败情况
```

### Redux状态管理开发

```typescript
// 1. Slice设计规范
// 文件: src/store/slices/careerSlice.ts
interface CareerState {
  // 数据状态
  data: any[];

  // 加载状态
  isLoading: boolean;
  error: string | null;

  // 业务状态
  hasJobSelected: boolean;
}

// 2. 异步操作定义
export const loadJobsAsync = createAsyncThunk(
  'career/loadJobs',
  async (_, { rejectWithValue }) => {
    try {
      const result = await jobService.getAllJobs();
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || '加载失败');
    }
  }
);

// 3. Reducer设计
const careerSlice = createSlice({
  name: 'career',
  initialState,
  reducers: {
    // 同步操作
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 异步操作处理
    builder
      .addCase(loadJobsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadJobsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(loadJobsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});
```

### Hook开发规范

```typescript
// 1. Hook命名规范
// 文件: src/hooks/useCareer.ts
export const useCareer = (): UseCareerReturn => {
  const dispatch = useAppDispatch();
  const state = useAppSelector((state) => state.career);

  // 操作方法使用useCallback优化
  const selectJob = useCallback(async (jobId: string, level: JobLevel) => {
    try {
      await dispatch(selectJobAsync({ jobId, level })).unwrap();
      return true;
    } catch (error) {
      console.error('选择岗位失败:', error);
      return false;
    }
  }, [dispatch]);

  return {
    // 状态数据
    ...state,

    // 操作方法
    selectJob,
  };
};

// 2. Hook返回值类型定义
interface UseCareerReturn {
  // 状态数据
  allJobs: JobProfile[];
  isLoading: boolean;
  error: string | null;

  // 操作方法
  selectJob: (jobId: string, level: JobLevel) => Promise<boolean>;
}
```

### 组件开发规范

```typescript
// 1. 组件Props接口定义
interface JobProgressCardProps {
  jobPreference: UserJobPreference | null;
  jobProgress: JobProgress | null;
  onSetJobTarget?: () => void;
  onViewProgress?: () => void;
}

// 2. 组件实现规范
const JobProgressCard: React.FC<JobProgressCardProps> = ({
  jobPreference,
  jobProgress,
  onSetJobTarget,
  onViewProgress,
}) => {
  const colors = useThemeColors();

  // 条件渲染逻辑
  if (!jobPreference?.isJobSelected) {
    return renderEmptyState();
  }

  return renderProgressContent();
};

// 3. 样式定义规范
const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  // 使用语义化的样式名称
});
```

### 推荐算法开发

```typescript
// 1. 算法类设计
class RecommendationEngine {
  // 权重配置可调整
  private defaultWeights: RecommendationWeights = {
    skillGap: 0.3,
    difficulty: 0.2,
    // ...
  };

  // 主要推荐方法
  async generateRecommendations(
    jobPreference: UserJobPreference,
    jobProgress: JobProgress,
    userBehavior: UserBehavior,
    availableContent: ContentMetadata[],
    maxItems: number = 10
  ): Promise<RecommendedContent[]> {
    // 实现推荐逻辑
  }

  // 分数计算方法
  private calculateRecommendationScore(
    content: ContentMetadata,
    jobPreference: UserJobPreference,
    jobProgress: JobProgress,
    userBehavior: UserBehavior
  ): number {
    // 多维度分数计算
    let totalScore = 0;

    // 1. 技能差距分数
    const skillGapScore = this.calculateSkillGapScore(content, jobProgress);
    totalScore += skillGapScore * this.defaultWeights.skillGap;

    // 2. 其他维度分数...

    return Math.min(1, Math.max(0, totalScore));
  }
}
```

### 测试开发规范

```typescript
// 1. 单元测试示例
// 文件: src/services/__tests__/JobService.test.ts
describe('JobService', () => {
  let jobService: JobService;

  beforeEach(() => {
    jobService = JobService.getInstance();
  });

  test('应该能够获取所有岗位', async () => {
    const jobs = await jobService.getAllJobs();
    expect(jobs).toBeDefined();
    expect(Array.isArray(jobs)).toBe(true);
  });

  test('应该能够根据ID获取岗位', async () => {
    const job = await jobService.getJobById('java-backend-engineer');
    expect(job).toBeDefined();
    expect(job?.id).toBe('java-backend-engineer');
  });
});

// 2. 组件测试示例
// 文件: src/components/__tests__/JobProgressCard.test.tsx
describe('JobProgressCard', () => {
  test('未选择岗位时应显示设置引导', () => {
    render(
      <JobProgressCard
        jobPreference={null}
        jobProgress={null}
        onSetJobTarget={jest.fn()}
      />
    );

    expect(screen.getByText('设置职业目标')).toBeTruthy();
  });
});
```

### 性能优化指南

```typescript
// 1. 组件性能优化
const JobProgressCard = React.memo<JobProgressCardProps>(({
  jobPreference,
  jobProgress,
  onSetJobTarget,
}) => {
  // 使用useMemo缓存计算结果
  const progressData = useMemo(() => {
    if (!jobProgress) return null;
    return calculateProgressData(jobProgress);
  }, [jobProgress]);

  // 使用useCallback缓存事件处理函数
  const handleSetTarget = useCallback(() => {
    onSetJobTarget?.();
  }, [onSetJobTarget]);

  return (
    // 组件内容
  );
});

// 2. 数据获取优化
const useCareer = () => {
  // 避免重复请求
  const loadJobs = useCallback(async () => {
    if (allJobs.length > 0) return; // 已有数据时不重复加载

    try {
      await dispatch(loadJobsAsync()).unwrap();
    } catch (error) {
      console.error('加载岗位数据失败:', error);
    }
  }, [allJobs.length, dispatch]);
};
```

### 调试技巧

```typescript
// 1. 开发环境调试
if (__DEV__) {
  console.log('岗位推荐生成:', {
    userId,
    jobId: jobPreference?.targetJob?.id,
    recommendationCount: recommendations.length
  });
}

// 2. 错误边界处理
class JobRecommendationErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('岗位推荐组件错误:', error, errorInfo);
    // 发送错误报告
  }

  render() {
    if (this.state.hasError) {
      return <FallbackComponent />;
    }

    return this.props.children;
  }
}
```

## 🎯 用户画像系统开发指南

### 用户画像数据收集开发

```typescript
// 1. 用户画像表单开发规范
// 文件: src/screens/profile/UserProfileSetupScreen.tsx

// 表单数据结构
interface UserProfileFormData {
  currentSkillLevel: number;        // 1-5级技能水平
  learningStyle: string;           // 学习风格
  availableTimePerWeek: number;    // 每周可用时间
  careerGoal: string;             // 职业目标
  hasProgrammingExperience: boolean; // 编程经验
  preferredLearningPace: string;   // 学习节奏
  learningMotivation: string;      // 学习动机
  targetSalaryRange: string;       // 期望薪资
  workExperienceYears: number;     // 工作经验年数
  educationLevel: string;          // 教育水平
  preferredJobLocations: string[]; // 偏好工作地点
}

// 2. 分步骤表单实现
const [currentStep, setCurrentStep] = useState(1);
const totalSteps = 4;

// 步骤验证
const validateStep = (step: number): boolean => {
  switch (step) {
    case 1: return formData.currentSkillLevel > 0;
    case 2: return formData.learningStyle !== '';
    case 3: return formData.careerGoal !== '';
    default: return true;
  }
};

// 3. API调用规范
const saveUserProfile = async (profileData: UserProfileFormData) => {
  const response = await fetch(`${API_BASE_URL}/user-profiles`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ userId: user.id, ...profileData })
  });
  return response.json();
};
```

### 技能评估工具开发

```typescript
// 1. 技能评估数据模型
interface SkillAssessment {
  id: number;
  userId: number;
  skillName: string;
  skillCategory: string;
  currentLevel: number;        // 1-5级
  targetLevel: number;         // 目标级别
  assessmentMethod: string;    // 评估方式
  lastAssessedAt: string;      // 最后评估时间
}

// 2. 技能评估组件开发
// 文件: src/screens/profile/UserSkillAssessmentScreen.tsx

// 技能等级可视化
const renderSkillLevel = (level: number) => (
  <ProgressBar
    progress={level / 5}
    progressColor={getSkillLevelColor(level)}
    backgroundColor={Colors.grey70}
    height={6}
  />
);

// 技能统计计算
const getSkillStatistics = (assessments: SkillAssessment[]) => ({
  totalSkills: assessments.length,
  advancedSkills: assessments.filter(s => s.currentLevel >= 4).length,
  improvementNeeded: assessments.filter(s => s.targetLevel > s.currentLevel).length
});

// 3. 技能评估API集成
const loadSkillAssessments = async (userId: number) => {
  const response = await fetch(`${API_BASE_URL}/user-skill-assessments/user/${userId}`);
  const result = await response.json();
  return result.data.content || [];
};
```

### 个性化推荐系统开发指南 (2025-07-10更新)

#### 1. 智能推荐算法开发

```typescript
// 文件: src/services/PersonalizedRecommendationService.ts

class PersonalizedRecommendationService {
  private apiService = ApiService.getInstance();

  // 获取个性化推荐
  async getPersonalizedRecommendations(
    userId: number,
    targetJobId?: number,
    limit: number = 10
  ): Promise<ServiceResponse<RecommendationData[]>> {
    try {
      const params = new URLSearchParams();
      if (targetJobId) params.append('targetJobId', targetJobId.toString());
      params.append('limit', limit.toString());

      const response = await this.apiService.get(
        `/learning-paths/recommendations/${userId}?${params.toString()}`
      );

      if (response.success && response.data) {
        const formattedData = this.formatRecommendations(response.data);
        return { success: true, data: formattedData };
      }

      return { success: false, error: '获取推荐失败' };
    } catch (error) {
      console.error('获取个性化推荐失败:', error);
      return { success: false, error: '网络请求失败' };
    }
  }

  // 提交用户反馈
  async submitFeedback(
    userId: number,
    pathId: number,
    action: 'ACCEPT' | 'REJECT' | 'LIKE' | 'DISLIKE',
    rating?: number,
    feedback?: string
  ): Promise<ServiceResponse<any>> {
    try {
      const response = await this.apiService.post('/learning-paths/recommendations/feedback', {
        userId,
        pathId,
        action,
        rating,
        feedback
      });

      return { success: response.success, data: response.data };
    } catch (error) {
      console.error('提交反馈失败:', error);
      return { success: false, error: '提交反馈失败' };
    }
  }

  // 格式化推荐数据
  private formatRecommendations(recommendations: any[]): RecommendationData[] {
    return recommendations.map(item => ({
      id: item.path?.id || 0,
      pathName: item.path?.name || '',
      description: item.path?.description || '',
      difficultyLevel: item.path?.difficultyLevel || 'BEGINNER',
      estimatedHours: item.path?.estimatedHours || 0,
      matchScore: Math.round((item.score || 0) * 100),
      recommendationReason: item.reason || '',
      algorithmVersion: item.algorithmVersion || 'v2.0',
      factors: item.factors || {}
    }));
  }
}
```

#### 2. 推荐页面组件开发

```typescript
// 文件: src/screens/learning/PersonalizedRecommendationScreen.tsx

const PersonalizedRecommendationScreen: React.FC = () => {
  const [recommendations, setRecommendations] = useState<RecommendationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const personalizedService = PersonalizedRecommendationService.getInstance();

  // 加载推荐数据
  const loadRecommendations = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const userId = 1; // 从用户状态获取
      const result = await personalizedService.getPersonalizedRecommendations(userId);

      if (result.success) {
        setRecommendations(result.data || []);
      } else {
        setError(result.error || '加载失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 处理接受推荐
  const handleAcceptRecommendation = async (pathId: number, pathName: string) => {
    try {
      const userId = 1; // 从用户状态获取
      const result = await personalizedService.submitFeedback(
        userId, pathId, 'ACCEPT', 5, `接受推荐：${pathName}`
      );

      if (result.success) {
        Toast.showSuccessToast('已接受推荐，为您跳转到学习路径');
        // 导航到学习路径详情页
        navigation.navigate('LearningPathDetail', { pathId });
      }
    } catch (error) {
      Toast.showErrorToast('操作失败，请重试');
    }
  };

  // 渲染推荐卡片
  const renderRecommendationCard = ({ item }: { item: RecommendationData }) => (
    <RecommendationCard
      key={item.id}
      data={item}
      onAccept={() => handleAcceptRecommendation(item.id, item.pathName)}
      onReject={() => handleRejectRecommendation(item.id, item.pathName)}
      onViewDetails={() => navigation.navigate('LearningPathDetail', { pathId: item.id })}
    />
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorMessage message={error} onRetry={loadRecommendations} />
      ) : (
        <FlatList
          data={recommendations}
          renderItem={renderRecommendationCard}
          keyExtractor={(item) => item.id.toString()}
          refreshing={loading}
          onRefresh={loadRecommendations}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};
```

#### 3. 推荐卡片组件开发

```typescript
// 文件: src/components/learning/RecommendationCard.tsx

interface RecommendationCardProps {
  data: RecommendationData;
  onAccept: () => void;
  onReject: () => void;
  onViewDetails: () => void;
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({
  data,
  onAccept,
  onReject,
  onViewDetails
}) => {
  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>{data.pathName}</Text>
        <View style={styles.scoreContainer}>
          <Text style={styles.scoreText}>{data.matchScore}%</Text>
          <Text style={styles.scoreLabel}>匹配度</Text>
        </View>
      </View>

      <Text style={styles.description}>{data.description}</Text>

      <View style={styles.metadata}>
        <Chip label={`难度: ${data.difficultyLevel}`} />
        <Chip label={`${data.estimatedHours}小时`} />
      </View>

      <Text style={styles.reason}>{data.recommendationReason}</Text>

      <View style={styles.actions}>
        <Button
          label="查看详情"
          size="small"
          outline
          onPress={onViewDetails}
        />
        <Button
          label="不感兴趣"
          size="small"
          outline
          color="red"
          onPress={onReject}
        />
        <Button
          label="开始学习"
          size="small"
          onPress={onAccept}
        />
      </View>
    </Card>
  );
};
```

#### 4. 开发调试技巧

```typescript
// 开发环境调试
if (__DEV__) {
  console.log('推荐数据:', {
    userId,
    recommendationCount: recommendations.length,
    averageScore: recommendations.reduce((sum, r) => sum + r.matchScore, 0) / recommendations.length,
    algorithmVersion: recommendations[0]?.algorithmVersion
  });
}

// 错误边界处理
class RecommendationErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('推荐系统错误:', error, errorInfo);
    // 发送错误报告到监控系统
  }
}

// 性能监控
const measureRecommendationPerformance = async () => {
  const startTime = performance.now();
  await loadRecommendations();
  const endTime = performance.now();
  console.log(`推荐加载耗时: ${endTime - startTime}ms`);
};
```

#### 5. 测试开发指南

```typescript
// 单元测试示例
describe('PersonalizedRecommendationService', () => {
  it('should format recommendations correctly', () => {
    const mockData = [
      {
        path: { id: 1, name: 'Java基础', description: '学习Java' },
        score: 0.85,
        reason: '推荐理由'
      }
    ];

    const service = new PersonalizedRecommendationService();
    const result = service.formatRecommendations(mockData);

    expect(result[0].matchScore).toBe(85);
    expect(result[0].pathName).toBe('Java基础');
  });
});

// 集成测试
describe('PersonalizedRecommendationScreen', () => {
  it('should load and display recommendations', async () => {
    const { getByText } = render(<PersonalizedRecommendationScreen />);

    await waitFor(() => {
      expect(getByText('Java基础')).toBeTruthy();
    });
  });
});
```

### 开发最佳实践

```typescript
// 1. 错误处理规范
const handleProfileError = (error: any) => {
  console.error('用户画像操作失败:', error);
  Alert.alert('操作失败', '请检查网络连接后重试');
};

// 2. 数据验证规范
const validateProfileData = (data: UserProfileFormData): string[] => {
  const errors: string[] = [];

  if (data.currentSkillLevel < 1 || data.currentSkillLevel > 5) {
    errors.push('技能水平必须在1-5级之间');
  }

  if (data.availableTimePerWeek < 1 || data.availableTimePerWeek > 40) {
    errors.push('每周学习时间必须在1-40小时之间');
  }

  return errors;
};

// 3. 性能优化规范
const useProfileData = (userId: number) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const loadProfile = async () => {
      try {
        const data = await fetchUserProfile(userId);
        if (isMounted) {
          setProfile(data);
        }
      } catch (error) {
        if (isMounted) {
          handleProfileError(error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadProfile();

    return () => {
      isMounted = false;
    };
  }, [userId]);

  return { profile, loading };
};
```

## 👤 用户管理系统开发指南 (2025-07-16新增)

### 功能概述
用户管理系统提供完整的用户资料编辑功能，包括基本信息修改、头像上传、表单验证等核心功能。

### 开发流程

#### 1. 后端开发

```java
// 用户控制器开发规范
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户基本信息管理相关接口")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    // 获取用户信息
    @GetMapping("/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户基本信息")
    public ResponseEntity<ApiResponse<User>> getUserById(@PathVariable Long userId) {
        try {
            User user = userService.findById(userId);
            if (user == null) {
                return ResponseEntity.status(404)
                        .body(ApiResponse.error("用户不存在"));
            }

            // 清除敏感信息
            user.setPassword(null);
            return ResponseEntity.ok(ApiResponse.success(user));
        } catch (Exception e) {
            log.error("获取用户信息异常: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户信息失败"));
        }
    }

    // 更新用户基本信息（部分更新）
    @PatchMapping("/{userId}")
    @Operation(summary = "更新用户基本信息", description = "部分更新用户的基本信息")
    public ResponseEntity<ApiResponse<User>> updateUserInfo(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> updates) {
        try {
            User updatedUser = userService.updateUserInfo(userId, updates);
            updatedUser.setPassword(null);
            return ResponseEntity.ok(ApiResponse.success("用户信息更新成功", updatedUser));
        } catch (BusinessException e) {
            return ResponseEntity.status(e.getHttpStatus())
                    .body(ApiResponse.error(e.getCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("更新用户信息异常: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("更新用户信息失败"));
        }
    }
}
```

```java
// 用户服务层开发规范
@Service
@Slf4j
public class UserService {

    // 更新用户基本信息（部分更新）
    public User updateUserInfo(Long userId, Map<String, Object> updates) {
        try {
            User user = findById(userId);
            if (user == null) {
                throw BusinessException.notFound("用户不存在");
            }

            // 验证和更新各个字段
            for (Map.Entry<String, Object> entry : updates.entrySet()) {
                String field = entry.getKey();
                Object value = entry.getValue();

                switch (field) {
                    case "nickname":
                        if (value != null) {
                            String nickname = value.toString().trim();
                            if (!isValidNickname(nickname)) {
                                throw BusinessException.badRequest("昵称格式不正确");
                            }
                            user.setNickname(nickname);
                        }
                        break;
                    case "email":
                        if (value != null) {
                            String email = value.toString().trim();
                            if (!isValidEmail(email)) {
                                throw BusinessException.badRequest("邮箱格式不正确");
                            }
                            user.setEmail(email);
                        }
                        break;
                    // 其他字段验证...
                }
            }

            user.setUpdatedAt(LocalDateTime.now());
            return userRepository.save(user);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw BusinessException.internalError("更新用户信息失败", e);
        }
    }

    // 验证方法
    private boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(emailRegex) && email.length() <= 100;
    }

    private boolean isValidNickname(String nickname) {
        String nicknameRegex = "^[\\u4e00-\\u9fa5a-zA-Z0-9_]{1,50}$";
        return nickname.matches(nicknameRegex);
    }
}
```

#### 2. 前端开发

```typescript
// 用户资料编辑页面开发规范
// 文件: src/screens/profile/EditProfileScreen.tsx

export const EditProfileScreen: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [formData, setFormData] = useState({
    nickname: '',
    email: '',
    phone: '',
    bio: ''
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // 加载用户信息
  const loadUserProfile = async () => {
    if (!userId) {
      showToast('用户ID不存在', 'error');
      navigation.goBack();
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.get(`/users/${userId}`);

      if (response.code === 20000) {
        const profile = response.data;
        setUserProfile(profile);
        setFormData({
          nickname: profile.nickname || '',
          email: profile.email || '',
          phone: profile.phone || '',
          bio: profile.bio || ''
        });
      } else {
        showToast(response.message || '加载用户信息失败', 'error');
        navigation.goBack();
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      showToast('加载用户信息失败', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // 实时验证
  const validateField = (field: keyof typeof formData, value: string) => {
    let error = '';

    switch (field) {
      case 'nickname':
        if (!value.trim()) {
          error = '昵称不能为空';
        } else if (!/^[\u4e00-\u9fa5a-zA-Z0-9_]{1,50}$/.test(value)) {
          error = '昵称只能包含中文、英文、数字和下划线';
        }
        break;
      case 'email':
        if (!value.trim()) {
          error = '邮箱不能为空';
        } else if (!/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(value)) {
          error = '邮箱格式不正确';
        }
        break;
      // 其他字段验证...
    }

    setFieldErrors(prev => ({
      ...prev,
      [field]: error
    }));

    return error === '';
  };

  // 保存用户信息
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      const response = await apiService.patch(`/users/${userId}`, formData);

      if (response.code === 20000) {
        showToast('保存成功', 'success');
        navigation.goBack();
      } else {
        showToast(response.message || '保存失败', 'error');
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      showToast('保存失败', 'error');
    } finally {
      setSaving(false);
    }
  };
};
```

### 开发规范

#### 1. 数据验证
- 前后端都要进行数据验证
- 使用正则表达式验证格式
- 提供清晰的错误提示信息

#### 2. 用户体验
- 实时表单验证和错误提示
- 变化检测和保存按钮状态管理
- 加载状态和错误处理

#### 3. 安全考虑
- 清除敏感信息（如密码）
- 输入数据清理和验证
- 文件上传安全检查

#### 4. 测试要求
- 完整的前后端联调测试
- 使用Playwright MCP验证用户流程
- 检查浏览器控制台确保无错误

## 🚀 岗位专属功能开发指南 (第三阶段)

### 项目实战平台开发

```typescript
// 1. 项目模板开发规范
// 文件: src/data/projectTemplates.ts
export const newProjectTemplate: EnhancedProjectTemplate = {
  id: 'unique-project-id',
  title: '项目标题',
  phases: [
    {
      id: 'phase-1',
      name: '阶段名称',
      tasks: [
        {
          id: 'task-1',
          title: '任务标题',
          type: 'coding',           // 任务类型
          priority: 'high',         // 优先级
          estimatedHours: 2,        // 预计时长
          skills: ['skill-id'],     // 相关技能
          isCompleted: false
        }
      ],
      deliverables: ['交付物列表'],
      resources: [/* 学习资源 */]
    }
  ],
  assessments: [/* 评估标准 */]
};

// 2. 项目进度跟踪
// 使用ProjectWorkshopService管理项目进度
const projectService = ProjectWorkshopService.getInstance();
await projectService.startProject(userId, projectId);
await projectService.updateTaskProgress(userId, projectId, taskId, 'completed');
```

### 面试准备系统开发

```typescript
// 1. 面试题开发规范
// 文件: src/data/jobSpecificInterviewQuestions.ts
const newInterviewQuestion: JobSpecificInterviewQuestion = {
  id: 'question-id',
  title: '面试题标题',
  content: '题目详细内容',
  type: InterviewQuestionType.TECHNICAL,
  difficulty: DifficultyLevel.MEDIUM,

  // 岗位相关性配置
  relatedJobs: ['job-id'],
  jobRelevance: {
    'job-id': {
      score: 95,                    // 相关性分数
      importance: 'critical',       // 重要程度
      frequency: 85                 // 出现频率
    }
  },

  // 评分标准
  scoringCriteria: {
    criteria: '评分标准描述',
    maxScore: 100,
    rubric: {
      excellent: '优秀标准',
      good: '良好标准',
      fair: '一般标准',
      poor: '较差标准'
    }
  }
};

// 2. 面试准备计划生成
const interviewService = JobInterviewService.getInstance();
const plan = await interviewService.generatePreparationPlan(userId, jobId, currentLevel);
```

### 作品集生成开发

```typescript
// 1. 作品集配置开发
const portfolioConfig: PortfolioGenerationConfig = {
  userId,
  jobId,
  includeProjects: ['project-1', 'project-2'],
  includeSkills: ['skill-1', 'skill-2'],
  templateId: 'template-modern',
  theme: 'light',
  primaryColor: '#007AFF',
  purpose: 'job_application',
  formats: ['web', 'pdf']
};

// 2. 生成任务管理
const portfolioService = PortfolioGenerationService.getInstance();
const task = await portfolioService.createGenerationTask(config);

// 3. 监听生成进度
const checkProgress = async () => {
  const tasks = await portfolioService.getUserGenerationTasks(userId);
  const currentTask = tasks.find(t => t.id === taskId);
  console.log(`生成进度: ${currentTask?.progress}%`);
};
```

### 求职模块增强开发

```typescript
// 1. 岗位推荐开发
const jobRecommendationService = JobRecommendationService.getInstance();

// 生成推荐
const recommendations = await jobRecommendationService.generateJobRecommendations(
  userId,
  userSkills,
  preferences
);

// 分析特定岗位
const analysis = await jobRecommendationService.analyzeSpecificJob(jobId, userSkills);

// 2. 岗位匹配组件使用
<JobMatchAnalysis
  jobTitle={job.name}
  overallMatch={analysis.overallMatch}
  skillMatches={analysis.skillMatches}
  recommendations={analysis.recommendations}
  onImproveSkill={(skill) => {
    // 处理技能提升
    navigation.navigate('SkillImprovement', { skill });
  }}
/>
```

### 求职板块UI重构开发指南 (2025-07-14)

#### 重构原则
1. **功能聚焦**：保留4个核心功能，删除非核心功能
2. **UI简化**：移除复杂导航，采用卡片式布局
3. **代码清理**：删除无用代码，提高维护性
4. **用户体验**：突出核心功能，提升操作效率

#### 核心功能配置

```typescript
// 求职核心功能定义
const CORE_JOB_FEATURES = [
  {
    key: 'recommendations',
    label: '智能推荐',
    icon: 'search',
    color: colors.primary,
    route: 'JobRecommendations'
  },
  {
    key: 'applied',
    label: '已申请',
    icon: 'checkmark-circle',
    color: colors.success,
    route: null  // 显示在当前页面
  },
  {
    key: 'analysis',
    label: '匹配分析',
    icon: 'analytics',
    color: colors.secondary,
    route: 'JobMatchAnalysis'
  },
  {
    key: 'interview',
    label: '面试准备',
    icon: 'school',
    color: colors.warning,
    route: 'InterviewPreparation'
  }
];
```

#### 组件重构模式

```typescript
// 重构前：复杂的标签页导航
const renderTabBar = () => {
  const tabs = [
    { key: 'recommended', label: '推荐' },
    { key: 'all', label: '全部' },
    { key: 'applied', label: '已申请' }
  ];
  // 复杂的标签页逻辑...
};

// 重构后：简化的功能卡片
const renderCoreFeatures = () => {
  return (
    <View style={styles.featuresContainer}>
      <Text style={styles.sectionTitle}>🚀 求职核心功能</Text>
      <View style={styles.featuresGrid}>
        {CORE_JOB_FEATURES.map((feature) => (
          <FeatureCard
            key={feature.key}
            feature={feature}
            onPress={() => handleFeaturePress(feature)}
          />
        ))}
      </View>
    </View>
  );
};
```

#### 状态管理简化

```typescript
// 重构前：复杂的状态管理
interface OldJobsScreenState {
  currentTab: 'recommended' | 'all' | 'applied';
  featuredJobs: Job[];
  recommendedJobs: Job[];
  recentJobs: Job[];
  allJobs: Job[];
}

// 重构后：简化的状态管理
interface NewJobsScreenState {
  allJobs: Job[];
  isRefreshing: boolean;
  isLoading: boolean;
}
```

#### 路由配置更新

```typescript
// 新增真实的JobMatchAnalysisScreen
import JobMatchAnalysisScreen from '../screens/jobs/JobMatchAnalysisScreen';

// 替换占位组件
<Stack.Screen
  name="JobMatchAnalysis"
  component={JobMatchAnalysisScreen}
  options={{ headerShown: false }}
/>
```

#### 测试验证流程

```typescript
// 使用Playwright MCP进行功能测试
const testJobsModule = async () => {
  // 1. 导航到求职页面
  await page.goto('/jobs');

  // 2. 验证4个核心功能入口
  await page.click('[data-testid="smart-recommendations"]');
  await page.click('[data-testid="applied-jobs"]');
  await page.click('[data-testid="job-analysis"]');
  await page.click('[data-testid="interview-prep"]');

  // 3. 检查控制台错误
  const errors = await page.evaluate(() => console.errors);
  expect(errors).toHaveLength(0);
};
```

### 在线代码编辑器开发

```typescript
// 1. 代码环境创建
const codeEditorService = OnlineCodeEditorService.getInstance();

// 为项目创建代码环境
const codeSnippet = await codeEditorService.createProjectCodeEnvironment(
  userId,
  projectId,
  projectTemplate
);

// 2. 代码执行
const executionResult = await codeEditorService.executeCode(
  userId,
  snippetId,
  code,
  language
);

// 3. 协作编程会话
const session = await codeEditorService.createCollaborativeSession(
  userId,
  snippetId,
  'Java项目协作',
  5 // 最大参与者数量
);
```

### 服务集成开发规范

```typescript
// 1. 服务间通信
class IntegratedService {
  private projectService = ProjectWorkshopService.getInstance();
  private interviewService = JobInterviewService.getInstance();
  private portfolioService = PortfolioGenerationService.getInstance();

  // 综合功能实现
  async createLearningPlan(userId: string, jobId: string) {
    // 1. 获取项目推荐
    const projects = await this.projectService.getRecommendedProjects(userId, jobId);

    // 2. 生成面试准备计划
    const interviewPlan = await this.interviewService.generatePreparationPlan(userId, jobId);

    // 3. 创建作品集配置
    const portfolioConfig = await this.portfolioService.generatePortfolioSuggestions(userId, jobId);

    return { projects, interviewPlan, portfolioConfig };
  }
}

// 2. 错误处理规范
try {
  const result = await service.performOperation();
  return result;
} catch (error) {
  console.error('操作失败:', error);
  // 记录错误日志
  // 显示用户友好的错误信息
  // 提供重试机制
  return null;
}
```

### Hook开发规范

```typescript
// 1. 项目实战Hook
export const useProjectWorkshop = () => {
  const [projects, setProjects] = useState<EnhancedProjectTemplate[]>([]);
  const [userProgress, setUserProgress] = useState<UserProjectProgress[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const startProject = useCallback(async (projectId: string) => {
    setIsLoading(true);
    try {
      const success = await projectService.startProject(userId, projectId);
      if (success) {
        // 刷新数据
        await loadUserProgress();
      }
      return success;
    } catch (error) {
      console.error('开始项目失败:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  return { projects, userProgress, isLoading, startProject };
};

// 2. 面试准备Hook
export const useInterviewPreparation = (jobId: string) => {
  const [questions, setQuestions] = useState<JobSpecificInterviewQuestion[]>([]);
  const [preparationPlan, setPreparationPlan] = useState<InterviewPreparationPlan | null>(null);

  // Hook实现...

  return { questions, preparationPlan, /* 其他状态和方法 */ };
};
```

### 测试开发规范

```typescript
// 1. 服务层测试
describe('ProjectWorkshopService', () => {
  let service: ProjectWorkshopService;

  beforeEach(() => {
    service = ProjectWorkshopService.getInstance();
  });

  test('应该能够开始项目', async () => {
    const result = await service.startProject('user-1', 'project-1');
    expect(result).toBe(true);
  });

  test('应该能够更新任务进度', async () => {
    await service.startProject('user-1', 'project-1');
    const result = await service.updateTaskProgress('user-1', 'project-1', 'task-1', 'completed');
    expect(result).toBe(true);
  });
});

// 2. 组件测试
describe('JobMatchAnalysis', () => {
  const mockProps = {
    jobTitle: 'Java后端工程师',
    overallMatch: 85,
    skillMatches: [/* 模拟数据 */],
    recommendations: ['建议1', '建议2']
  };

  test('应该正确显示匹配度', () => {
    render(<JobMatchAnalysis {...mockProps} />);
    expect(screen.getByText('85%')).toBeTruthy();
    expect(screen.getByText('高度匹配')).toBeTruthy();
  });
});
```

### 性能优化指南

```typescript
// 1. 服务层缓存
class OptimizedService {
  private cache = new Map<string, any>();
  private cacheTimeout = 5 * 60 * 1000; // 5分钟

  async getCachedData(key: string, fetcher: () => Promise<any>) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const data = await fetcher();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }
}

// 2. 组件性能优化
const OptimizedComponent = React.memo<Props>(({ data, onAction }) => {
  // 使用useMemo缓存计算结果
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  // 使用useCallback缓存事件处理函数
  const handleAction = useCallback((id: string) => {
    onAction?.(id);
  }, [onAction]);

  return <div>{/* 组件内容 */}</div>;
});
```

---

## 🎯 市场分析系统开发指南 (2025-07-12新增)

### 功能概述
市场分析系统为用户提供基于职业目标的市场数据分析，包括薪资水平、市场活跃度、职业前景等关键指标。

### 开发流程

#### 1. 后端开发
```java
// 1. 实体类设计
@Entity
@Table(name = "market_analysis")
public class MarketAnalysis {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "market_activity_score")
    private Integer marketActivityScore = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "demand_trend")
    private DemandTrend demandTrend = DemandTrend.STABLE;

    // 枚举定义 - 注意使用大写命名规范
    public enum DemandTrend {
        INCREASING("需求增长"),
        STABLE("需求稳定"),
        DECREASING("需求下降");
    }
}

// 2. 控制器实现
@RestController
@RequestMapping("/api/market-analysis")
public class MarketAnalysisController {

    @GetMapping("/career-goal/{careerGoalId}")
    public ApiResponse<JobMarketData> getCareerGoalMarketAnalysis(@PathVariable Long careerGoalId) {
        JobMarketData data = marketAnalysisService.getCareerGoalMarketData(careerGoalId);
        return ApiResponse.success("市场分析数据获取成功", data);
    }
}
```

#### 2. 前端开发
```typescript
// 1. 类型定义
export interface JobMarketData {
  jobId: number;
  jobTitle: string;
  jobCategory: string;
  marketActivityScore: number;
  careerProspectScore: number;
  salaryGrowthRate: number;
  demandTrend: string;
  competitionLevel: string;
  marketInsight: string;
  careerAdvice: string;
  avgSalaryJunior: number;
  avgSalaryMid: number;
  avgSalarySenior: number;
  lastUpdated: string;
}

// 注意：枚举值必须与后端保持一致
export enum DemandTrend {
  INCREASING = 'INCREASING',
  STABLE = 'STABLE',
  DECREASING = 'DECREASING'
}

// 2. 页面组件实现
export const MarketAnalysisScreen: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketAnalysisData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMarketAnalysis();
  }, [jobId]);

  const loadMarketAnalysis = async () => {
    try {
      const data = await MarketAnalysisService.getMarketAnalysis(jobId);
      setMarketData(data);
    } catch (error) {
      console.error('加载市场分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <MarketCoreMetrics data={marketData} />
      <SalaryAnalysis data={marketData?.salaryData} />
      <MarketInsights insight={marketData?.marketInsight} />
    </ScrollView>
  );
};
```

### 关键开发经验

#### 1. 枚举类型统一问题
**问题**: 前后端枚举值不匹配导致序列化失败
```
No enum constant com.itbook.entity.MarketAnalysis.DemandTrend.STABLE
```

**解决方案**: 统一使用大写命名规范
```java
// 后端枚举定义
public enum DemandTrend {
    INCREASING("需求增长"),
    STABLE("需求稳定"),
    DECREASING("需求下降");
}
```

```typescript
// 前端枚举定义
export enum DemandTrend {
  INCREASING = 'INCREASING',
  STABLE = 'STABLE',
  DECREASING = 'DECREASING'
}
```

#### 2. 状态管理问题
**问题**: CareerManagementScreen中targetJob获取失败
**原因**: 使用了旧的jobPreference状态，而新数据在careerProgress中

**解决方案**: 优先使用统一进度体系的数据
```typescript
const handleViewMarketAnalysis = () => {
  // 优先使用统一进度体系的数据
  const targetJob = careerProgress?.targetJob || jobPreference?.targetJob;

  if (targetJob) {
    navigation.navigate('MarketAnalysis', {
      jobId: targetJob.id,
      jobTitle: targetJob.title
    });
  }
};
```

#### 3. 测试验证流程
1. **API测试**: 使用PowerShell验证后端API响应
2. **前端测试**: 使用Playwright MCP进行完整用户流程测试
3. **控制台检查**: 确保无JavaScript运行时错误
4. **数据验证**: 验证前后端数据传输的正确性

### 开发规范

#### 1. 命名规范
- 后端实体类使用大写枚举值
- 前端类型定义与后端保持一致
- API路径使用RESTful风格

#### 2. 错误处理
- 后端提供详细的错误信息
- 前端实现优雅的错误处理和用户提示

---

## 🔧 数据模型清理与概念统一指南 (2025-07-13)

### 背景说明
在项目发展过程中，出现了Job（求职）和Career（职业目标）概念混用的问题，需要进行系统性清理。

### 清理原则
1. **概念明确**：Career = 职业目标，Job = 求职岗位
2. **类型统一**：统一使用`types/career.ts`中的类型定义
3. **命名规范**：职业目标功能统一使用Career前缀
4. **向后兼容**：保留必要的兼容性字段，标记废弃

### 开发规范

#### 1. 类型定义规范
```typescript
// ✅ 正确：使用 types/career.ts 中的标准类型
import {
  CareerGoal,              // 职业目标
  UserCareerPreference,    // 用户职业偏好
  CareerProgress,          // 职业进度
  CareerMatch,             // 职业匹配
  CareerLevel              // 职业级别
} from '../types/career';

// ❌ 错误：不要使用 types/job.ts 中的重复类型
import { JobProfile, UserJobPreference } from '../types/job'; // 已废弃
```

#### 2. 状态管理规范
```typescript
// ✅ 正确：使用Career相关字段
interface CareerState {
  allCareerGoals: CareerGoal[];           // 所有职业目标
  popularCareerGoals: CareerGoal[];       // 热门职业目标
  careerPreference: UserCareerPreference; // 用户职业偏好
  hasCareerSelected: boolean;             // 是否已选择职业
  careerProgress: CareerProgress;         // 职业进度
  careerMatch: CareerMatch;               // 职业匹配
}

// ❌ 错误：不要使用Job相关字段
interface CareerState {
  allJobs: JobProfile[];        // 已废弃
  jobPreference: UserJobPreference; // 已废弃
  hasJobSelected: boolean;      // 已废弃
}
```

#### 3. Hook使用规范
```typescript
// ✅ 正确：使用Career相关方法和字段
const {
  careerPreference,        // 职业偏好
  hasCareerSelected,       // 是否已选择职业
  careerProgress,          // 职业进度
  loadCareerGoals,         // 加载职业目标
  selectCareerGoal,        // 选择职业目标
  analyzeCareerMatch,      // 分析职业匹配
} = useCareer();

// ❌ 错误：不要使用Job相关字段
const {
  jobPreference,           // 已废弃
  hasJobSelected,          // 已废弃
  jobProgress,             // 已废弃
  loadJobs,                // 已废弃
  selectJob,               // 已废弃
} = useCareer();
```

#### 4. 组件开发规范
```typescript
// ✅ 正确：使用正确的数据字段
const CareerManagementScreen = () => {
  const { careerPreference, careerProgress } = useCareer();

  // 优先使用统一进度体系的数据
  const targetCareer = careerProgress?.targetJob || careerPreference?.targetCareer;
  const level = careerProgress?.currentLevel || careerPreference?.currentLevel;

  // 条件渲染
  if (!hasCareerSelected || !careerPreference?.targetCareer) {
    return <CareerSelectionGuide />;
  }

  return <CareerManagementContent />;
};

// ❌ 错误：不要直接使用废弃字段
const targetJob = jobPreference?.targetJob; // 已废弃
```

#### 5. 服务层规范
```typescript
// ✅ 正确：使用CareerService处理职业目标相关功能
import { CareerService } from '../services/CareerService';

const careerService = CareerService.getInstance();
const careerGoals = await careerService.getAllCareerGoals();

// ⚠️ 注意：JobService已标记废弃，仅用于求职岗位功能
/**
 * @deprecated 此服务正在重构中
 * 职业目标相关功能 -> 使用 CareerService
 * 求职岗位相关功能 -> 使用 JobsService (待创建)
 */
import { JobService } from '../services/JobService';
```

### 迁移检查清单

#### 类型定义检查
- [ ] 确认不再导入`types/job.ts`中的类型
- [ ] 统一使用`types/career.ts`中的标准类型
- [ ] 更新所有接口定义使用Career相关类型

#### 状态管理检查
- [ ] `careerSlice.ts`中所有字段使用Career命名
- [ ] 异步操作使用Career相关命名
- [ ] 移除或标记废弃Job相关字段

#### Hook层检查
- [ ] `useCareer`返回Career相关字段
- [ ] `useJob`标记为废弃并添加迁移说明
- [ ] 更新所有Hook调用使用新字段名

#### 组件层检查
- [ ] 所有组件使用Career相关props
- [ ] 更新条件渲染逻辑使用新字段
- [ ] 确保数据获取逻辑正确

#### 服务层检查
- [ ] 职业目标功能迁移到CareerService
- [ ] JobService标记废弃并添加说明
- [ ] 更新所有服务调用

### 测试验证
```typescript
// 1. 单元测试更新
describe('CareerService', () => {
  test('应该能够获取所有职业目标', async () => {
    const careerGoals = await careerService.getAllCareerGoals();
    expect(careerGoals).toBeDefined();
    expect(Array.isArray(careerGoals)).toBe(true);
  });
});

// 2. 组件测试更新
describe('CareerManagementScreen', () => {
  test('未选择职业时应显示引导页面', () => {
    render(<CareerManagementScreen />);
    expect(screen.getByText('选择职业目标')).toBeTruthy();
  });
});

// 3. 前后端联调测试
// 使用Playwright MCP验证功能正常工作
```

### 常见问题解决

#### 问题1：数据获取失败
```typescript
// 原因：使用了废弃的字段
const targetJob = jobPreference?.targetJob; // undefined

// 解决：使用正确的数据源
const targetCareer = careerProgress?.targetJob || careerPreference?.targetCareer;
```

#### 问题2：类型错误
```typescript
// 原因：导入了废弃的类型
import { JobProfile } from '../types/job';

// 解决：使用标准类型
import { CareerGoal } from '../types/career';
```

#### 问题3：Hook返回值错误
```typescript
// 原因：使用了废弃的字段名
const { jobPreference } = useCareer(); // undefined

// 解决：使用新的字段名
const { careerPreference } = useCareer();
```
- 控制台日志记录关键操作

#### 3. 测试要求
- 必须进行完整的前后端联调测试
- 使用Playwright MCP验证用户流程
- 检查浏览器控制台确保无错误

## 🎨 技能图谱可视化功能开发指南

### 功能概述
技能图谱可视化功能为用户提供直观的技能关系展示和布局切换体验，通过可视化方式展示技能之间的关联性、分类和学习路径。

### 核心组件结构
```typescript
// 主页面组件
src/screens/visualization/SkillGraphScreen.tsx

// 相关服务
src/services/SkillGraphService.ts  // 技能图谱API服务
src/services/ApiService.ts         // 通用API服务

// 类型定义
src/types/skillGraph.ts           // 技能图谱相关类型
```

### 开发要点

#### 1. 布局算法实现
```typescript
// 三种布局模式的实现
const getLayoutedSkills = (skillIds: number[], layout: string) => {
  switch (layout) {
    case 'force':     // 力导向：按学时排序
    case 'hierarchy': // 层次：按难度分层
    case 'circular':  // 圆形：按分类排序
  }
};
```

#### 2. 数据映射策略
```typescript
// 技能ID到名称映射
const skillsMap = await loadSkillsData();
const getSkillName = (skillId: number) => {
  return skillsMap[skillId]?.name || `技能 ${skillId}`;
};
```

#### 3. 状态管理模式
```typescript
// 核心状态
const [selectedLayout, setSelectedLayout] = useState<'force' | 'hierarchy' | 'circular'>('force');
const [skillsMap, setSkillsMap] = useState<{[key: number]: any}>({});
const [layoutLoading, setLayoutLoading] = useState(false);
```

### API集成要求
- **技能图谱统计**: `/api/skill-graph/statistics`
- **技能聚类数据**: `/api/skill-graph/clusters`
- **原子技能详情**: `/api/atomic-skills`

### UI设计规范
- 严格使用design-tokens设计令牌
- 支持深色/浅色主题切换
- 遵循Material Design 3规范
- activeOpacity: 0.7统一交互反馈

### 测试验证要求
1. **功能测试**: 三种布局模式切换正常
2. **数据测试**: 技能名称正确映射显示
3. **交互测试**: 所有按钮和Tab切换正常
4. **主题测试**: 深色/浅色主题完美适配
5. **控制台检查**: 无JavaScript错误

### 常见问题解决

#### 问题1：技能名称显示为ID
```typescript
// 原因：skillsMap未正确加载
// 解决：确保loadSkillsData()在useEffect中调用
useEffect(() => {
  loadGraphData();
  loadSkillsData(); // 必须调用
}, []);
```

#### 问题2：布局切换无视觉变化
```typescript
// 原因：未实现真正的布局算法
// 解决：实现getLayoutedSkills函数
const layoutedSkills = getLayoutedSkills(cluster.skillIds, selectedLayout);
```

#### 问题3：主题切换异常
```typescript
// 原因：使用了错误的颜色属性
// 解决：使用正确的主题颜色
const colors = useThemeColors();
color: colors.text // 而不是 colors.textPrimary
```

---

**文档版本**: v1.5
**最后更新**: 2025年7月22日
**维护团队**: ITBook开发团队
**本次更新**: 新增技能图谱可视化功能开发指南
