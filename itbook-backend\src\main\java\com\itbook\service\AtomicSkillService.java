package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 原子技能服务类
 * 提供原子技能的业务逻辑处理，包括技能的创建、查询、更新、删除等操作
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Service
@Transactional
public class AtomicSkillService {

    private static final Logger log = LoggerFactory.getLogger(AtomicSkillService.class);

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    /**
     * 创建新的原子技能
     * 
     * @param atomicSkill 原子技能实体
     * @return 创建的原子技能
     */
    public AtomicSkill createAtomicSkill(AtomicSkill atomicSkill) {
        log.info("创建原子技能: name={}, category={}", atomicSkill.getName(), atomicSkill.getCategory());
        
        // 验证技能名称是否已存在
        if (atomicSkillRepository.existsByNameAndIsActiveTrue(atomicSkill.getName())) {
            throw new BusinessException("技能名称已存在: " + atomicSkill.getName());
        }
        
        // 设置默认值
        atomicSkill.setIsActive(true);
        atomicSkill.setStatus(AtomicSkill.Status.DRAFT);
        atomicSkill.setCreatedAt(LocalDateTime.now());
        atomicSkill.setUpdatedAt(LocalDateTime.now());
        
        AtomicSkill savedSkill = atomicSkillRepository.save(atomicSkill);
        log.info("原子技能创建成功: id={}, name={}", savedSkill.getId(), savedSkill.getName());
        
        return savedSkill;
    }

    /**
     * 根据ID获取原子技能
     * 
     * @param id 技能ID
     * @return 原子技能实体
     */
    @Transactional(readOnly = true)
    public AtomicSkill getAtomicSkillById(Long id) {
        log.debug("根据ID获取原子技能: id={}", id);
        
        return atomicSkillRepository.findById(id)
                .orElseThrow(() -> new BusinessException("原子技能不存在: " + id));
    }

    /**
     * 更新原子技能
     * 
     * @param id 技能ID
     * @param atomicSkill 更新的技能信息
     * @return 更新后的原子技能
     */
    public AtomicSkill updateAtomicSkill(Long id, AtomicSkill atomicSkill) {
        log.info("更新原子技能: id={}, name={}", id, atomicSkill.getName());
        
        AtomicSkill existingSkill = getAtomicSkillById(id);
        
        // 检查名称是否与其他技能冲突
        if (!existingSkill.getName().equals(atomicSkill.getName()) && 
            atomicSkillRepository.existsByNameAndIsActiveTrue(atomicSkill.getName())) {
            throw new BusinessException("技能名称已存在: " + atomicSkill.getName());
        }
        
        // 更新字段
        existingSkill.setName(atomicSkill.getName());
        existingSkill.setDescription(atomicSkill.getDescription());
        existingSkill.setCategory(atomicSkill.getCategory());
        existingSkill.setSubcategory(atomicSkill.getSubcategory());
        existingSkill.setDifficultyLevel(atomicSkill.getDifficultyLevel());
        existingSkill.setEstimatedHours(atomicSkill.getEstimatedHours());
        existingSkill.setSkillType(atomicSkill.getSkillType());
        existingSkill.setAssessmentMethod(atomicSkill.getAssessmentMethod());
        existingSkill.setPassThreshold(atomicSkill.getPassThreshold());
        existingSkill.setLearningResourcesJson(atomicSkill.getLearningResourcesJson());
        existingSkill.setPracticeExercisesJson(atomicSkill.getPracticeExercisesJson());
        existingSkill.setTagsJson(atomicSkill.getTagsJson());
        existingSkill.setKeywords(atomicSkill.getKeywords());
        existingSkill.setIndustryRelevanceJson(atomicSkill.getIndustryRelevanceJson());
        existingSkill.setUpdatedAt(LocalDateTime.now());
        
        AtomicSkill updatedSkill = atomicSkillRepository.save(existingSkill);
        log.info("原子技能更新成功: id={}, name={}", updatedSkill.getId(), updatedSkill.getName());
        
        return updatedSkill;
    }

    /**
     * 删除原子技能（软删除）
     * 
     * @param id 技能ID
     */
    public void deleteAtomicSkill(Long id) {
        log.info("删除原子技能: id={}", id);
        
        AtomicSkill skill = getAtomicSkillById(id);
        skill.setIsActive(false);
        skill.setUpdatedAt(LocalDateTime.now());
        
        atomicSkillRepository.save(skill);
        log.info("原子技能删除成功: id={}, name={}", skill.getId(), skill.getName());
    }

    /**
     * 分页查询原子技能
     * 
     * @param pageable 分页参数
     * @return 分页的原子技能列表
     */
    @Transactional(readOnly = true)
    public Page<AtomicSkill> getAtomicSkills(Pageable pageable) {
        log.debug("分页查询原子技能: page={}, size={}", pageable.getPageNumber(), pageable.getPageSize());
        
        return atomicSkillRepository.findByIsActiveTrueOrderByCreatedAtDesc(pageable);
    }

    /**
     * 根据分类查询原子技能
     * 
     * @param category 技能分类
     * @param pageable 分页参数
     * @return 分页的原子技能列表
     */
    @Transactional(readOnly = true)
    public Page<AtomicSkill> getAtomicSkillsByCategory(String category, Pageable pageable) {
        log.debug("根据分类查询原子技能: category={}", category);
        
        return atomicSkillRepository.findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(category, pageable);
    }

    /**
     * 搜索原子技能
     * 
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 搜索结果
     */
    @Transactional(readOnly = true)
    public Page<AtomicSkill> searchAtomicSkills(String keyword, Pageable pageable) {
        log.debug("搜索原子技能: keyword={}", keyword);
        
        return atomicSkillRepository.findByNameContainingIgnoreCaseAndIsActiveTrueOrderByCreatedAtDesc(keyword, pageable);
    }

    /**
     * 根据职业目标推荐技能
     * 
     * @param careerGoalId 职业目标ID
     * @param excludeSkillIds 排除的技能ID集合
     * @return 推荐的技能列表
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getRecommendedSkillsForCareer(Long careerGoalId, Set<Long> excludeSkillIds) {
        log.debug("根据职业目标推荐技能: careerGoalId={}, excludeCount={}", 
                careerGoalId, excludeSkillIds != null ? excludeSkillIds.size() : 0);
        
        if (excludeSkillIds == null || excludeSkillIds.isEmpty()) {
            excludeSkillIds = new HashSet<>();
            excludeSkillIds.add(-1L); // 使用一个不存在的ID作为占位符
        }
        
        return atomicSkillRepository.findRecommendedSkillsForCareer(careerGoalId, excludeSkillIds);
    }

    /**
     * 发布技能（将状态改为已发布）
     * 
     * @param id 技能ID
     * @return 发布后的技能
     */
    public AtomicSkill publishAtomicSkill(Long id) {
        log.info("发布原子技能: id={}", id);
        
        AtomicSkill skill = getAtomicSkillById(id);
        skill.setStatus(AtomicSkill.Status.PUBLISHED);
        skill.setUpdatedAt(LocalDateTime.now());
        
        AtomicSkill publishedSkill = atomicSkillRepository.save(skill);
        log.info("原子技能发布成功: id={}, name={}", publishedSkill.getId(), publishedSkill.getName());
        
        return publishedSkill;
    }

    /**
     * 获取所有技能分类
     * 
     * @return 技能分类列表
     */
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        log.debug("获取所有技能分类");
        
        return atomicSkillRepository.findDistinctCategoriesByIsActiveTrue();
    }

    /**
     * 根据难度级别查询技能
     * 
     * @param difficultyLevel 难度级别
     * @param pageable 分页参数
     * @return 分页的技能列表
     */
    @Transactional(readOnly = true)
    public Page<AtomicSkill> getAtomicSkillsByDifficulty(AtomicSkill.DifficultyLevel difficultyLevel, Pageable pageable) {
        log.debug("根据难度级别查询技能: difficultyLevel={}", difficultyLevel);
        
        return atomicSkillRepository.findByDifficultyLevelAndIsActiveTrueOrderByCreatedAtDesc(difficultyLevel, pageable);
    }

    /**
     * 获取热门技能（按平均评分排序）
     *
     * @param pageable 分页参数
     * @return 热门技能列表
     */
    @Transactional(readOnly = true)
    public Page<AtomicSkill> getPopularAtomicSkills(Pageable pageable) {
        log.debug("获取热门技能");

        return atomicSkillRepository.findByIsActiveTrueOrderByAverageRatingDescCreatedAtDesc(pageable);
    }

    /**
     * 批量创建原子技能
     *
     * @param atomicSkills 原子技能列表
     * @return 创建的原子技能列表
     */
    public List<AtomicSkill> createAtomicSkillsBatch(List<AtomicSkill> atomicSkills) {
        log.info("批量创建原子技能: count={}", atomicSkills.size());

        // 验证技能名称是否重复
        for (AtomicSkill skill : atomicSkills) {
            if (atomicSkillRepository.existsByNameAndIsActiveTrue(skill.getName())) {
                throw new BusinessException("技能名称已存在: " + skill.getName());
            }

            // 设置默认值
            skill.setIsActive(true);
            skill.setStatus(AtomicSkill.Status.DRAFT);
            skill.setCreatedAt(LocalDateTime.now());
            skill.setUpdatedAt(LocalDateTime.now());
        }

        List<AtomicSkill> savedSkills = atomicSkillRepository.saveAll(atomicSkills);
        log.info("批量创建原子技能成功: count={}", savedSkills.size());

        return savedSkills;
    }

    /**
     * 批量删除原子技能（软删除）
     *
     * @param skillIds 技能ID列表
     */
    public void deleteAtomicSkillsBatch(Set<Long> skillIds) {
        log.info("批量删除原子技能: ids={}", skillIds);

        List<AtomicSkill> skills = atomicSkillRepository.findAllById(skillIds);
        for (AtomicSkill skill : skills) {
            skill.setIsActive(false);
            skill.setUpdatedAt(LocalDateTime.now());
        }

        atomicSkillRepository.saveAll(skills);
        log.info("批量删除原子技能成功: count={}", skills.size());
    }

    /**
     * 批量发布技能
     *
     * @param skillIds 技能ID列表
     * @return 发布的技能数量
     */
    public int publishAtomicSkillsBatch(Set<Long> skillIds) {
        log.info("批量发布原子技能: ids={}", skillIds);

        List<AtomicSkill> skills = atomicSkillRepository.findAllById(skillIds);
        for (AtomicSkill skill : skills) {
            skill.setStatus(AtomicSkill.Status.PUBLISHED);
            skill.setUpdatedAt(LocalDateTime.now());
        }

        List<AtomicSkill> publishedSkills = atomicSkillRepository.saveAll(skills);
        log.info("批量发布原子技能成功: count={}", publishedSkills.size());

        return publishedSkills.size();
    }

    /**
     * 获取技能统计信息
     *
     * @return 技能统计数据
     */
    @Transactional(readOnly = true)
    public AtomicSkillStatistics getSkillStatistics() {
        log.debug("获取技能统计信息");

        Long totalSkills = atomicSkillRepository.countActiveSkills();
        Double averageRating = atomicSkillRepository.getAverageRating();
        Double averageCompletionRate = atomicSkillRepository.getAverageCompletionRate();

        return new AtomicSkillStatistics(totalSkills, averageRating, averageCompletionRate);
    }

    /**
     * 技能统计信息内部类
     */
    public static class AtomicSkillStatistics {
        private final Long totalSkills;
        private final Double averageRating;
        private final Double averageCompletionRate;

        public AtomicSkillStatistics(Long totalSkills, Double averageRating, Double averageCompletionRate) {
            this.totalSkills = totalSkills;
            this.averageRating = averageRating;
            this.averageCompletionRate = averageCompletionRate;
        }

        public Long getTotalSkills() { return totalSkills; }
        public Double getAverageRating() { return averageRating; }
        public Double getAverageCompletionRate() { return averageCompletionRate; }
    }
}
