package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.entity.UserAtomicSkillMastery;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.SkillRelationshipRepository;
import com.itbook.repository.UserAtomicSkillMasteryRepository;
import com.itbook.dto.AtomicSkillDTO;
import com.itbook.dto.SkillGraphDTO;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 原子技能服务类
 * 提供原子技能的CRUD操作、技能图谱查询、技能推荐等核心功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class AtomicSkillService {

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private SkillRelationshipRepository skillRelationshipRepository;

    @Autowired
    private UserAtomicSkillMasteryRepository masteryRepository;

    /**
     * 创建原子技能
     */
    public ApiResponse<AtomicSkill> createAtomicSkill(AtomicSkillDTO skillDTO) {
        try {
            // 检查技能编码是否已存在
            if (atomicSkillRepository.existsBySkillCode(skillDTO.getSkillCode())) {
                return ApiResponse.error("技能编码已存在: " + skillDTO.getSkillCode());
            }

            AtomicSkill skill = convertToEntity(skillDTO);
            skill.setStatus(AtomicSkill.Status.DRAFT);
            skill.setCreatedBy(skillDTO.getCreatedBy());
            
            AtomicSkill savedSkill = atomicSkillRepository.save(skill);
            return ApiResponse.success(savedSkill);
        } catch (Exception e) {
            return ApiResponse.error("创建原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 更新原子技能
     */
    public ApiResponse<AtomicSkill> updateAtomicSkill(Long skillId, AtomicSkillDTO skillDTO) {
        try {
            AtomicSkill existingSkill = atomicSkillRepository.findById(skillId)
                    .orElseThrow(() -> new RuntimeException("技能不存在"));

            // 更新基本信息
            existingSkill.setName(skillDTO.getName());
            existingSkill.setDescription(skillDTO.getDescription());
            existingSkill.setCategory(skillDTO.getCategory());
            existingSkill.setSubcategory(skillDTO.getSubcategory());
            existingSkill.setDifficultyLevel(skillDTO.getDifficultyLevel());
            existingSkill.setEstimatedHours(skillDTO.getEstimatedHours());
            existingSkill.setSkillType(skillDTO.getSkillType());
            existingSkill.setUpdatedBy(skillDTO.getUpdatedBy());

            AtomicSkill updatedSkill = atomicSkillRepository.save(existingSkill);
            return ApiResponse.success(updatedSkill);
        } catch (Exception e) {
            return ApiResponse.error("更新原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取原子技能
     */
    public ApiResponse<AtomicSkill> getAtomicSkillById(Long skillId) {
        try {
            AtomicSkill skill = atomicSkillRepository.findById(skillId)
                    .orElseThrow(() -> new RuntimeException("技能不存在"));
            return ApiResponse.success(skill);
        } catch (Exception e) {
            return ApiResponse.error("获取原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询原子技能
     */
    public ApiResponse<Page<AtomicSkill>> getAtomicSkills(
            String category, 
            String subcategory,
            AtomicSkill.DifficultyLevel difficultyLevel,
            AtomicSkill.Status status,
            Pageable pageable) {
        try {
            Page<AtomicSkill> skills = atomicSkillRepository.findByFilters(
                    category, subcategory, difficultyLevel, status, pageable);
            return ApiResponse.success(skills);
        } catch (Exception e) {
            return ApiResponse.error("查询原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 搜索原子技能
     */
    public ApiResponse<List<AtomicSkill>> searchAtomicSkills(String keyword) {
        try {
            List<AtomicSkill> skills = atomicSkillRepository.searchByKeyword(keyword);
            return ApiResponse.success(skills);
        } catch (Exception e) {
            return ApiResponse.error("搜索原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能的前置技能
     */
    public ApiResponse<List<AtomicSkill>> getPrerequisiteSkills(Long skillId) {
        try {
            List<SkillRelationship> relationships = skillRelationshipRepository
                    .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);
            
            List<AtomicSkill> prerequisites = relationships.stream()
                    .map(rel -> rel.getSourceSkill())
                    .collect(Collectors.toList());
            
            return ApiResponse.success(prerequisites);
        } catch (Exception e) {
            return ApiResponse.error("获取前置技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能的后续技能
     */
    public ApiResponse<List<AtomicSkill>> getSuccessorSkills(Long skillId) {
        try {
            List<SkillRelationship> relationships = skillRelationshipRepository
                    .findBySourceSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.SUCCESSOR);
            
            List<AtomicSkill> successors = relationships.stream()
                    .map(rel -> rel.getTargetSkill())
                    .collect(Collectors.toList());
            
            return ApiResponse.success(successors);
        } catch (Exception e) {
            return ApiResponse.error("获取后续技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取相关技能
     */
    public ApiResponse<List<AtomicSkill>> getRelatedSkills(Long skillId) {
        try {
            List<SkillRelationship> relationships = skillRelationshipRepository
                    .findRelatedSkills(skillId);
            
            Set<AtomicSkill> relatedSkills = new HashSet<>();
            for (SkillRelationship rel : relationships) {
                if (rel.getSourceSkillId().equals(skillId)) {
                    relatedSkills.add(rel.getTargetSkill());
                } else {
                    relatedSkills.add(rel.getSourceSkill());
                }
            }
            
            return ApiResponse.success(new ArrayList<>(relatedSkills));
        } catch (Exception e) {
            return ApiResponse.error("获取相关技能失败: " + e.getMessage());
        }
    }

    /**
     * 构建技能图谱
     */
    public ApiResponse<SkillGraphDTO> buildSkillGraph(Long rootSkillId, int depth) {
        try {
            AtomicSkill rootSkill = atomicSkillRepository.findById(rootSkillId)
                    .orElseThrow(() -> new RuntimeException("根技能不存在"));

            SkillGraphDTO graph = new SkillGraphDTO();
            Set<Long> visitedSkills = new HashSet<>();
            
            buildGraphRecursively(rootSkill, graph, visitedSkills, depth, 0);
            
            return ApiResponse.success(graph);
        } catch (Exception e) {
            return ApiResponse.error("构建技能图谱失败: " + e.getMessage());
        }
    }

    /**
     * 递归构建技能图谱
     */
    private void buildGraphRecursively(AtomicSkill skill, SkillGraphDTO graph, 
                                     Set<Long> visitedSkills, int maxDepth, int currentDepth) {
        if (currentDepth >= maxDepth || visitedSkills.contains(skill.getId())) {
            return;
        }

        visitedSkills.add(skill.getId());
        graph.addNode(skill);

        if (currentDepth < maxDepth - 1) {
            // 获取所有关系
            List<SkillRelationship> relationships = skillRelationshipRepository
                    .findBySourceSkillIdOrTargetSkillId(skill.getId(), skill.getId());

            for (SkillRelationship rel : relationships) {
                graph.addEdge(rel);
                
                AtomicSkill nextSkill = rel.getSourceSkillId().equals(skill.getId()) 
                        ? rel.getTargetSkill() : rel.getSourceSkill();
                
                buildGraphRecursively(nextSkill, graph, visitedSkills, maxDepth, currentDepth + 1);
            }
        }
    }

    /**
     * 获取用户技能掌握度
     */
    public ApiResponse<UserAtomicSkillMastery> getUserSkillMastery(Long userId, Long skillId) {
        try {
            UserAtomicSkillMastery mastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, skillId)
                    .orElse(null);
            
            if (mastery == null) {
                // 创建默认掌握度记录
                mastery = new UserAtomicSkillMastery(userId, skillId);
                mastery = masteryRepository.save(mastery);
            }
            
            return ApiResponse.success(mastery);
        } catch (Exception e) {
            return ApiResponse.error("获取用户技能掌握度失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户技能掌握度
     */
    public ApiResponse<UserAtomicSkillMastery> updateUserSkillMastery(
            Long userId, Long skillId, UserAtomicSkillMastery.MasteryLevel masteryLevel, 
            BigDecimal masteryScore) {
        try {
            UserAtomicSkillMastery mastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, skillId)
                    .orElse(new UserAtomicSkillMastery(userId, skillId));

            mastery.setMasteryLevel(masteryLevel);
            mastery.setMasteryScore(masteryScore);
            
            // 更新置信度（基于评估次数和分数）
            BigDecimal confidence = calculateConfidence(mastery);
            mastery.setConfidenceLevel(confidence);

            UserAtomicSkillMastery updatedMastery = masteryRepository.save(mastery);
            return ApiResponse.success(updatedMastery);
        } catch (Exception e) {
            return ApiResponse.error("更新用户技能掌握度失败: " + e.getMessage());
        }
    }

    /**
     * 推荐学习技能
     */
    public ApiResponse<List<AtomicSkill>> recommendSkillsForUser(Long userId, Long careerGoalId) {
        try {
            // 获取用户当前掌握的技能
            List<UserAtomicSkillMastery> userMasteries = masteryRepository
                    .findByUserIdAndMasteryLevelNot(userId, UserAtomicSkillMastery.MasteryLevel.NONE);
            
            Set<Long> masteredSkillIds = userMasteries.stream()
                    .map(UserAtomicSkillMastery::getAtomicSkillId)
                    .collect(Collectors.toSet());

            // 基于职业目标和已掌握技能推荐下一步学习的技能
            List<AtomicSkill> recommendations = atomicSkillRepository
                    .findRecommendedSkillsForCareer(careerGoalId, masteredSkillIds);

            return ApiResponse.success(recommendations);
        } catch (Exception e) {
            return ApiResponse.error("推荐学习技能失败: " + e.getMessage());
        }
    }

    /**
     * 计算置信度
     */
    private BigDecimal calculateConfidence(UserAtomicSkillMastery mastery) {
        // 基于评估次数和最近评估分数计算置信度
        int assessmentCount = mastery.getAssessmentCount();
        BigDecimal lastScore = mastery.getLastAssessmentScore();
        
        if (assessmentCount == 0 || lastScore == null) {
            return BigDecimal.ZERO;
        }
        
        // 简单的置信度计算公式
        BigDecimal baseConfidence = lastScore.divide(new BigDecimal("100"));
        BigDecimal assessmentFactor = new BigDecimal(Math.min(assessmentCount, 10)).divide(new BigDecimal("10"));
        
        return baseConfidence.multiply(assessmentFactor);
    }

    /**
     * DTO转实体
     */
    private AtomicSkill convertToEntity(AtomicSkillDTO dto) {
        AtomicSkill skill = new AtomicSkill();
        skill.setSkillCode(dto.getSkillCode());
        skill.setName(dto.getName());
        skill.setDescription(dto.getDescription());
        skill.setCategory(dto.getCategory());
        skill.setSubcategory(dto.getSubcategory());
        skill.setDifficultyLevel(dto.getDifficultyLevel());
        skill.setEstimatedHours(dto.getEstimatedHours());
        skill.setSkillType(dto.getSkillType());
        skill.setAssessmentMethod(dto.getAssessmentMethod());
        skill.setPassThreshold(dto.getPassThreshold());
        skill.setKeywords(dto.getKeywords());
        return skill;
    }
}
