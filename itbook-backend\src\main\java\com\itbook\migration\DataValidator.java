package com.itbook.migration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据验证器
 * 负责验证数据迁移的完整性和正确性
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Component
public class DataValidator {

    private static final Logger logger = LoggerFactory.getLogger(DataValidator.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 验证迁移结果
     */
    public ValidationResult validateMigration() {
        ValidationResult result = new ValidationResult();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        try {
            logger.info("开始验证数据迁移结果...");

            // 验证数据完整性
            validateDataIntegrity(errors, warnings);

            // 验证映射关系
            validateMappingRelationships(errors, warnings);

            // 验证数据一致性
            validateDataConsistency(errors, warnings);

            // 验证业务逻辑
            validateBusinessLogic(errors, warnings);

            // 验证性能指标
            validatePerformance(errors, warnings);

            result.setErrors(errors);
            result.setWarnings(warnings);
            result.setValid(errors.isEmpty());

            if (result.isValid()) {
                result.setMessage("数据迁移验证通过");
                logger.info("数据迁移验证通过");
            } else {
                result.setErrorMessage("数据迁移验证失败，发现 " + errors.size() + " 个错误");
                logger.error("数据迁移验证失败，错误: {}", errors);
            }

        } catch (Exception e) {
            logger.error("数据验证过程中发生异常", e);
            errors.add("验证过程异常: " + e.getMessage());
            result.setErrors(errors);
            result.setValid(false);
            result.setErrorMessage("验证过程异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证数据完整性
     */
    private void validateDataIntegrity(List<String> errors, List<String> warnings) {
        logger.info("验证数据完整性...");

        try {
            // 验证原始数据是否完整保留
            Integer originalCareerSkills = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill", Integer.class);
            Integer backupCareerSkills = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_backup", Integer.class);

            if (!originalCareerSkills.equals(backupCareerSkills)) {
                errors.add("career_skill表数据不完整，原始: " + backupCareerSkills + "，当前: " + originalCareerSkills);
            }

            // 验证原子技能是否生成
            Integer atomicSkillCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill", Integer.class);
            if (atomicSkillCount == 0) {
                errors.add("未生成任何原子技能");
            } else if (atomicSkillCount < originalCareerSkills) {
                warnings.add("原子技能数量(" + atomicSkillCount + ")少于原始技能数量(" + originalCareerSkills + ")");
            }

            // 验证映射关系是否建立
            Integer mappingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_mapping", Integer.class);
            if (mappingCount == 0) {
                errors.add("未建立任何映射关系");
            }

            // 验证必要字段是否完整
            Integer nullNameCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill WHERE name IS NULL OR name = ''", Integer.class);
            if (nullNameCount > 0) {
                errors.add("存在 " + nullNameCount + " 个原子技能名称为空");
            }

            Integer nullCodeCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill WHERE skill_code IS NULL OR skill_code = ''", Integer.class);
            if (nullCodeCount > 0) {
                errors.add("存在 " + nullCodeCount + " 个原子技能编码为空");
            }

        } catch (Exception e) {
            errors.add("数据完整性验证异常: " + e.getMessage());
        }
    }

    /**
     * 验证映射关系
     */
    private void validateMappingRelationships(List<String> errors, List<String> warnings) {
        logger.info("验证映射关系...");

        try {
            // 验证每个career_skill都有对应的映射
            List<Map<String, Object>> unmappedSkills = jdbcTemplate.queryForList("""
                SELECT cs.id, cs.skill_name 
                FROM career_skill cs 
                LEFT JOIN career_skill_mapping csm ON cs.id = csm.career_skill_id 
                WHERE csm.id IS NULL
                """);

            if (!unmappedSkills.isEmpty()) {
                errors.add("存在 " + unmappedSkills.size() + " 个未映射的职业技能");
                for (Map<String, Object> skill : unmappedSkills) {
                    warnings.add("未映射的技能: " + skill.get("skill_name"));
                }
            }

            // 验证映射的置信度分布
            List<Map<String, Object>> confidenceStats = jdbcTemplate.queryForList("""
                SELECT 
                    CASE 
                        WHEN confidence_score >= 0.8 THEN 'HIGH'
                        WHEN confidence_score >= 0.6 THEN 'MEDIUM'
                        WHEN confidence_score >= 0.4 THEN 'LOW'
                        ELSE 'VERY_LOW'
                    END as confidence_level,
                    COUNT(*) as count
                FROM career_skill_mapping 
                GROUP BY confidence_level
                """);

            int veryLowCount = 0;
            for (Map<String, Object> stat : confidenceStats) {
                if ("VERY_LOW".equals(stat.get("confidence_level"))) {
                    veryLowCount = ((Number) stat.get("count")).intValue();
                }
            }

            if (veryLowCount > 0) {
                warnings.add("存在 " + veryLowCount + " 个极低置信度的映射关系");
            }

            // 验证映射权重的合理性
            Integer invalidWeightCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM career_skill_mapping 
                WHERE weight < 0 OR weight > 1
                """, Integer.class);

            if (invalidWeightCount > 0) {
                errors.add("存在 " + invalidWeightCount + " 个无效权重的映射关系");
            }

        } catch (Exception e) {
            errors.add("映射关系验证异常: " + e.getMessage());
        }
    }

    /**
     * 验证数据一致性
     */
    private void validateDataConsistency(List<String> errors, List<String> warnings) {
        logger.info("验证数据一致性...");

        try {
            // 验证技能编码唯一性
            Integer duplicateCodeCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM (
                    SELECT skill_code, COUNT(*) as cnt 
                    FROM atomic_skill 
                    GROUP BY skill_code 
                    HAVING cnt > 1
                ) as duplicates
                """, Integer.class);

            if (duplicateCodeCount > 0) {
                errors.add("存在 " + duplicateCodeCount + " 个重复的技能编码");
            }

            // 验证分类一致性
            List<String> invalidCategories = jdbcTemplate.queryForList("""
                SELECT DISTINCT category FROM atomic_skill 
                WHERE category NOT IN ('programming_language', 'development_framework', 
                                      'database', 'web_technology', 'architecture_design', 'general')
                """, String.class);

            if (!invalidCategories.isEmpty()) {
                warnings.add("存在非标准分类: " + String.join(", ", invalidCategories));
            }

            // 验证难度级别一致性
            Integer invalidDifficultyCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM atomic_skill 
                WHERE difficulty_level NOT IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT')
                """, Integer.class);

            if (invalidDifficultyCount > 0) {
                errors.add("存在 " + invalidDifficultyCount + " 个无效的难度级别");
            }

            // 验证预计时长的合理性
            Integer unreasonableHoursCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM atomic_skill 
                WHERE estimated_hours <= 0 OR estimated_hours > 1000
                """, Integer.class);

            if (unreasonableHoursCount > 0) {
                warnings.add("存在 " + unreasonableHoursCount + " 个不合理的预计学习时长");
            }

        } catch (Exception e) {
            errors.add("数据一致性验证异常: " + e.getMessage());
        }
    }

    /**
     * 验证业务逻辑
     */
    private void validateBusinessLogic(List<String> errors, List<String> warnings) {
        logger.info("验证业务逻辑...");

        try {
            // 验证技能状态
            Integer inactiveSkillCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM atomic_skill 
                WHERE is_active = false OR status != 'PUBLISHED'
                """, Integer.class);

            if (inactiveSkillCount > 0) {
                warnings.add("存在 " + inactiveSkillCount + " 个非活跃或未发布的技能");
            }

            // 验证评分范围
            Integer invalidRatingCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM atomic_skill 
                WHERE average_rating < 0 OR average_rating > 5
                """, Integer.class);

            if (invalidRatingCount > 0) {
                errors.add("存在 " + invalidRatingCount + " 个无效评分的技能");
            }

            // 验证完成率范围
            Integer invalidCompletionRateCount = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM atomic_skill 
                WHERE completion_rate < 0 OR completion_rate > 1
                """, Integer.class);

            if (invalidCompletionRateCount > 0) {
                errors.add("存在 " + invalidCompletionRateCount + " 个无效完成率的技能");
            }

        } catch (Exception e) {
            errors.add("业务逻辑验证异常: " + e.getMessage());
        }
    }

    /**
     * 验证性能指标
     */
    private void validatePerformance(List<String> errors, List<String> warnings) {
        logger.info("验证性能指标...");

        try {
            // 验证查询性能
            long startTime = System.currentTimeMillis();
            jdbcTemplate.queryForObject("SELECT COUNT(*) FROM atomic_skill", Integer.class);
            long queryTime = System.currentTimeMillis() - startTime;

            if (queryTime > 1000) {
                warnings.add("原子技能表查询耗时过长: " + queryTime + "ms");
            }

            // 验证表大小
            List<Map<String, Object>> tableStats = jdbcTemplate.queryForList("""
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name IN ('atomic_skill', 'career_skill_mapping', 'skill_relationship')
                """);

            for (Map<String, Object> stat : tableStats) {
                String tableName = (String) stat.get("table_name");
                Number sizeMb = (Number) stat.get("size_mb");
                
                if (sizeMb != null && sizeMb.doubleValue() > 100) {
                    warnings.add("表 " + tableName + " 大小过大: " + sizeMb + "MB");
                }
            }

        } catch (Exception e) {
            warnings.add("性能验证异常: " + e.getMessage());
        }
    }

    /**
     * 快速验证迁移状态
     */
    public boolean isValidMigration() {
        try {
            Integer atomicSkillCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill", Integer.class);
            Integer mappingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_mapping", Integer.class);
            
            return atomicSkillCount > 0 && mappingCount > 0;
        } catch (Exception e) {
            logger.error("快速验证失败", e);
            return false;
        }
    }

    /**
     * 获取验证摘要
     */
    public Map<String, Object> getValidationSummary() {
        try {
            Integer atomicSkillCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill", Integer.class);
            Integer mappingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_mapping", Integer.class);
            Integer careerSkillCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill", Integer.class);
            
            Double avgConfidence = jdbcTemplate.queryForObject(
                "SELECT AVG(confidence_score) FROM career_skill_mapping", Double.class);
            
            return Map.of(
                "atomicSkillCount", atomicSkillCount,
                "mappingCount", mappingCount,
                "careerSkillCount", careerSkillCount,
                "averageConfidence", avgConfidence != null ? avgConfidence : 0.0,
                "isValid", isValidMigration(),
                "coverageRate", careerSkillCount > 0 ? (double) mappingCount / careerSkillCount : 0.0
            );
            
        } catch (Exception e) {
            logger.error("获取验证摘要失败", e);
            return Map.of("error", "获取验证摘要失败: " + e.getMessage());
        }
    }
}
