# ITBook技术文档

## 📋 技术架构概览

### 整体架构
```
ITBook学习平台
├── 前端 (React Native + Expo)
│   ├── 用户界面层 (UI Components)
│   ├── 业务逻辑层 (Business Logic)
│   ├── 数据管理层 (State Management)
│   └── 服务接口层 (API Services)
├── 后端 (Java Spring Boot) [已实现]
│   ├── 控制器层 (Controllers) - 认证、课程、健康检查、推荐职位
│   ├── 数据传输层 (DTOs) - JobDTO等企业级DTO模式
│   ├── 服务层 (Services) - 用户服务、课程服务、推荐算法服务
│   ├── 数据访问层 (Repositories) - JPA数据访问
│   └── 数据库层 (MySQL) - 开发/生产环境
└── 基础设施 (Infrastructure)
    ├── 缓存系统 (JVM Local Cache)
    ├── 文件存储 (File Storage)
    └── 监控系统 (Monitoring)
```

## 🛠️ 核心技术栈

### 前端技术栈
```typescript
// 核心框架
React Native: "0.72.x"
Expo: "~49.0.0"
TypeScript: "^5.0.0"

// UI组件库
react-native-ui-lib: "^7.0.0"
@expo/vector-icons: "^13.0.0"

// 导航系统
@react-navigation/native: "^6.0.0"
@react-navigation/native-stack: "^6.0.0"
@react-navigation/bottom-tabs: "^6.0.0"

// 状态管理 (企业级架构)
React Hooks (useState, useEffect, useContext)
Redux Toolkit: "@reduxjs/toolkit"
React Redux: "react-redux"
Redux Persist: "redux-persist" (长期业务状态持久化)
AsyncStorage: "@react-native-async-storage/async-storage"
多层缓存系统: src/utils/cache.ts (短期临时数据)

// 开发工具
ESLint: "^8.0.0"
Prettier: "^3.0.0"
```

### 后端技术栈 (已实现)
```java
// 核心框架
Spring Boot: "2.7.0"
Spring Security: "5.7.0"
Spring Data JPA: "2.7.0"
Spring Web: "5.3.21"

// 数据库
MySQL: "8.0.33"
MySQL Connector: "8.0.33"
Caffeine Cache: "3.1.1" // JVM本地缓存

// 工具库
BCrypt: "0.4" // 密码加密
SpringDoc OpenAPI: "1.6.9" // API文档
Jackson: "2.13.3" // JSON处理

// 构建工具
Maven: "3.8+"
Java: "1.8+"

// 服务配置
Server Port: 8888
Context Path: /api
Database: itbook_dev/itbook_prod
```

## 📁 项目结构

### 前端目录结构
```
src/
├── components/           # 可复用组件
│   ├── achievement/     # 成就系统组件
│   ├── common/          # 通用组件
│   ├── community/       # 社区功能组件
│   ├── dashboard/       # 仪表板组件
│   ├── discover/        # 发现模块组件
│   ├── learning/        # 学习模块组件
│   ├── portfolio/       # 作品集组件
│   └── recommendation/  # 推荐系统组件
├── screens/             # 页面组件
│   ├── achievement/     # 成就系统页面
│   ├── assessment/      # 技能评估页面
│   ├── visualization/   # 可视化页面 (技能图谱)
│   ├── community/       # 社区功能页面
│   ├── discover/        # 发现模块页面
│   ├── interview/       # 面试练习页面
│   ├── jobs/           # 求职模块页面
│   ├── learning/       # 学习模块页面
│   ├── portfolio/      # 作品集页面
│   ├── profile/        # 个人中心页面
│   └── study/          # 学习打卡页面
├── navigation/          # 导航配置
├── types/              # TypeScript类型定义
├── data/               # 模拟数据
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
└── constants/          # 常量定义
```

### 后端目录结构
```
itbook-backend/
├── src/main/java/com/itbook/
│   ├── controller/          # 控制器层
│   │   ├── AuthController.java      # 用户认证控制器
│   │   ├── CourseController.java    # 课程管理控制器
│   │   └── HealthController.java    # 健康检查控制器
│   ├── service/            # 服务层
│   │   ├── UserService.java        # 用户服务
│   │   └── CourseService.java      # 课程服务
│   ├── repository/         # 数据访问层
│   │   ├── UserRepository.java     # 用户数据访问
│   │   ├── CourseRepository.java   # 课程数据访问
│   │   ├── ArticleRepository.java  # 文章数据访问
│   │   ├── JobRepository.java      # 职位数据访问
│   │   └── CompanyRepository.java  # 公司数据访问
│   ├── entity/             # 实体类
│   │   ├── User.java              # 用户实体
│   │   ├── Course.java            # 课程实体
│   │   ├── Article.java           # 文章实体
│   │   ├── Job.java               # 职位实体
│   │   ├── Company.java           # 公司实体
│   │   ├── Role.java              # 角色实体
│   │   ├── Lesson.java            # 课时实体
│   │   ├── UserProfile.java       # 用户画像实体
│   │   ├── UserSkillAssessment.java # 技能评估实体
│   │   └── LearningPath.java      # 学习路径实体
│   ├── config/             # 配置类
│   │   ├── SecurityConfig.java     # 安全配置
│   │   ├── WebConfig.java          # Web配置
│   │   └── DataInitializer.java    # 数据初始化
│   ├── common/             # 通用类
│   │   └── ApiResponse.java        # 统一响应格式
│   ├── security/           # 安全相关
│   └── util/              # 工具类
├── src/main/resources/
│   ├── application.yml             # 主配置文件
│   ├── application-dev.yml         # 开发环境配置
│   ├── application-prod.yml        # 生产环境配置
│   └── db/                        # 数据库脚本
│       ├── init-dev.sql           # 开发环境初始化
│       ├── data-dev.sql           # 开发环境数据
│       └── data-prod.sql          # 生产环境数据
└── pom.xml                        # Maven配置文件
```

## 🔧 核心技术实现

### 1. 导航系统架构

```typescript
// 导航类型定义
export type RootStackParamList = {
  MainTabs: undefined;
  // 功能页面
  CodeEditor: undefined;
  InterviewPractice: undefined;
  SkillAssessment: undefined;
  ProjectDetail: { projectId: string };
  AchievementCenter: undefined;
  // ... 其他页面
};

// 主导航结构
const RootNavigator = () => (
  <NavigationContainer>
    <Stack.Navigator>
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      {/* 其他页面 */}
    </Stack.Navigator>
  </NavigationContainer>
);
```

### 2. 组件设计模式

#### 个性化路径卡片组件
```typescript
interface SmartRecommendationCardProps {
  recommendations: RecommendationData;
  onItemPress?: (item: RecommendationItem) => void;
  maxItems?: number;
}

export const SmartRecommendationCard: React.FC<SmartRecommendationCardProps> = ({
  recommendations,
  onItemPress,
  maxItems = 5,
}) => {
  // 组件实现
};
```

#### 成就系统组件
```typescript
interface AchievementCardProps {
  userAchievement: UserAchievement;
  onPress?: (userAchievement: UserAchievement) => void;
  compact?: boolean;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
  userAchievement,
  onPress,
  compact = false,
}) => {
  // 组件实现
};
```

### 3. 资讯功能技术实现

#### 3.1 资讯数据模型
```typescript
interface News {
  id: number;
  title: string;
  summary: string;
  content?: string;
  coverImage?: string;
  source: string;
  sourceUrl?: string;
  author: string;
  categoryId: number;
  category?: NewsCategory;
  tags: string;
  tagList?: string[];
  status: string;
  priority: number;
  isFeatured: boolean;
  isTrending: boolean;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  commentCount: number;
  publishedAt: string;
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
}

interface NewsCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

#### 3.2 资讯API服务
```typescript
// 资讯列表API调用
const response = await apiService.get<PagedResponse<News>>('/news?page=0&size=20&sortBy=publishedAt&sortDir=desc');

// 资讯详情API调用
const response = await apiService.get<News>(`/news/${newsId}`);

// 点赞功能API调用
const response = await apiService.post<string>(`/news/${newsId}/like`);
```

#### 3.3 资讯组件架构
- **NewsListComponent**: 资讯列表展示组件
- **NewsDetailScreen**: 资讯详情页面组件
- **导航集成**: 使用React Navigation实现页面跳转
- **状态管理**: 本地状态管理加载、错误处理
- **UI设计**: 遵循Material Design 3设计规范

#### 3.4 前后端集成特性
- ✅ 真实API数据替代模拟数据
- ✅ 完整的错误处理和fallback机制
- ✅ TypeScript类型安全保障
- ✅ 响应式设计和移动端优化
- ✅ 企业级代码质量标准

### 4. 状态管理策略

#### 本地状态管理
```typescript
// 使用React Hooks进行状态管理
const [user, setUser] = useState<User | null>(null);
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

// 自定义Hook示例
export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  };
  
  const logout = () => {
    // 登出逻辑
  };
  
  return { user, isAuthenticated, login, logout };
};
```

#### 数据持久化
```typescript
// AsyncStorage使用示例
import AsyncStorage from '@react-native-async-storage/async-storage';

export const StorageService = {
  async setItem(key: string, value: any): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Storage setItem error:', error);
    }
  },
  
  async getItem<T>(key: string): Promise<T | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  },
};
```

### 4. 类型系统设计

#### 核心数据类型
```typescript
// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  level: number;
  totalPoints: number;
  createdAt: Date;
  updatedAt: Date;
}

// 学习内容类型
export interface LearningContent {
  id: string;
  title: string;
  description: string;
  type: ContentType;
  difficulty: DifficultyLevel;
  duration: number;
  tags: string[];
  author: User;
  createdAt: Date;
}

// 成就系统类型
export interface Achievement {
  id: string;
  title: string;
  description: string;
  type: AchievementType;
  difficulty: AchievementDifficulty;
  icon: string;
  points: number;
  requirements: AchievementRequirement[];
}
```

### 5. 性能优化策略

#### 组件优化
```typescript
// 使用React.memo优化组件渲染
export const AchievementCard = React.memo<AchievementCardProps>(({
  userAchievement,
  onPress,
  compact = false,
}) => {
  // 组件实现
});

// 使用useMemo优化计算
const filteredAchievements = useMemo(() => {
  return achievements.filter(achievement => 
    achievement.type === selectedType
  );
}, [achievements, selectedType]);

// 使用useCallback优化函数
const handleAchievementPress = useCallback((achievement: UserAchievement) => {
  onPress?.(achievement);
}, [onPress]);
```

#### 列表优化
```typescript
// 使用FlatList优化长列表渲染
<FlatList
  data={achievements}
  renderItem={({ item }) => (
    <AchievementCard
      userAchievement={item}
      onPress={handleAchievementPress}
    />
  )}
  keyExtractor={(item) => item.id}
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  windowSize={10}
  initialNumToRender={5}
/>
```

## 🎨 UI/UX设计规范

### 设计系统
```typescript
// 颜色系统
export const Colors = {
  primary: '#007AFF',      // 主色调
  secondary: '#5856D6',    // 辅助色
  success: '#34C759',      // 成功色
  warning: '#FF9500',      // 警告色
  error: '#FF3B30',        // 错误色
  
  // 语义化颜色
  text: '#000000',         // 主文本
  textSecondary: '#8E8E93', // 次要文本
  background: '#FFFFFF',    // 背景色
  surface: '#F2F2F7',      // 表面色
  border: '#C6C6C8',       // 边框色
};

// 间距系统
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// 字体系统
export const Typography = {
  h1: { fontSize: 32, fontWeight: 'bold' },
  h2: { fontSize: 24, fontWeight: 'bold' },
  h3: { fontSize: 20, fontWeight: '600' },
  body: { fontSize: 16, fontWeight: 'normal' },
  caption: { fontSize: 12, fontWeight: 'normal' },
};
```

### 组件样式规范
```typescript
// 统一的样式规范
const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
});
```

## 🔒 安全与最佳实践

### 代码安全
```typescript
// 输入验证
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 错误处理
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  return '网络错误，请稍后重试';
};

// 数据清理
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
```

### 性能监控
```typescript
// 性能监控工具
export const PerformanceMonitor = {
  startTimer: (name: string) => {
    console.time(name);
  },
  
  endTimer: (name: string) => {
    console.timeEnd(name);
  },
  
  logMemoryUsage: () => {
    if (__DEV__) {
      console.log('Memory usage:', performance.memory);
    }
  },
};
```

## 📊 数据管理策略

### 模拟数据管理
```typescript
// 数据工厂模式
export class DataFactory {
  static createUser(overrides?: Partial<User>): User {
    return {
      id: generateId(),
      username: 'user_' + Date.now(),
      email: '<EMAIL>',
      displayName: '学习者',
      level: 1,
      totalPoints: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
  
  static createAchievement(overrides?: Partial<Achievement>): Achievement {
    return {
      id: generateId(),
      title: '示例成就',
      description: '这是一个示例成就',
      type: AchievementType.LEARNING,
      difficulty: AchievementDifficulty.EASY,
      icon: '🎯',
      points: 10,
      requirements: [],
      ...overrides,
    };
  }
}
```

### 缓存策略
```typescript
// 简单的内存缓存实现
class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private ttl = 5 * 60 * 1000; // 5分钟TTL
  
  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear(): void {
    this.cache.clear();
  }
}

export const cache = new MemoryCache();
```

## 📱 离线功能与数据同步

### 离线存储架构
```typescript
// 离线存储服务
export class OfflineStorage {
  private static instance: OfflineStorage;
  private readonly CACHE_KEY = 'itbook_cached_content';
  private readonly CONFIG_KEY = 'itbook_cache_config';
  private readonly OPERATIONS_KEY = 'itbook_offline_operations';

  // 缓存内容
  async cacheContent(content: CachedContent): Promise<boolean> {
    try {
      const existingContent = await this.getCachedContent();
      const updatedContent = existingContent.filter(item => item.id !== content.id);
      updatedContent.push(content);

      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(updatedContent));
      return true;
    } catch (error) {
      console.error('Failed to cache content:', error);
      return false;
    }
  }

  // 获取缓存内容
  async getCachedContent(): Promise<CachedContent[]> {
    try {
      const data = await AsyncStorage.getItem(this.CACHE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get cached content:', error);
      return [];
    }
  }
}
```

### 网络状态监控
```typescript
// 网络状态Hook
export const useNetworkStatus = () => {
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: true,
    isInternetReachable: null,
    type: null,
    details: null,
  });

  useEffect(() => {
    const checkNetworkStatus = () => {
      const online = navigator.onLine !== false;
      setNetworkState({
        isConnected: online,
        isInternetReachable: online,
        type: online ? 'wifi' : 'none',
        details: { strength: online ? 'excellent' : 'none' },
      });
    };

    // 监听网络状态变化
    window.addEventListener('online', checkNetworkStatus);
    window.addEventListener('offline', checkNetworkStatus);

    return () => {
      window.removeEventListener('online', checkNetworkStatus);
      window.removeEventListener('offline', checkNetworkStatus);
    };
  }, []);

  return { networkState, isOnline: networkState.isConnected };
};
```

### 缓存管理系统
```typescript
// 缓存管理器
export class CacheManager {
  private static instance: CacheManager;
  private offlineStorage: OfflineStorage;
  private downloadQueue: CacheItem[] = [];
  private currentDownloads: Map<string, CacheProgress> = new Map();

  // 智能缓存推荐
  async getSmartCacheRecommendations(): Promise<CacheItem[]> {
    return [
      {
        id: 'rec_1',
        type: 'article',
        title: '推荐文章：React Hooks 最佳实践',
        priority: 'high',
        metadata: { reason: '基于您的学习历史推荐' },
      },
    ];
  }

  // 批量缓存内容
  async batchCacheContent(items: CacheItem[]): Promise<void> {
    const sortedItems = items.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    this.downloadQueue.push(...sortedItems);
    this.processDownloadQueue();
  }
}
```

### 数据同步机制
```typescript
// 离线操作同步
export const useOfflineSync = () => {
  const { isOnline } = useNetworkStatus();
  const [offlineStorage] = useState(() => OfflineStorage.getInstance());

  const syncOfflineOperations = useCallback(async (): Promise<void> => {
    if (!isOnline) return;

    try {
      const operations = await offlineStorage.getOfflineOperations();
      const pendingOperations = operations.filter(op => !op.synced);

      for (const operation of pendingOperations) {
        try {
          // 模拟同步操作到服务器
          await simulateSyncOperation(operation);
          await offlineStorage.markOperationSynced(operation.id);
        } catch (error) {
          console.error(`Failed to sync operation ${operation.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to sync offline operations:', error);
    }
  }, [isOnline, offlineStorage]);

  return { syncOfflineOperations };
};
```

### 离线功能集成
```typescript
// 离线功能Hook
export const useOffline = () => {
  const { isOnline, networkStatus } = useNetworkStatus();
  const [cacheManager] = useState(() => CacheManager.getInstance());
  const [offlineStorage] = useState(() => OfflineStorage.getInstance());

  // 缓存内容
  const cacheContent = useCallback(async (item: CacheItem, content?: string): Promise<boolean> => {
    try {
      if (content) {
        return await cacheManager.cacheContent(item, content);
      } else {
        await cacheManager.batchCacheContent([item]);
        return true;
      }
    } catch (error) {
      console.error('Failed to cache content:', error);
      return false;
    }
  }, [cacheManager]);

  return {
    isOnline,
    networkStatus,
    cacheContent,
    getCachedContent: cacheManager.getCachedContent.bind(cacheManager),
    isContentCached: cacheManager.isContentCached.bind(cacheManager),
  };
};
```

## 🎯 职业导向就业加速器技术实现

### 职业目标数据模型设计

```typescript
// 核心职业目标接口
interface CareerGoal {
  id: number;
  name: string;                    // 职业目标名称
  category: string;                // 职业分类
  description: string;             // 职业描述
  icon: string;                    // 职业图标

  coreSkills: SkillRequirement[];  // 核心技能
  bonusSkills: SkillRequirement[]; // 加分技能
  careerPath: { [level in JobLevel]?: CareerStep; };
  projects: { [level in JobLevel]?: ProjectTemplate[]; };
  interviewTopics: InterviewTopic[];
  marketData: JobMarketData;
}

// 用户职业偏好
interface UserCareerPreference {
  userId: string;
  targetCareer?: CareerGoal;
  currentLevel: JobLevel;
  targetLevel: JobLevel;
  isCareerSelected: boolean;
  selectedAt?: string;
  lastUpdated: string;
}

// 职业进度跟踪
interface CareerProgress {
  userId: string;
  careerId: number;
  overallProgress: number;         // 整体进度 0-100
  skillProgress: UserSkillProgress[];
  completedProjects: string[];
  currentPhase: string;
  estimatedCompletion: string;
  milestones: Milestone[];
}
```

### 智能推荐引擎架构

```typescript
// 推荐引擎核心类
class RecommendationEngine {
  // 推荐权重配置
  private defaultWeights: RecommendationWeights = {
    skillGap: 0.3,        // 技能差距权重
    difficulty: 0.2,      // 难度匹配权重
    popularity: 0.15,     // 内容热度权重
    recency: 0.1,         // 内容新鲜度权重
    userBehavior: 0.15,   // 用户行为权重
    jobRelevance: 0.1     // 岗位相关性权重
  };

  // 生成个性化推荐
  async generateRecommendations(
    careerPreference: UserCareerPreference,
    careerProgress: CareerProgress,
    userBehavior: UserBehavior,
    availableContent: ContentMetadata[],
    maxItems: number = 10
  ): Promise<RecommendedContent[]>

  // 计算推荐分数
  private calculateRecommendationScore(
    content: ContentMetadata,
    careerPreference: UserCareerPreference,
    careerProgress: CareerProgress,
    userBehavior: UserBehavior
  ): number
}
```

### 内容标签系统

```typescript
// 内容标签服务
class ContentTaggingService {
  // 从内容中提取标签
  extractTagsFromContent(
    contentId: string,
    title: string,
    description: string,
    contentType: 'course' | 'article' | 'project' | 'question'
  ): string[]

  // 计算内容与职业的相关性
  calculateCareerRelevance(
    contentId: string,
    title: string,
    description: string,
    contentType: string,
    extractedTags: string[]
  ): ContentCareerRelevance

  // 批量处理内容标签
  async batchProcessContent(contents: ContentItem[]): Promise<ContentCareerRelevance[]>
}

// 内容职业相关性
interface ContentCareerRelevance {
  contentId: string;
  contentType: 'course' | 'article' | 'project' | 'question';
  careerRelevance: {
    [careerId: string]: {
      score: number;           // 0-1相关性分数
      level: 'high' | 'medium' | 'low';
      reason: string;          // 相关性原因
      tags: string[];          // 相关标签
    };
  };
  extractedTags: string[];     // 从内容中提取的标签
  manualTags: string[];        // 手动添加的标签
  lastUpdated: string;
}
```

### Redux状态管理集成

```typescript
// 职业规划相关的Redux Slice
interface CareerState {
  allJobs: JobProfile[];
  popularJobs: JobProfile[];
  jobPreference: UserJobPreference | null;
  hasJobSelected: boolean;
  jobProgress: JobProgress | null;
  overallProgress: number;
  recommendations: RecommendedContent[];
  jobMatch: JobMatchAnalysis | null;
  personalization: PersonalizationConfig;
  isLoading: boolean;
  error: string | null;
}

// 异步操作
export const loadJobsAsync = createAsyncThunk('career/loadJobs', ...)
export const selectJobAsync = createAsyncThunk('career/selectJob', ...)
export const generateRecommendationsAsync = createAsyncThunk('career/generateRecommendations', ...)
```

### 组件架构设计

```typescript
// 岗位进度卡片组件
const JobProgressCard: React.FC<JobProgressCardProps> = ({
  jobPreference,
  jobProgress,
  onSetJobTarget,
  onViewProgress,
  onViewRecommendations,
}) => {
  // 渲染岗位信息、进度条、技能进度、下一步建议等
}

// 岗位推荐卡片组件
const JobRecommendationCard: React.FC<JobRecommendationCardProps> = ({
  recommendations,
  isLoading,
  onRefresh,
  onItemPress,
  onViewAll,
}) => {
  // 渲染推荐内容列表、相关性分数、推荐理由等
}

// 岗位选择引导页面
const JobSelectionScreen: React.FC<JobSelectionScreenProps> = ({
  navigation,
  onJobSelected
}) => {
  // 分步骤选择流程：技术方向 → 具体岗位 → 目标级别 → 确认选择
}
```

### 服务层架构

```typescript
// 岗位服务类
class JobService {
  // 岗位数据管理
  async getAllJobs(): Promise<JobProfile[]>
  async getJobById(jobId: string): Promise<JobProfile | null>
  async getPopularJobs(): Promise<JobProfile[]>

// 职业规划服务类 (第一阶段新增)
class CareerService {
  // 岗位数据持久化存储
  async cacheJobData(jobs: JobProfile[]): Promise<boolean>
  async getCachedJobData(): Promise<JobProfile[] | null>
  async getJobsWithOfflineSupport(): Promise<JobProfile[]>

  // 用户行为跟踪
  async trackUserBehavior(userId: string, jobId: string, actionType: string, actionData?: any): Promise<boolean>
  async getUserBehaviorData(userId: string): Promise<UserBehaviorData[] | null>
  async analyzeUserBehaviorPattern(userId: string): Promise<BehaviorPattern>

  // 岗位推荐算法基础
  async generateBehaviorBasedRecommendations(userId: string): Promise<RecommendedContent[]>

  // 数据同步机制
  async syncPendingData(): Promise<boolean>
  async getSyncStats(): Promise<SyncStatus>
}

  // 用户偏好管理
  async saveJobPreference(userId: string, jobId: string, level: JobLevel): Promise<boolean>
  async getJobPreference(userId: string): Promise<UserJobPreference | null>
  async clearJobPreference(userId: string): Promise<boolean>

  // 进度跟踪
  async getJobProgress(userId: string): Promise<JobProgress | null>
  async updateSkillProgress(userId: string, skillId: string, progress: number): Promise<boolean>

  // 推荐生成
  async generateJobRecommendations(userId: string): Promise<RecommendedContent[]>
  async analyzeJobMatch(userId: string, jobId: string): Promise<JobMatchAnalysis | null>
}
```

### Hook设计模式

```typescript
// 职业规划Hook
export const useCareer = (): UseCareerReturn => {
  const dispatch = useAppDispatch();
  const careerState = useAppSelector((state) => state.career);

  // 提供状态数据和操作方法
  return {
    // 状态数据
    allJobs, popularJobs, jobPreference, hasJobSelected,
    jobProgress, overallProgress, recommendations, jobMatch,

    // 操作方法
    loadJobs, loadUserCareerData, selectJob, clearJobSelection,
    updateSkillProgress, refreshRecommendations, analyzeJobMatch,

    // 配置方法
    setPersonalizationConfig, setRecommendationModeConfig,

    // 清理方法
    clearErrorState, resetCareer
  };
};

// 岗位推荐Hook
export const useJobRecommendations = (userId?: string) => {
  const { recommendations, isLoading, error } = useAppSelector((state) => state.career);

  return { recommendations, isLoading, error, refresh };
};
```

## 🚀 岗位专属功能技术实现 (第三阶段)

### 项目实战平台架构

```typescript
// 项目实战服务核心类
class ProjectWorkshopService {
  // 项目模板管理
  async getAllProjectTemplates(): Promise<EnhancedProjectTemplate[]>
  async getRecommendedProjects(userId: string, jobId?: string): Promise<EnhancedProjectTemplate[]>

  // 项目进度管理
  async startProject(userId: string, projectId: string): Promise<boolean>
  async updateTaskProgress(userId: string, projectId: string, taskId: string): Promise<boolean>

  // 统计分析
  async getUserStats(userId: string): Promise<ProjectWorkshopStats>
}

// 增强的项目模板
interface EnhancedProjectTemplate {
  phases: ProjectPhase[];           // 项目阶段
  assessments: ProjectAssessment[]; // 评估标准
  techStack: TechStackConfig;       // 技术栈配置
  certification?: CertificationInfo; // 认证信息
}
```

### 岗位定制面试系统

```typescript
// 岗位面试服务
class JobInterviewService {
  // 面试题管理
  async getJobInterviewQuestions(jobId: string): Promise<JobSpecificInterviewQuestion[]>
  async getHighFrequencyQuestions(jobId: string): Promise<JobSpecificInterviewQuestion[]>

  // 准备计划
  async generatePreparationPlan(userId: string, jobId: string): Promise<InterviewPreparationPlan>

  // 模拟面试
  async createMockInterviewSession(userId: string, jobId: string): Promise<MockInterviewSession>
}

// 岗位面试题扩展
interface JobSpecificInterviewQuestion extends InterviewQuestion {
  jobRelevance: {
    [jobId: string]: {
      score: number;                // 相关性分数
      importance: 'critical' | 'important' | 'nice_to_have';
      frequency: number;            // 出现频率
    };
  };
  scoringCriteria?: ScoringCriteria; // 评分标准
}
```

### 作品集生成系统

```typescript
// 作品集生成服务
class PortfolioGenerationService {
  // 生成配置建议
  async generatePortfolioSuggestions(userId: string, jobId?: string): Promise<PortfolioGenerationConfig>

  // 创建生成任务
  async createGenerationTask(config: PortfolioGenerationConfig): Promise<PortfolioGenerationTask>

  // 执行生成流程
  private async executeGenerationTask(task: PortfolioGenerationTask): Promise<void>
}

// 生成任务管理
interface PortfolioGenerationTask {
  steps: GenerationStep[];          // 生成步骤
  progress: number;                 // 进度百分比
  result?: GenerationResult;        // 生成结果
}
```

### 求职模块增强

```typescript
// 岗位推荐服务
class JobRecommendationService {
  // 智能推荐
  async generateJobRecommendations(userId: string, userSkills: SkillMap): Promise<JobRecommendation[]>

  // 匹配分析
  async analyzeSpecificJob(jobId: string, userSkills: SkillMap): Promise<JobMatchAnalysis>

  // 相似岗位
  async getSimilarJobs(jobId: string): Promise<JobProfile[]>
}

// 岗位匹配分析
interface JobMatchAnalysis {
  overallMatch: number;             // 整体匹配度
  skillGaps: SkillGap[];           // 技能差距
  readinessAssessment: ReadinessInfo; // 准备度评估
}
```

### 求职板块UI重构 (2025-07-14)

#### 重构目标
1. **功能聚焦**：删除非核心功能，保留4个核心功能
2. **UI简化**：移除复杂的标签页导航，采用功能卡片式布局
3. **用户体验**：突出显示核心功能，提升操作效率
4. **代码清理**：删除无用代码，提高维护性

#### 技术实现

```typescript
// 重构前的JobsScreen结构
interface OldJobsScreenState {
  currentTab: 'recommended' | 'all' | 'applied';  // 3个标签页
  featuredJobs: Job[];
  recommendedJobs: Job[];
  recentJobs: Job[];
  allJobs: Job[];
}

// 重构后的JobsScreen结构
interface NewJobsScreenState {
  allJobs: Job[];                    // 简化状态管理
  isRefreshing: boolean;
  isLoading: boolean;
}

// 核心功能配置
const coreFeatures = [
  {
    key: 'recommendations',
    label: '智能推荐',
    icon: 'search',
    route: 'JobRecommendations'
  },
  {
    key: 'applied',
    label: '已申请',
    icon: 'checkmark-circle',
    route: null  // 显示在当前页面
  },
  {
    key: 'analysis',
    label: '匹配分析',
    icon: 'analytics',
    route: 'JobMatchAnalysis'
  },
  {
    key: 'interview',
    label: '面试准备',
    icon: 'school',
    route: 'InterviewPreparation'
  }
];
```

#### 组件架构变更

```typescript
// 删除的组件和方法
- renderRecommendedJobs()     // 推荐职位渲染
- renderAllJobs()             // 全部职位渲染
- renderTabBar()              // 标签页导航

// 新增的组件和方法
+ renderCoreFeatures()        // 核心功能入口
+ renderAppliedJobsHeader()   // 已申请职位标题

// 保留的组件和方法
✓ renderAppliedJobs()         // 已申请职位列表
✓ handleJobPress()            // 职位点击处理
✓ handleBookmark()            // 收藏处理
```

#### 路由配置更新

```typescript
// 新增JobMatchAnalysisScreen页面
import JobMatchAnalysisScreen from '../screens/jobs/JobMatchAnalysisScreen';

// 替换占位组件
- const JobMatchAnalysisScreen = () => null;  // 删除占位组件
+ component={JobMatchAnalysisScreen as any}   // 使用真实组件
```

### 在线代码编辑器

```typescript
// 代码编辑器服务
class OnlineCodeEditorService {
  // 项目环境
  async createProjectCodeEnvironment(userId: string, projectId: string): Promise<CodeSnippet>

  // 代码执行
  async executeCode(userId: string, snippetId: string, code: string): Promise<CodeExecutionResult>

  // 协作编程
  async createCollaborativeSession(userId: string, snippetId: string): Promise<CollaborativeCodingSession>
}

// 项目集成配置
interface ProjectWorkshopIntegration {
  config: {
    autoSync: boolean;              // 自动同步
    allowCollaboration: boolean;    // 允许协作
    enableVersionControl: boolean;  // 版本控制
  };
  progressSync: ProgressSyncConfig; // 进度同步
}
```

### 数据流架构

```typescript
// 服务层交互
ProjectWorkshopService ←→ JobInterviewService ←→ PortfolioGenerationService
                ↓                    ↓                        ↓
        OnlineCodeEditorService ←→ JobRecommendationService
                ↓                    ↓
        ContentTaggingService ←→ RecommendationEngine
```

### 状态管理集成

```typescript
// Redux状态扩展
interface AppState {
  career: CareerState;              // 职业规划状态
  projectWorkshop: ProjectWorkshopState; // 项目实战状态
  interview: InterviewState;        // 面试准备状态
  portfolio: PortfolioState;        // 作品集状态
  codeEditor: CodeEditorState;      // 代码编辑器状态
}

// 异步操作
export const startProjectAsync = createAsyncThunk('projectWorkshop/startProject', ...)
export const generatePortfolioAsync = createAsyncThunk('portfolio/generate', ...)
export const executeCodeAsync = createAsyncThunk('codeEditor/execute', ...)
```

### 性能优化策略

```typescript
// 缓存策略
class ServiceCache {
  // 项目模板缓存
  private projectTemplatesCache: Map<string, EnhancedProjectTemplate[]>

  // 面试题缓存
  private interviewQuestionsCache: Map<string, JobSpecificInterviewQuestion[]>

  // 推荐结果缓存
  private recommendationsCache: Map<string, JobRecommendation[]>
}

// 懒加载策略
const ProjectWorkshopScreen = lazy(() => import('./ProjectWorkshopScreen'));
const CodeEditorScreen = lazy(() => import('./CodeEditorScreen'));
```

---

## 🎯 第一阶段：岗位目标管理与展示系统

### 架构设计

```typescript
// 第一阶段系统架构
岗位目标管理与展示系统
├── 前端页面层
│   └── CareerManagementScreen          # 岗位管理页面
├── 服务层
│   ├── CareerService                   # 职业规划服务
│   └── JobService (扩展)               # 岗位服务扩展
├── 状态管理层
│   ├── careerSlice (扩展)              # Redux状态管理扩展
│   └── useCareer Hook (扩展)           # 自定义Hook扩展
└── 数据存储层
    ├── AsyncStorage                    # 本地数据存储
    ├── 行为数据缓存                     # 用户行为跟踪数据
    └── 同步队列管理                     # 离线数据同步
```

### CareerService 核心实现

```typescript
// 职业规划服务核心类
class CareerService {
  private static instance: CareerService;
  private readonly BEHAVIOR_DATA_KEY = 'itbook_user_behavior';
  private readonly MARKET_TRENDS_KEY = 'itbook_market_trends';
  private readonly SYNC_STATUS_KEY = 'itbook_sync_status';
  private readonly CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24小时

  // 单例模式
  public static getInstance(): CareerService {
    if (!CareerService.instance) {
      CareerService.instance = new CareerService();
    }
    return CareerService.instance;
  }

  // 岗位数据持久化存储
  async cacheJobData(jobs: JobProfile[]): Promise<boolean> {
    try {
      const cacheData = {
        jobs,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };
      await storageService.setItem('cached_job_data', cacheData);
      return true;
    } catch (error) {
      console.error('岗位数据缓存失败:', error);
      return false;
    }
  }

  // 支持离线访问的岗位数据获取
  async getJobsWithOfflineSupport(): Promise<JobProfile[]> {
    try {
      // 首先尝试从缓存获取
      const cachedJobs = await this.getCachedJobData();
      if (cachedJobs) {
        return cachedJobs;
      }

      // 缓存不存在或过期，从JobService获取
      const jobs = await this.jobService.getAllJobs();
      await this.cacheJobData(jobs);
      return jobs;
    } catch (error) {
      // 返回过期缓存作为备用
      const cacheData = await storageService.getItem('cached_job_data');
      return cacheData?.jobs || [];
    }
  }

  // 用户行为跟踪
  async trackUserBehavior(
    userId: string,
    jobId: string,
    actionType: 'view' | 'like' | 'share' | 'apply' | 'bookmark' | 'learn' | 'practice',
    actionData?: any
  ): Promise<boolean> {
    try {
      const behaviorData: UserBehaviorData = {
        userId,
        jobId,
        actionType,
        actionData,
        timestamp: new Date().toISOString(),
        sessionId: this.currentSessionId
      };

      // 保存到本地存储
      const existingData = await this.getUserBehaviorData(userId) || [];
      existingData.push(behaviorData);
      await storageService.setItem(`${this.BEHAVIOR_DATA_KEY}_${userId}`, existingData);

      // 添加到待同步队列
      await this.addToPendingSyncQueue(behaviorData);
      return true;
    } catch (error) {
      console.error('用户行为记录失败:', error);
      return false;
    }
  }

  // 分析用户行为模式
  async analyzeUserBehaviorPattern(userId: string): Promise<BehaviorPattern> {
    const behaviorData = await this.getUserBehaviorData(userId) || [];

    // 分析偏好的岗位类别、活跃时间段、学习偏好
    const jobCategoryCount: { [category: string]: number } = {};
    const timeSlotCount: { [timeSlot: string]: number } = {};
    const actionTypeCount: { [actionType: string]: number } = {};

    for (const behavior of behaviorData) {
      // 统计分析逻辑
      const job = await this.jobService.getJobById(behavior.jobId);
      if (job) {
        jobCategoryCount[job.category] = (jobCategoryCount[job.category] || 0) + 1;
      }

      const hour = new Date(behavior.timestamp).getHours();
      const timeSlot = this.getTimeSlot(hour);
      timeSlotCount[timeSlot] = (timeSlotCount[timeSlot] || 0) + 1;

      actionTypeCount[behavior.actionType] = (actionTypeCount[behavior.actionType] || 0) + 1;
    }

    return {
      preferredJobCategories: Object.entries(jobCategoryCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category),
      mostActiveTimeSlots: Object.entries(timeSlotCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 2)
        .map(([timeSlot]) => timeSlot),
      learningPreferences: Object.entries(actionTypeCount)
        .filter(([actionType]) => ['learn', 'practice', 'view'].includes(actionType))
        .sort(([,a], [,b]) => b - a)
        .map(([actionType]) => actionType),
      engagementScore: Math.min(100, behaviorData.length * 2)
    };
  }

  // 数据同步机制
  async syncPendingData(): Promise<boolean> {
    try {
      const syncStatus = await this.getSyncStatus();

      if (syncStatus.syncInProgress || syncStatus.pendingActions.length === 0) {
        return true;
      }

      // 标记同步开始
      syncStatus.syncInProgress = true;
      await this.saveSyncStatus(syncStatus);

      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 同步完成，清空待同步队列
      syncStatus.pendingActions = [];
      syncStatus.syncInProgress = false;
      syncStatus.lastSyncTime = new Date().toISOString();
      delete syncStatus.lastSyncError;

      await this.saveSyncStatus(syncStatus);
      return true;
    } catch (error) {
      console.error('数据同步失败:', error);
      return false;
    }
  }
}
```

### CareerManagementScreen 页面架构

```typescript
// 岗位管理页面组件
const CareerManagementScreen: React.FC = ({ navigation }) => {
  const colors = useThemeColors();
  const { user } = useAuth();
  const {
    jobPreference,
    hasJobSelected,
    jobProgress,
    overallProgress,
    recommendations,
    syncStats,
    isLoading,
    error,
    loadUserCareerData,
    trackUserBehavior,
    syncCareerData,
  } = useCareer();

  // 页面组件结构
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 头部导航 */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        {/* 导航按钮和标题 */}
      </View>

      <ScrollView style={styles.content}>
        {/* 岗位信息卡片 */}
        {renderJobInfoCard()}

        {/* 技能进度卡片 */}
        {renderSkillProgressCard()}

        {/* 学习资源推荐卡片 */}
        {renderLearningResourcesCard()}

        {/* 市场分析卡片 */}
        {renderMarketAnalysisCard()}

        {/* 数据同步状态卡片 */}
        {renderDataSyncCard()}

        {/* 开发者测试卡片 (仅开发环境) */}
        {renderDeveloperTestCard()}
      </ScrollView>
    </View>
  );
};
```

### Redux 状态管理扩展

```typescript
// careerSlice 扩展
interface CareerState {
  // 原有状态...

  // 新增数据同步状态
  syncStats: {
    lastSyncTime: string;
    pendingCount: number;
    isOnline: boolean;
    lastError?: string;
  } | null;
}

// 新增异步操作
export const trackUserBehaviorAsync = createAsyncThunk(
  'career/trackUserBehavior',
  async ({ userId, jobId, actionType, actionData }) => {
    const careerService = CareerService.getInstance();
    const success = await careerService.trackUserBehavior(userId, jobId, actionType, actionData);
    if (!success) {
      throw new Error('行为跟踪失败');
    }
    return { userId, jobId, actionType, actionData };
  }
);

export const syncCareerDataAsync = createAsyncThunk(
  'career/syncCareerData',
  async () => {
    const careerService = CareerService.getInstance();
    const success = await careerService.syncPendingData();
    if (!success) {
      throw new Error('数据同步失败');
    }
    const syncStats = await careerService.getSyncStats();
    return syncStats;
  }
);
```

### 测试架构

```typescript
// CareerManagementTest 测试套件
export async function runPhaseOneTests(): Promise<boolean> {
  const testResults = [];

  // 执行所有测试
  testResults.push(await testCareerServiceBasicFunctions());
  testResults.push(await testJobDataCaching());
  testResults.push(await testDataPersistence());

  // 统计测试结果
  const passedTests = testResults.filter(result => result).length;
  const totalTests = testResults.length;

  return passedTests === totalTests;
}

// 基本功能测试
async function testCareerServiceBasicFunctions(): Promise<boolean> {
  try {
    const careerService = CareerService.getInstance();

    // 测试离线岗位数据获取
    const jobs = await careerService.getJobsWithOfflineSupport();

    // 测试用户行为跟踪
    const trackResult = await careerService.trackUserBehavior(
      'test_user_001',
      'frontend-developer',
      'view',
      { page: 'career-management' }
    );

    // 测试行为模式分析
    const behaviorPattern = await careerService.analyzeUserBehaviorPattern('test_user_001');

    // 测试数据同步
    const syncResult = await careerService.syncPendingData();

    return jobs.length > 0 && trackResult && syncResult;
  } catch (error) {
    console.error('基本功能测试失败:', error);
    return false;
  }
}
```

### 性能优化策略

```typescript
// 缓存管理
class CareerServiceCache {
  private jobDataCache: Map<string, { data: JobProfile[], timestamp: number }> = new Map();
  private behaviorCache: Map<string, UserBehaviorData[]> = new Map();

  // 智能缓存策略
  getCachedJobData(key: string): JobProfile[] | null {
    const cached = this.jobDataCache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_EXPIRY_TIME) {
      this.jobDataCache.delete(key);
      return null;
    }

    return cached.data;
  }

  // 内存优化
  cleanupExpiredCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.jobDataCache.entries()) {
      if (now - cached.timestamp > this.CACHE_EXPIRY_TIME) {
        this.jobDataCache.delete(key);
      }
    }
  }
}
```

## 🎯 用户画像系统API接口

### 用户画像管理
```http
# 获取或创建用户画像
GET /api/user-profiles/{userId}/or-create
Response: {
  "code": 20000,
  "message": "success",
  "data": {
    "id": 1,
    "userId": 1,
    "currentSkillLevel": 3,
    "learningStyle": "MIXED",
    "availableTimePerWeek": 20,
    "careerGoal": "成为Java后端开发工程师",
    "preferredLearningPace": "NORMAL",
    "workExperienceYears": 2,
    "educationLevel": "本科",
    "targetSalaryRange": "15K-20K",
    "learningMotivation": "提升技术能力，获得更好的职业发展"
  }
}

# 创建/更新用户画像
POST /api/user-profiles
Request: {
  "userId": 1,
  "currentSkillLevel": 3,
  "learningStyle": "MIXED",
  "availableTimePerWeek": 20,
  "careerGoal": "成为Java后端开发工程师",
  "preferredLearningPace": "NORMAL",
  "workExperienceYears": 2,
  "educationLevel": "本科",
  "targetSalaryRange": "15K-20K"
}
```

### 技能评估管理
```http
# 获取用户技能评估列表
GET /api/user-skill-assessments/user/{userId}
Response: {
  "code": 20000,
  "message": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "userId": 1,
        "skillName": "Java",
        "skillCategory": "Programming Language",
        "currentLevel": 3,
        "targetLevel": 4,
        "assessmentMethod": "SELF_EVALUATION",
        "lastAssessedAt": "2024-01-15T10:30:00"
      }
    ],
    "totalElements": 3
  }
}

# 创建技能评估
POST /api/user-skill-assessments
Request: {
  "userId": 1,
  "skillName": "Spring Boot",
  "skillCategory": "Framework",
  "currentLevel": 2,
  "targetLevel": 4,
  "assessmentMethod": "SELF_EVALUATION"
}
```

### 个性化推荐系统 (2025-07-10新增)

#### 推荐算法架构
```typescript
// 推荐算法服务
class RecommendationAlgorithmService {
  // 多因子推荐算法
  calculateRecommendationScore(
    userProfile: UserProfile,
    learningPath: LearningPath,
    targetJob?: Job
  ): RecommendationScore {
    const factors = {
      skillLevelMatch: 0.25,    // 技能水平匹配
      learningStyleMatch: 0.20, // 学习风格匹配
      pathPopularity: 0.15,     // 路径受欢迎程度
      jobRelevance: 0.15,       // 职业目标匹配
      pathRating: 0.15,         // 路径评分
      timeAvailability: 0.10    // 时间可用性匹配
    };

    return this.weightedScore(factors, userProfile, learningPath);
  }
}

// 推荐服务
class RecommendationService {
  // 生成个性化推荐
  async generatePersonalizedRecommendations(
    userId: Long,
    targetJobId?: Long,
    limit: number = 10
  ): Promise<RecommendationResult[]>

  // 提交用户反馈
  async submitFeedback(
    userId: Long,
    pathId: Long,
    action: FeedbackAction,
    rating?: number,
    feedback?: string
  ): Promise<RecommendationFeedback>
}
```

## 👤 用户管理系统API接口 (2025-07-16新增)

### 用户基本信息管理
```http
# 获取用户信息
GET /api/users/{userId}
Response: {
  "code": 20000,
  "message": "success",
  "data": {
    "id": 1,
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "nickname": "测试昵称",
    "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=<EMAIL>",
    "bio": "这是我的个人简介，我是一名前端开发工程师，热爱学习新技术。",
    "phone": "13800138002",
    "status": "ACTIVE",
    "createdAt": "2025-01-01T00:00:00",
    "updatedAt": "2025-07-16T10:30:00"
  }
}

# 更新用户基本信息（部分更新）
PATCH /api/users/{userId}
Content-Type: application/json
Body: {
  "nickname": "新昵称",
  "email": "<EMAIL>",
  "phone": "13900139001",
  "bio": "更新后的个人简介"
}
Response: {
  "code": 20000,
  "message": "用户信息更新成功",
  "data": {
    // 更新后的用户信息
  }
}

# 更新用户头像
PATCH /api/users/{userId}/avatar
Content-Type: application/json
Body: {
  "avatar": "https://example.com/avatar.jpg"
}
```

### 文件上传管理
```http
# 上传用户头像
POST /api/upload/avatar/{userId}
Content-Type: multipart/form-data
Body: FormData {
  file: [图片文件]
}
Response: {
  "code": 20000,
  "message": "头像上传成功",
  "data": {
    "url": "/api/uploads/avatars/avatar_1_20250716_103000_abc12345.jpg",
    "user": {
      // 更新后的用户信息
    }
  }
}

# 删除用户头像
DELETE /api/upload/avatar/{userId}
Response: {
  "code": 20000,
  "message": "头像删除成功",
  "data": {
    "url": "https://api.dicebear.com/7.x/avataaars/svg?seed=username",
    "user": {
      // 恢复默认头像后的用户信息
    }
  }
}
```

### 数据验证规则
```typescript
interface UserUpdateValidation {
  nickname: {
    required: true,
    maxLength: 50,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,50}$/, // 中文、英文、数字、下划线
    message: "昵称只能包含中文、英文、数字和下划线"
  },
  email: {
    required: true,
    maxLength: 100,
    pattern: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
    message: "邮箱格式不正确"
  },
  phone: {
    required: false,
    pattern: /^1[3-9]\d{9}$/, // 中国大陆手机号
    message: "手机号格式不正确"
  },
  bio: {
    required: false,
    maxLength: 500,
    message: "个人简介长度不能超过500个字符"
  }
}
```

### 前端组件架构
```typescript
// 用户资料编辑页面
export const EditProfileScreen: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [formData, setFormData] = useState({
    nickname: '',
    email: '',
    phone: '',
    bio: ''
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // 核心功能
  const loadUserProfile = async () => { /* 加载用户信息 */ };
  const handleFieldChange = (field: string, value: string) => { /* 处理字段变化 */ };
  const validateField = (field: string, value: string) => { /* 实时验证 */ };
  const validateForm = () => { /* 表单验证 */ };
  const handleSave = async () => { /* 保存用户信息 */ };
  const uploadAvatar = async (asset: ImagePickerAsset) => { /* 头像上传 */ };
};
```

#### API接口设计
```http
# 获取个性化推荐
GET /api/learning-paths/recommendations/{userId}?targetJobId={jobId}&limit={limit}
Response: {
  "code": 20000,
  "message": "个性化推荐获取成功",
  "data": [
    {
      "path": {
        "id": 1,
        "name": "Java基础入门",
        "description": "Java编程语言基础学习路径",
        "difficultyLevel": "BEGINNER",
        "estimatedHours": 30
      },
      "score": 0.780,
      "reason": "推荐！该学习路径与您的技能水平高度匹配，学习时间安排合理。",
      "algorithmVersion": "v2.0",
      "factors": {
        "skillLevelMatch": 0.85,
        "learningStyleMatch": 0.75,
        "pathPopularity": 0.90,
        "jobRelevance": 0.80,
        "pathRating": 0.88,
        "timeAvailability": 0.70
      }
    }
  ]
}

# 提交推荐反馈
POST /api/learning-paths/recommendations/feedback
Request: {
  "userId": 1,
  "pathId": 1,
  "action": "ACCEPT",
  "rating": 5,
  "feedback": "这个推荐很好，正是我需要的学习路径"
}
Response: {
  "code": 20000,
  "message": "反馈记录成功",
  "data": {
    "id": 1,
    "userId": 1,
    "pathId": 1,
    "action": "ACCEPT",
    "rating": 5,
    "feedback": "这个推荐很好，正是我需要的学习路径",
    "feedbackTime": "2025-07-10T13:30:00"
  }
}

# 刷新推荐
POST /api/learning-paths/recommendations/refresh
Request: {
  "userId": 1,
  "targetJobId": 2,
  "limit": 10
}

# 获取推荐历史
GET /api/learning-paths/recommendations/history?userId={userId}&page={page}&size={size}
```

#### 数据库设计
```sql
-- 推荐记录表
CREATE TABLE path_recommendation (
  id bigint NOT NULL AUTO_INCREMENT,
  user_id bigint NOT NULL COMMENT '用户ID',
  job_id bigint NULL COMMENT '目标岗位ID（可选）',
  recommended_path_id bigint NOT NULL COMMENT '推荐的学习路径ID',
  recommendation_score decimal(5,4) NOT NULL COMMENT '推荐分数',
  recommendation_reason text COMMENT '推荐理由',
  algorithm_version varchar(20) DEFAULT 'v2.0' COMMENT '算法版本',
  recommendation_factors json COMMENT '推荐因子详情',
  created_at datetime NOT NULL,
  updated_at datetime NOT NULL,
  PRIMARY KEY (id),
  INDEX idx_user_job (user_id, job_id),
  INDEX idx_created_at (created_at)
);

-- 推荐反馈表
CREATE TABLE recommendation_feedback (
  id bigint NOT NULL AUTO_INCREMENT,
  user_id bigint NOT NULL COMMENT '用户ID',
  path_id bigint NOT NULL COMMENT '学习路径ID',
  recommendation_id bigint NULL COMMENT '推荐记录ID',
  action enum('ACCEPT','REJECT','LIKE','DISLIKE') NOT NULL COMMENT '反馈动作',
  rating int NULL COMMENT '评分(1-5分)',
  feedback text NULL COMMENT '反馈内容',
  feedback_time datetime NOT NULL COMMENT '反馈时间',
  created_at datetime NOT NULL,
  updated_at datetime NOT NULL,
  PRIMARY KEY (id),
  INDEX idx_user_path (user_id, path_id),
  INDEX idx_feedback_time (feedback_time)
);
```

#### 前端组件架构
```typescript
// 个性化推荐页面
const PersonalizedRecommendationScreen: React.FC = () => {
  const [recommendations, setRecommendations] = useState<RecommendationData[]>([]);
  const [loading, setLoading] = useState(true);

  // 加载推荐数据
  const loadRecommendations = async () => {
    const result = await personalizedRecommendationService
      .getPersonalizedRecommendations(userId);
    if (result.success) {
      setRecommendations(result.data);
    }
  };

  // 处理用户反馈
  const handleAcceptRecommendation = async (pathId: number, pathName: string) => {
    await personalizedRecommendationService.acceptRecommendation(userId, pathId);
    await loadRecommendations(); // 重新加载推荐
  };

  return (
    <View>
      {/* 推荐列表渲染 */}
      {recommendations.map(recommendation => (
        <RecommendationCard
          key={recommendation.id}
          data={recommendation}
          onAccept={() => handleAcceptRecommendation(recommendation.id, recommendation.pathName)}
          onReject={() => handleRejectRecommendation(recommendation.id, recommendation.pathName)}
        />
      ))}
    </View>
  );
};

// 推荐服务类
class PersonalizedRecommendationService {
  async getPersonalizedRecommendations(userId: number, targetJobId?: number) {
    const response = await apiService.get(`/learning-paths/recommendations/${userId}`);
    return this.formatRecommendations(response.data);
  }

  async acceptRecommendation(userId: number, pathId: number, feedback?: string) {
    return await apiService.post('/learning-paths/recommendations/feedback', {
      userId, pathId, action: 'ACCEPT', feedback
    });
  }

  formatRecommendations(recommendations: any[]) {
    return recommendations.map(item => ({
      id: item.path?.id,
      pathName: item.path?.name,
      description: item.path?.description,
      matchScore: Math.round((item.score || 0) * 100),
      recommendationReason: item.reason,
      algorithmVersion: item.algorithmVersion,
      factors: item.factors
    }));
  }
}
```

---

## 🎯 市场分析系统 (2025-07-12新增)

### 系统概述
市场分析系统为用户提供基于职业目标的市场数据分析，包括薪资水平、市场活跃度、职业前景等关键指标，帮助用户做出明智的职业决策。

### 技术架构
```
市场分析系统
├── 前端组件
│   ├── MarketAnalysisScreen - 市场分析页面
│   ├── MarketAnalysisService - 数据服务层
│   └── marketAnalysis.ts - 类型定义
├── 后端服务
│   ├── MarketAnalysisController - 控制器
│   ├── MarketAnalysisService - 业务逻辑
│   └── MarketAnalysisRepository - 数据访问
└── 数据模型
    ├── CareerMarketData - 职业市场数据
    └── 枚举类型 (DemandTrend, CompetitionLevel)
```

### API接口设计
```http
# 获取职业市场分析数据
GET /api/market-analysis/job/{jobId}
Response: {
  "code": 20000,
  "message": "市场分析数据获取成功",
  "data": {
    "id": 1,
    "jobId": 2,
    "marketActivityScore": 82,
    "careerProspectScore": 68,
    "salaryGrowthRate": 12.30,
    "demandTrend": "STABLE",
    "competitionLevel": "MEDIUM",
    "marketInsight": "Java后端开发工程师市场需求稳定...",
    "careerAdvice": "推荐深入学习Spring生态系统...",
    "salaryData": {
      "juniorSalary": 95000,
      "midSalary": 145000,
      "seniorSalary": 260000
    },
    "lastUpdated": "2025-07-12T02:52:48"
  }
}

# 获取推荐职位列表
GET /api/jobs/recommendations?userId={userId}&limit={limit}
Response: {
  "code": 20000,
  "message": "获取推荐职位成功",
  "data": [
    {
      "id": "1",
      "title": "Java后端工程师",
      "company": "阿里巴巴",
      "salary": "25-40K",
      "location": "杭州",
      "matchScore": 92,
      "requirements": ["Java", "Spring Boot", "MySQL"],
      "description": "负责核心业务系统的后端开发，参与高并发系统设计",
      "tags": ["高匹配", "大厂"],
      "isUrgent": false,
      "isRemote": false,
      "publishedAt": "2025-07-15T10:00:00"
    }
  ]
}

# 记录推荐反馈
POST /api/jobs/recommendations/feedback
Request: {
  "jobId": "1",
  "action": "click",
  "userId": 2,
  "timestamp": "2025-07-15T10:30:00"
}
Response: {
  "code": 20000,
  "message": "反馈记录成功"
}
```

## 🎯 推荐职位系统架构 (2025-07-15)

### 系统组件
```
推荐职位系统
├── 后端服务层
│   ├── JobRecommendationService - 推荐算法服务
│   ├── RecommendedJobDTO - 数据传输对象
│   └── JobController - 推荐接口控制器
├── 前端服务层
│   ├── RecommendedJobService - API调用服务
│   ├── RecommendedJobCard - 职位卡片组件
│   └── JobsScreen - 求职页面集成
└── 推荐算法
    ├── 职业目标匹配 (40%权重)
    ├── 技能匹配度计算 (35%权重)
    ├── 地理位置匹配 (15%权重)
    └── 薪资匹配度 (10%权重)
```

### 核心算法逻辑
```java
// 推荐分数计算公式
总分数 = 职业目标匹配分数 × 0.4 +
        技能匹配分数 × 0.35 +
        地理位置匹配分数 × 0.15 +
        薪资匹配分数 × 0.1

// 分数范围: 60-95分，确保合理的匹配度展示
```

### 关键技术特性
1. **数据一致性**: 统一枚举类型命名规范，避免前后端数据不匹配
2. **错误处理**: 完善的异常处理和用户友好的错误提示
3. **性能优化**: 数据缓存和懒加载策略
4. **类型安全**: TypeScript类型定义确保数据结构一致性
5. **响应式设计**: 适配不同屏幕尺寸的UI布局

### 测试覆盖
- ✅ 单元测试: API接口测试
- ✅ 集成测试: 前后端联调测试
- ✅ 推荐职位系统测试: Playwright MCP完整流程验证
  - ✅ 推荐算法正确性: 匹配分数动态计算
  - ✅ 数据完整性: 职位信息、薪资、地点、技能要求
  - ✅ 降级机制: API失败时使用fallback数据
  - ✅ 用户交互: 点击反馈记录功能
  - ✅ 控制台无错误: 前端运行时错误检查

---

## 🔧 数据模型清理与概念统一 (2025-07-13)

### 问题背景
在项目发展过程中，出现了Job（求职）和Career（职业目标）概念混用的问题：
- `types/job.ts`中重复定义了与`types/career.ts`相同的接口
- `careerSlice.ts`中使用了`JobProfile`、`UserJobPreference`等Job相关类型
- `useCareer` Hook中返回`jobPreference`、`jobProgress`等Job相关字段
- 组件中混用`jobPreference?.targetJob`和`careerGoal`

### 清理目标
1. **概念统一**：明确区分Career（职业目标）和Job（求职岗位）
2. **类型统一**：统一使用`types/career.ts`中的类型定义
3. **命名规范**：所有职业目标相关功能使用Career命名
4. **代码清理**：移除重复和废弃的代码

### 技术实现

#### 1. 类型定义清理
```typescript
// 废弃 types/job.ts 中的重复类型定义
// 统一使用 types/career.ts 中的标准类型

// 正确的职业目标类型
interface CareerGoal {
  id: number;
  name: string;
  category: string;
  description: string;
  // ... 其他字段
}

interface UserCareerPreference {
  userId: string;
  targetCareer?: CareerGoal;  // 使用 targetCareer 而非 targetJob
  currentLevel: CareerLevel;
  isCareerSelected: boolean;  // 使用 isCareerSelected 而非 isJobSelected
  // ... 其他字段
}
```

#### 2. 状态管理重构
```typescript
// careerSlice.ts 重构
interface CareerState {
  // 职业目标数据
  allCareerGoals: CareerGoal[];           // 替换 allJobs
  popularCareerGoals: CareerGoal[];       // 替换 popularJobs

  // 用户职业数据
  userCareerPreferences: Record<string, UserCareerPreference>;  // 替换 userJobPreferences
  userCareerProgress: Record<string, CareerProgress>;           // 替换 userJobProgress

  // 兼容性字段
  careerPreference: UserCareerPreference | null;  // 替换 jobPreference
  hasCareerSelected: boolean;                      // 替换 hasJobSelected
  careerProgress: CareerProgress | null;           // 替换 jobProgress
  careerMatch: CareerMatch | null;                 // 替换 jobMatch
}

// 异步操作重命名
export const loadCareerGoalsAsync = createAsyncThunk('career/loadCareerGoals', ...)
export const selectCareerGoalAsync = createAsyncThunk('career/selectCareerGoal', ...)
```

#### 3. Hook层清理
```typescript
// useCareer.ts 更新
interface UseCareerReturn {
  // 状态数据
  allCareerGoals: CareerGoal[];           // 替换 allJobs
  popularCareerGoals: CareerGoal[];       // 替换 popularJobs
  careerPreference: UserCareerPreference; // 替换 jobPreference
  hasCareerSelected: boolean;             // 替换 hasJobSelected
  careerProgress: CareerProgress;         // 替换 jobProgress
  careerMatch: CareerMatch;               // 替换 jobMatch

  // 操作方法
  loadCareerGoals: () => Promise<void>;                    // 替换 loadJobs
  selectCareerGoal: (userId, careerGoalId, level) => Promise<any>; // 替换 selectJob
  clearCareerSelection: (userId) => Promise<void>;         // 替换 clearJobSelection
  analyzeCareerMatch: (userId, careerGoalId) => Promise<void>; // 替换 analyzeJobMatch
}

// useJob.ts 标记废弃
/**
 * @deprecated 此Hook已废弃，请使用 useCareer
 * 职业目标相关功能 -> 使用 hooks/useCareer.ts
 * 求职岗位相关功能 -> 使用 hooks/useJobs.ts (待创建)
 */
```

#### 4. 组件层更新
```typescript
// CareerManagementScreen.tsx 更新
const CareerManagementScreen = () => {
  const {
    careerPreference,        // 替换 jobPreference
    hasCareerSelected,       // 替换 hasJobSelected
    careerProgress,          // 替换 jobProgress
    careerMatch,             // 替换 jobMatch
    loadUserCareerData,
    selectCareerGoal,        // 替换 selectJob
    analyzeCareerMatch,      // 替换 analyzeJobMatch
  } = useCareer();

  // 使用正确的数据字段
  const targetCareer = careerProgress?.targetJob || careerPreference?.targetCareer;
  const level = careerGoal?.targetLevel || careerPreference?.currentLevel;

  // 条件渲染
  if (!hasCareerSelected || !careerPreference?.targetCareer) {
    return <CareerSelectionGuide />;
  }
};
```

#### 5. 服务层清理
```typescript
// JobService.ts 标记废弃
/**
 * @deprecated 此服务正在重构中，职业目标相关功能将迁移到CareerService
 *
 * 迁移计划：
 * - 职业目标相关方法 -> CareerService
 * - 求职岗位相关方法 -> JobsService (待创建)
 */

// 更新导入语句使用正确的类型
import {
  CareerGoal,              // 替换 JobProfile
  UserCareerPreference,    // 替换 UserJobPreference
  CareerProgress,          // 替换 JobProgress
  CareerMatch,             // 替换 JobMatchAnalysis
  CareerLevel              // 替换 JobLevel
} from '../types/career';
```

### 清理成果
1. ✅ **类型统一**：废弃`types/job.ts`重复类型，统一使用`types/career.ts`
2. ✅ **状态管理**：`careerSlice.ts`完全使用Career相关类型
3. ✅ **Hook清理**：`useCareer`统一Career字段，`useJob`标记废弃
4. ✅ **组件更新**：所有组件使用正确的Career数据字段
5. ✅ **服务清理**：`JobService`标记废弃，职业目标功能迁移
6. ✅ **数据修正**：`careerGoals.ts`使用正确的`CareerGoal`类型
7. ✅ **测试验证**：Playwright MCP前后端联调测试通过

### 迁移指南
```typescript
// 旧代码 -> 新代码
jobPreference?.targetJob          -> careerPreference?.targetCareer
hasJobSelected                    -> hasCareerSelected
jobProgress                       -> careerProgress
jobMatch                          -> careerMatch
allJobs                          -> allCareerGoals
popularJobs                      -> popularCareerGoals
loadJobs()                       -> loadCareerGoals()
selectJob(userId, jobId, level)  -> selectCareerGoal(userId, careerGoalId, level)
analyzeJobMatch(userId, jobId)   -> analyzeCareerMatch(userId, careerGoalId)

// 类型迁移
JobProfile                       -> CareerGoal
UserJobPreference               -> UserCareerPreference
JobProgress                     -> CareerProgress
JobMatchAnalysis               -> CareerMatch
JobLevel                       -> CareerLevel
```
- ✅ UI测试: 页面渲染和交互测试
- ✅ 错误处理测试: 异常情况处理验证

## 🔄 Redux-persist架构迁移 (2025-07-13)

### 迁移背景
为了实现企业级状态管理，ITBook项目完成了从直接AsyncStorage操作到Redux-persist统一状态管理的架构迁移。

### 架构设计
```typescript
// 双层状态管理架构
┌─────────────────────────────────────────┐
│           Redux + Redux-persist          │
│        (长期业务状态持久化)                │
│  ┌─────────────────────────────────────┐ │
│  │ • 用户认证状态                        │ │
│  │ • 职业目标偏好                        │ │
│  │ • 学习进度数据                        │ │
│  │ • 推荐系统配置                        │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         多层缓存系统 (cache.ts)           │
│        (短期临时数据管理)                 │
│  ┌─────────────────────────────────────┐ │
│  │ • API响应缓存                        │ │
│  │ • 临时UI状态                         │ │
│  │ • 会话级数据                         │ │
│  │ • 性能优化缓存                       │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 迁移成果

#### 1. 废弃服务清理
```typescript
// 已废弃的服务
DataInitService        -> Redux-persist自动管理
LearningProgressService -> Redux状态管理
storageService直接调用  -> Redux actions

// 保留的服务 (标记废弃)
JobService             -> 仅保留求职相关功能
useJob Hook           -> 标记@deprecated
```

#### 2. API调用优化
```typescript
// 优化前：重复调用链
careerSlice -> JobService.getAllJobs() -> careerGoalService.getAllCareerGoals()

// 优化后：直接调用
careerSlice -> careerGoalService.getAllCareerGoals()
```

#### 3. 类型安全改进
```typescript
// 明确的返回类型定义
export const loadCareerGoalsAsync = createAsyncThunk(
  'career/loadCareerGoals',
  async (_, { rejectWithValue }) => {
    const careerGoalService = (await import('../../services/CareerGoalService')).default;
    const [allCareerGoals, popularCareerGoals] = await Promise.all([
      careerGoalService.getAllCareerGoals(),
      careerGoalService.getPopularCareerGoals()
    ]);
    return {
      allCareerGoals: allCareerGoals as CareerGoal[],
      popularCareerGoals: popularCareerGoals as CareerGoal[]
    };
  }
);
```

### 测试验证
- ✅ 前后端联调测试: Playwright MCP验证
- ✅ 状态持久化测试: Redux-persist自动恢复
- ✅ API调用优化测试: 减少重复请求
- ✅ 错误处理测试: 废弃方法警告正常
- ✅ 用户体验测试: 页面加载和交互正常

---

## 🎯 项目详情页面重构优化技术实现 (2025-07-14)

### 重构目标与技术方案

#### 1. 数据源优化
```typescript
// 修复后台API问题
@Modifying  // 添加缺失的注解
@Query("UPDATE Project p SET p.viewCount = p.viewCount + 1 WHERE p.id = :id")
void incrementViewCount(@Param("id") Long id);

// 前端类型定义优化
export interface Project {
  id: number;
  title: string;
  description: string;
  difficulty: ProjectDifficulty;
  technologies?: string[] | null;  // 支持null值
  technologiesList?: string[];     // 前端处理后的数组
  // ... 其他字段
}
```

#### 2. 智能内容生成系统
```typescript
// 基于项目类型的学习价值生成
const generateLearningValue = (type: ProjectType, difficulty: ProjectDifficulty) => {
  const baseValues: Record<ProjectType, string[]> = {
    'WEB_DEVELOPMENT': [
      '掌握现代Web开发技术栈',
      '学习前端框架和响应式设计',
      '理解Web性能优化技巧',
      '掌握前后端交互原理'
    ],
    'BACKEND_DEVELOPMENT': [
      '掌握服务器端开发技能',
      '学习数据库设计和优化',
      '理解API设计和微服务架构',
      '掌握系统性能调优技巧'
    ],
    // ... 其他项目类型
  };
  return baseValues[type] || ['掌握项目开发技能'];
};
```

#### 3. 用户体验优化
```typescript
// 加载状态处理
if (loading) {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.background }}>
      <View flex center>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text text16 color={Colors.grey30} marginT-16>
          加载项目详情中...
        </Text>
      </View>
    </SafeAreaView>
  );
}

// 错误状态处理
if (error || !project) {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.background }}>
      <View flex center padding-32>
        <Ionicons name="alert-circle" size={64} color={Colors.red30} />
        <Text text18 color={Colors.text} marginT-16 center>
          加载失败
        </Text>
        <Button label="重试" onPress={loadProjectDetail} />
      </View>
    </SafeAreaView>
  );
}
```

#### 4. API集成与数据处理
```typescript
// 真实API调用替换模拟数据
const loadProjectDetail = async () => {
  try {
    setLoading(true);
    setError(null);

    const numericProjectId = parseInt(projectId, 10);
    if (isNaN(numericProjectId)) {
      throw new Error('无效的项目ID');
    }

    const projectData = await ProjectService.getProjectById(numericProjectId);
    setProject(projectData);
  } catch (error) {
    setError(error instanceof Error ? error.message : '加载项目详情失败');
  } finally {
    setLoading(false);
  }
};
```

#### 5. 交互功能实现
```typescript
// 点赞功能
const handleLike = async () => {
  if (!project) return;

  try {
    await ProjectService.likeProject(project.id);
    setIsLiked(!isLiked);
    setProject({
      ...project,
      likeCount: isLiked ? project.likeCount - 1 : project.likeCount + 1
    });
  } catch (error) {
    Alert.alert('错误', '点赞失败，请稍后重试');
  }
};
```

### 技术特性

#### 1. 企业级最佳实践
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 完善的加载、错误、空数据状态处理
- **用户体验**: 流畅的交互反馈和状态提示
- **代码质量**: 遵循React Native和企业级开发规范

#### 2. 学习平台业务导向
- **内容智能化**: 基于项目类型自动生成学习价值描述
- **学习导向设计**: 突出学习目标、技能提升和就业价值
- **资源整合**: 提供学习指南、社群入口等学习支持
- **社交功能**: 点赞、收藏、分享等互动功能

#### 3. UI设计一致性
- **设计令牌**: 使用统一的design-tokens
- **组件复用**: 与发现板块保持一致的UI风格
- **响应式设计**: 适配不同屏幕尺寸
- **交互反馈**: 统一的activeOpacity和动画效果

### 测试验证结果
- ✅ **API集成测试**: 所有项目详情API调用成功返回200状态码
- ✅ **数据展示测试**: 真实数据正确加载和显示
- ✅ **交互功能测试**: 点赞、收藏、导航功能正常工作
- ✅ **错误处理测试**: 加载状态、错误状态正确处理
- ✅ **用户体验测试**: 页面流畅，无JavaScript运行时错误
- ✅ **前后端联调测试**: Playwright MCP完整流程验证通过

---

**文档版本**：v1.9
**最后更新**：2025年7月14日
**维护团队**：ITBook技术团队
**本次更新**：新增项目详情页面重构优化技术文档
