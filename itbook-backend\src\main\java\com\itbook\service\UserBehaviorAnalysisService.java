package com.itbook.service;

import com.itbook.entity.UserProfile;
import com.itbook.entity.UserAtomicSkillMastery;
import com.itbook.entity.UserLearningPathProgress;
import com.itbook.entity.LearningPath;
import com.itbook.entity.UserStepProgress;
import com.itbook.entity.LearningPathStep;
import com.itbook.entity.Course;
import com.itbook.repository.UserProfileRepository;
import com.itbook.repository.UserAtomicSkillMasteryRepository;
import com.itbook.repository.UserLearningPathProgressRepository;
import com.itbook.repository.UserStepProgressRepository;
import com.itbook.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 用户行为分析服务
 * 负责分析用户学习行为，生成用户画像洞察
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserBehaviorAnalysisService {

    private final UserProfileRepository userProfileRepository;
    private final UserAtomicSkillMasteryRepository userAtomicSkillMasteryRepository;
    private final UserLearningPathProgressRepository userLearningPathProgressRepository;
    private final UserStepProgressRepository userStepProgressRepository;

    /**
     * 用户学习行为分析结果
     */
    public static class LearningBehaviorAnalysis {
        private Long userId;
        private LocalDateTime analysisTime;
        
        // 学习活跃度分析
        private Double learningActivityScore; // 0-100
        private Integer totalLearningDays;
        private Integer continuousLearningDays;
        private Double averageDailyLearningTime; // 小时
        
        // 学习偏好分析
        private String preferredLearningTime; // morning, afternoon, evening, night
        private List<String> preferredSkillCategories;
        private String learningPatternType; // consistent, burst, irregular
        
        // 技能掌握分析
        private Double skillMasteryGrowthRate; // 技能掌握增长率
        private List<String> strongSkillAreas; // 擅长技能领域
        private List<String> weakSkillAreas; // 薄弱技能领域
        private Integer totalMasteredSkills;
        
        // 学习效率分析
        private Double learningEfficiencyScore; // 0-100
        private Double averageCompletionRate; // 平均完成率
        private Integer abandonedPathsCount; // 放弃的学习路径数量
        
        // 社交学习分析
        private Integer communityInteractionScore; // 社区互动分数
        private Boolean isActiveCommunityMember;
        private List<String> interactionTypes; // like, comment, share, help
        
        // 目标导向分析
        private Boolean hasCareerGoal;
        private String careerGoalAlignment; // high, medium, low
        private Double goalProgressRate; // 目标进度率
        
        // 学习挑战分析
        private List<String> identifiedChallenges; // 识别的学习挑战
        private List<String> recommendedImprovements; // 推荐改进建议
        
        // 构造函数、Getters和Setters
        public LearningBehaviorAnalysis() {}
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public LocalDateTime getAnalysisTime() { return analysisTime; }
        public void setAnalysisTime(LocalDateTime analysisTime) { this.analysisTime = analysisTime; }
        
        public Double getLearningActivityScore() { return learningActivityScore; }
        public void setLearningActivityScore(Double learningActivityScore) { this.learningActivityScore = learningActivityScore; }
        
        public Integer getTotalLearningDays() { return totalLearningDays; }
        public void setTotalLearningDays(Integer totalLearningDays) { this.totalLearningDays = totalLearningDays; }
        
        public Integer getContinuousLearningDays() { return continuousLearningDays; }
        public void setContinuousLearningDays(Integer continuousLearningDays) { this.continuousLearningDays = continuousLearningDays; }
        
        public Double getAverageDailyLearningTime() { return averageDailyLearningTime; }
        public void setAverageDailyLearningTime(Double averageDailyLearningTime) { this.averageDailyLearningTime = averageDailyLearningTime; }
        
        public String getPreferredLearningTime() { return preferredLearningTime; }
        public void setPreferredLearningTime(String preferredLearningTime) { this.preferredLearningTime = preferredLearningTime; }
        
        public List<String> getPreferredSkillCategories() { return preferredSkillCategories; }
        public void setPreferredSkillCategories(List<String> preferredSkillCategories) { this.preferredSkillCategories = preferredSkillCategories; }
        
        public String getLearningPatternType() { return learningPatternType; }
        public void setLearningPatternType(String learningPatternType) { this.learningPatternType = learningPatternType; }
        
        public Double getSkillMasteryGrowthRate() { return skillMasteryGrowthRate; }
        public void setSkillMasteryGrowthRate(Double skillMasteryGrowthRate) { this.skillMasteryGrowthRate = skillMasteryGrowthRate; }
        
        public List<String> getStrongSkillAreas() { return strongSkillAreas; }
        public void setStrongSkillAreas(List<String> strongSkillAreas) { this.strongSkillAreas = strongSkillAreas; }
        
        public List<String> getWeakSkillAreas() { return weakSkillAreas; }
        public void setWeakSkillAreas(List<String> weakSkillAreas) { this.weakSkillAreas = weakSkillAreas; }
        
        public Integer getTotalMasteredSkills() { return totalMasteredSkills; }
        public void setTotalMasteredSkills(Integer totalMasteredSkills) { this.totalMasteredSkills = totalMasteredSkills; }
        
        public Double getLearningEfficiencyScore() { return learningEfficiencyScore; }
        public void setLearningEfficiencyScore(Double learningEfficiencyScore) { this.learningEfficiencyScore = learningEfficiencyScore; }
        
        public Double getAverageCompletionRate() { return averageCompletionRate; }
        public void setAverageCompletionRate(Double averageCompletionRate) { this.averageCompletionRate = averageCompletionRate; }
        
        public Integer getAbandonedPathsCount() { return abandonedPathsCount; }
        public void setAbandonedPathsCount(Integer abandonedPathsCount) { this.abandonedPathsCount = abandonedPathsCount; }
        
        public Integer getCommunityInteractionScore() { return communityInteractionScore; }
        public void setCommunityInteractionScore(Integer communityInteractionScore) { this.communityInteractionScore = communityInteractionScore; }
        
        public Boolean getIsActiveCommunityMember() { return isActiveCommunityMember; }
        public void setIsActiveCommunityMember(Boolean isActiveCommunityMember) { this.isActiveCommunityMember = isActiveCommunityMember; }
        
        public List<String> getInteractionTypes() { return interactionTypes; }
        public void setInteractionTypes(List<String> interactionTypes) { this.interactionTypes = interactionTypes; }
        
        public Boolean getHasCareerGoal() { return hasCareerGoal; }
        public void setHasCareerGoal(Boolean hasCareerGoal) { this.hasCareerGoal = hasCareerGoal; }
        
        public String getCareerGoalAlignment() { return careerGoalAlignment; }
        public void setCareerGoalAlignment(String careerGoalAlignment) { this.careerGoalAlignment = careerGoalAlignment; }
        
        public Double getGoalProgressRate() { return goalProgressRate; }
        public void setGoalProgressRate(Double goalProgressRate) { this.goalProgressRate = goalProgressRate; }
        
        public List<String> getIdentifiedChallenges() { return identifiedChallenges; }
        public void setIdentifiedChallenges(List<String> identifiedChallenges) { this.identifiedChallenges = identifiedChallenges; }
        
        public List<String> getRecommendedImprovements() { return recommendedImprovements; }
        public void setRecommendedImprovements(List<String> recommendedImprovements) { this.recommendedImprovements = recommendedImprovements; }
    }

    /**
     * 分析用户学习行为
     * 
     * @param userId 用户ID
     * @return 学习行为分析结果
     */
    @Transactional(readOnly = true)
    public LearningBehaviorAnalysis analyzeLearningBehavior(Long userId) {
        log.info("开始分析用户学习行为: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = new LearningBehaviorAnalysis();
            analysis.setUserId(userId);
            analysis.setAnalysisTime(LocalDateTime.now());
            
            // 获取用户基础数据
            Optional<UserProfile> userProfileOpt = userProfileRepository.findByUserId(userId);
            List<UserAtomicSkillMastery> skillMasteries = userAtomicSkillMasteryRepository.findByUserId(userId);
            List<UserLearningPathProgress> pathProgresses = userLearningPathProgressRepository.findByUserId(userId);
            
            // 分析学习活跃度
            analyzeLearningActivity(analysis, pathProgresses);
            
            // 分析学习偏好
            analyzeLearningPreferences(analysis, skillMasteries, pathProgresses);
            
            // 分析技能掌握情况
            analyzeSkillMastery(analysis, skillMasteries);
            
            // 分析学习效率
            analyzeLearningEfficiency(analysis, pathProgresses);
            
            // 分析社交学习行为
            analyzeCommunityInteraction(analysis, userId);
            
            // 分析目标导向性
            analyzeGoalOrientation(analysis, userProfileOpt.orElse(null), skillMasteries);
            
            // 识别学习挑战和改进建议
            identifyLearningChallenges(analysis);
            
            log.info("用户学习行为分析完成: userId={}, activityScore={}", 
                    userId, analysis.getLearningActivityScore());
            
            return analysis;
            
        } catch (Exception e) {
            log.error("分析用户学习行为失败: userId={}", userId, e);
            throw BusinessException.internalError("分析用户学习行为失败", e);
        }
    }

    /**
     * 分析学习活跃度
     */
    private void analyzeLearningActivity(LearningBehaviorAnalysis analysis, 
                                       List<UserLearningPathProgress> pathProgresses) {
        if (pathProgresses.isEmpty()) {
            analysis.setLearningActivityScore(0.0);
            analysis.setTotalLearningDays(0);
            analysis.setContinuousLearningDays(0);
            analysis.setAverageDailyLearningTime(0.0);
            return;
        }
        
        // 计算总学习天数
        Set<String> learningDates = pathProgresses.stream()
                .filter(p -> p.getLastStudiedAt() != null)
                .map(p -> p.getLastStudiedAt().toLocalDate().toString())
                .collect(Collectors.toSet());
        
        analysis.setTotalLearningDays(learningDates.size());
        
        // 计算连续学习天数（简化实现）
        analysis.setContinuousLearningDays(calculateContinuousLearningDays(pathProgresses));
        
        // 计算平均每日学习时间（基于进度估算）
        double totalEstimatedHours = pathProgresses.stream()
                .mapToDouble(p -> {
                    try {
                        double completionRate = p.getCompletionPercentage().doubleValue() / 100.0;
                        // 安全地获取学习路径的预估时间，避免懒加载异常
                        double estimatedHours = 10.0; // 默认值
                        if (p.getLearningPath() != null) {
                            try {
                                estimatedHours = p.getLearningPath().getEstimatedHours();
                            } catch (Exception e) {
                                // 忽略懒加载异常，使用默认值
                                estimatedHours = 10.0;
                            }
                        }
                        return completionRate * estimatedHours;
                    } catch (Exception e) {
                        return 0.0; // 出现异常时返回0
                    }
                })
                .sum();
        
        double averageDailyTime = learningDates.size() > 0 ? 
                totalEstimatedHours / learningDates.size() : 0.0;
        analysis.setAverageDailyLearningTime(
                BigDecimal.valueOf(averageDailyTime).setScale(2, RoundingMode.HALF_UP).doubleValue());
        
        // 计算学习活跃度分数 (0-100)
        double activityScore = Math.min(100.0, 
                (learningDates.size() * 2.0) + 
                (analysis.getContinuousLearningDays() * 3.0) + 
                (averageDailyTime * 10.0));
        
        analysis.setLearningActivityScore(
                BigDecimal.valueOf(activityScore).setScale(1, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 计算连续学习天数
     */
    private Integer calculateContinuousLearningDays(List<UserLearningPathProgress> pathProgresses) {
        // 简化实现：基于最近的学习记录
        LocalDateTime now = LocalDateTime.now();
        long daysSinceLastStudy = pathProgresses.stream()
                .filter(p -> p.getLastStudiedAt() != null)
                .mapToLong(p -> ChronoUnit.DAYS.between(p.getLastStudiedAt(), now))
                .min()
                .orElse(999L);
        
        return daysSinceLastStudy <= 1 ? 
                Math.min(30, pathProgresses.size()) : 0; // 最多30天连续学习
    }

    /**
     * 分析学习偏好
     */
    private void analyzeLearningPreferences(LearningBehaviorAnalysis analysis,
                                          List<UserAtomicSkillMastery> skillMasteries,
                                          List<UserLearningPathProgress> pathProgresses) {
        // 分析偏好的技能分类
        Map<String, Long> categoryCount = skillMasteries.stream()
                .filter(s -> s.getAtomicSkill() != null && s.getAtomicSkill().getCategory() != null)
                .collect(Collectors.groupingBy(
                        s -> s.getAtomicSkill().getCategory(),
                        Collectors.counting()));
        
        List<String> preferredCategories = categoryCount.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        analysis.setPreferredSkillCategories(preferredCategories);
        
        // 分析学习时间偏好（简化实现）
        analysis.setPreferredLearningTime("evening"); // 默认晚上
        
        // 分析学习模式类型
        String patternType = pathProgresses.size() > 5 ? "consistent" : 
                           pathProgresses.size() > 2 ? "burst" : "irregular";
        analysis.setLearningPatternType(patternType);
    }

    /**
     * 分析技能掌握情况
     */
    private void analyzeSkillMastery(LearningBehaviorAnalysis analysis,
                                   List<UserAtomicSkillMastery> skillMasteries) {
        if (skillMasteries.isEmpty()) {
            analysis.setSkillMasteryGrowthRate(0.0);
            analysis.setStrongSkillAreas(new ArrayList<>());
            analysis.setWeakSkillAreas(new ArrayList<>());
            analysis.setTotalMasteredSkills(0);
            return;
        }
        
        // 计算掌握的技能总数
        long masteredCount = skillMasteries.stream()
                .filter(s -> s.getMasteryLevel() != null && 
                           s.getMasteryLevel().ordinal() >= 2) // INTERMEDIATE及以上
                .count();
        
        analysis.setTotalMasteredSkills((int) masteredCount);
        
        // 分析强项和弱项技能领域
        Map<String, Double> categoryMasteryAvg = skillMasteries.stream()
                .filter(s -> s.getAtomicSkill() != null && s.getAtomicSkill().getCategory() != null)
                .collect(Collectors.groupingBy(
                        s -> s.getAtomicSkill().getCategory(),
                        Collectors.averagingDouble(s -> s.getMasteryLevel() != null ? 
                                s.getMasteryLevel().ordinal() : 0)));
        
        List<String> strongAreas = categoryMasteryAvg.entrySet().stream()
                .filter(e -> e.getValue() >= 2.0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        List<String> weakAreas = categoryMasteryAvg.entrySet().stream()
                .filter(e -> e.getValue() < 1.5)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        analysis.setStrongSkillAreas(strongAreas);
        analysis.setWeakSkillAreas(weakAreas);
        
        // 计算技能掌握增长率（简化实现）
        double growthRate = Math.min(100.0, masteredCount * 5.0);
        analysis.setSkillMasteryGrowthRate(
                BigDecimal.valueOf(growthRate).setScale(1, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 分析学习效率
     */
    private void analyzeLearningEfficiency(LearningBehaviorAnalysis analysis,
                                         List<UserLearningPathProgress> pathProgresses) {
        if (pathProgresses.isEmpty()) {
            analysis.setLearningEfficiencyScore(0.0);
            analysis.setAverageCompletionRate(0.0);
            analysis.setAbandonedPathsCount(0);
            return;
        }
        
        // 计算平均完成率
        double avgCompletionRate = pathProgresses.stream()
                .mapToDouble(p -> p.getCompletionPercentage().doubleValue())
                .average()
                .orElse(0.0);
        
        analysis.setAverageCompletionRate(
                BigDecimal.valueOf(avgCompletionRate).setScale(1, RoundingMode.HALF_UP).doubleValue());
        
        // 计算放弃的路径数量（进度<10%且超过30天未学习）
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        long abandonedCount = pathProgresses.stream()
                .filter(p -> p.getCompletionPercentage().doubleValue() < 10.0 &&
                           p.getLastStudiedAt() != null &&
                           p.getLastStudiedAt().isBefore(thirtyDaysAgo))
                .count();
        
        analysis.setAbandonedPathsCount((int) abandonedCount);
        
        // 计算学习效率分数
        double efficiencyScore = Math.max(0.0, 
                avgCompletionRate - (abandonedCount * 10.0));
        
        analysis.setLearningEfficiencyScore(
                BigDecimal.valueOf(efficiencyScore).setScale(1, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 分析社区互动行为
     */
    private void analyzeCommunityInteraction(LearningBehaviorAnalysis analysis, Long userId) {
        // 简化实现：基于用户ID生成模拟数据
        int interactionScore = (int) (userId % 100);
        analysis.setCommunityInteractionScore(interactionScore);
        analysis.setIsActiveCommunityMember(interactionScore > 50);
        
        List<String> interactionTypes = new ArrayList<>();
        if (interactionScore > 20) interactionTypes.add("like");
        if (interactionScore > 40) interactionTypes.add("comment");
        if (interactionScore > 60) interactionTypes.add("share");
        if (interactionScore > 80) interactionTypes.add("help");
        
        analysis.setInteractionTypes(interactionTypes);
    }

    /**
     * 分析目标导向性
     */
    private void analyzeGoalOrientation(LearningBehaviorAnalysis analysis,
                                      UserProfile userProfile,
                                      List<UserAtomicSkillMastery> skillMasteries) {
        boolean hasCareerGoal = userProfile != null && 
                               userProfile.getCareerGoal() != null && 
                               !userProfile.getCareerGoal().trim().isEmpty();
        
        analysis.setHasCareerGoal(hasCareerGoal);
        
        if (hasCareerGoal) {
            // 简化的目标对齐度分析
            String alignment = skillMasteries.size() > 10 ? "high" :
                             skillMasteries.size() > 5 ? "medium" : "low";
            analysis.setCareerGoalAlignment(alignment);
            
            // 计算目标进度率
            double progressRate = Math.min(100.0, skillMasteries.size() * 3.0);
            analysis.setGoalProgressRate(
                    BigDecimal.valueOf(progressRate).setScale(1, RoundingMode.HALF_UP).doubleValue());
        } else {
            analysis.setCareerGoalAlignment("none");
            analysis.setGoalProgressRate(0.0);
        }
    }

    /**
     * 识别学习挑战和改进建议
     */
    private void identifyLearningChallenges(LearningBehaviorAnalysis analysis) {
        List<String> challenges = new ArrayList<>();
        List<String> improvements = new ArrayList<>();
        
        // 基于分析结果识别挑战
        if (analysis.getLearningActivityScore() < 30) {
            challenges.add("学习活跃度较低");
            improvements.add("建议制定每日学习计划，保持学习连续性");
        }
        
        if (analysis.getAverageCompletionRate() < 50) {
            challenges.add("学习完成率偏低");
            improvements.add("建议选择适合自己难度的学习路径，循序渐进");
        }
        
        if (analysis.getAbandonedPathsCount() > 3) {
            challenges.add("容易放弃学习路径");
            improvements.add("建议设定短期目标，增强学习动机");
        }
        
        if (!analysis.getHasCareerGoal()) {
            challenges.add("缺乏明确的职业目标");
            improvements.add("建议明确职业发展方向，制定学习目标");
        }
        
        if (analysis.getCommunityInteractionScore() < 30) {
            challenges.add("社区参与度较低");
            improvements.add("建议积极参与社区讨论，与其他学习者交流");
        }
        
        analysis.setIdentifiedChallenges(challenges);
        analysis.setRecommendedImprovements(improvements);
    }

    /**
     * 获取用户偏好技能分类分析
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserPreferredSkillCategories(Long userId) {
        try {
            log.info("🔍 分析用户偏好技能分类: {}", userId);

            // 获取用户学习路径进度
            List<UserLearningPathProgress> pathProgresses = userLearningPathProgressRepository.findByUserId(userId);

            Map<String, Integer> skillCategoryCount = new HashMap<>();
            Map<String, Double> skillCategoryTime = new HashMap<>();

            // 分析学习路径的技能标签
            for (UserLearningPathProgress progress : pathProgresses) {
                try {
                    LearningPath learningPath = progress.getLearningPath();
                    if (learningPath != null && learningPath.getSkillTagsJson() != null) {
                        // 解析JSON格式的技能标签
                        String skillTagsJson = learningPath.getSkillTagsJson();
                        if (skillTagsJson != null && !skillTagsJson.trim().isEmpty()) {
                            log.debug("处理学习路径技能标签: pathId={}, skillTags={}", learningPath.getId(), skillTagsJson);

                            // 简单的JSON解析（假设是字符串数组格式）
                            skillTagsJson = skillTagsJson.replaceAll("[\\[\\]\"]", "");
                            String[] skills = skillTagsJson.split(",");

                            for (String skill : skills) {
                                skill = skill.trim();
                                if (!skill.isEmpty()) {
                                    // 将技能归类到大类别
                                    String category = categorizeSkill(skill);
                                    skillCategoryCount.put(category, skillCategoryCount.getOrDefault(category, 0) + 1);

                                    // 计算在该类别上的学习时间
                                    double studiedHours = (progress.getStudiedMinutes() != null ? progress.getStudiedMinutes() : 0) / 60.0;
                                    skillCategoryTime.put(category, skillCategoryTime.getOrDefault(category, 0.0) + studiedHours);

                                    log.debug("技能分类: {} -> {}, 学习时间: {}小时", skill, category, studiedHours);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理学习路径技能标签时出错: pathId={}", progress.getLearningPath().getId(), e);
                }
            }

            // 获取用户课程学习情况
            List<UserStepProgress> stepProgresses = userStepProgressRepository.findByUserId(userId);
            for (UserStepProgress stepProgress : stepProgresses) {
                try {
                    LearningPathStep step = stepProgress.getStep();
                    if (step != null && step.getCourse() != null) {
                        Course course = step.getCourse();
                        String category = course.getCategory();
                        if (category != null && !category.trim().isEmpty()) {
                            String normalizedCategory = categorizeCourseCategory(category);
                            skillCategoryCount.put(normalizedCategory, skillCategoryCount.getOrDefault(normalizedCategory, 0) + 1);

                            double studiedHours = (stepProgress.getStudiedMinutes() != null ? stepProgress.getStudiedMinutes() : 0) / 60.0;
                            skillCategoryTime.put(normalizedCategory, skillCategoryTime.getOrDefault(normalizedCategory, 0.0) + studiedHours);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理课程分类时出错: stepId={}", stepProgress.getId(), e);
                }
            }

            // 按学习时间排序，获取前5个偏好分类
            List<Map<String, Object>> preferredCategories = skillCategoryTime.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    Map<String, Object> categoryInfo = new HashMap<>();
                    categoryInfo.put("category", entry.getKey());
                    categoryInfo.put("studiedHours", Math.round(entry.getValue() * 10.0) / 10.0);
                    categoryInfo.put("courseCount", skillCategoryCount.getOrDefault(entry.getKey(), 0));
                    categoryInfo.put("percentage", Math.round((entry.getValue() / skillCategoryTime.values().stream().mapToDouble(Double::doubleValue).sum()) * 100.0 * 10.0) / 10.0);
                    return categoryInfo;
                })
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("preferredCategories", preferredCategories);
            result.put("totalCategories", skillCategoryCount.size());
            result.put("totalStudiedHours", Math.round(skillCategoryTime.values().stream().mapToDouble(Double::doubleValue).sum() * 10.0) / 10.0);
            result.put("analysisTime", LocalDateTime.now());

            log.info("✅ 用户偏好技能分类分析完成: userId={}, categories={}", userId, preferredCategories.size());
            return result;

        } catch (Exception e) {
            log.error("获取用户偏好技能分类失败: userId={}", userId, e);
            throw new RuntimeException("获取用户偏好技能分类失败", e);
        }
    }

    /**
     * 将技能归类到大类别
     */
    private String categorizeSkill(String skill) {
        skill = skill.toLowerCase();

        if (skill.contains("java") || skill.contains("spring") || skill.contains("backend") || skill.contains("后端")) {
            return "后端开发";
        } else if (skill.contains("react") || skill.contains("frontend") || skill.contains("javascript") || skill.contains("typescript") || skill.contains("前端")) {
            return "前端开发";
        } else if (skill.contains("python") || skill.contains("data") || skill.contains("pandas") || skill.contains("numpy") || skill.contains("数据")) {
            return "数据分析";
        } else if (skill.contains("docker") || skill.contains("kubernetes") || skill.contains("devops") || skill.contains("运维")) {
            return "DevOps运维";
        } else if (skill.contains("algorithm") || skill.contains("structure") || skill.contains("算法") || skill.contains("数据结构")) {
            return "算法与数据结构";
        } else if (skill.contains("mysql") || skill.contains("database") || skill.contains("数据库")) {
            return "数据库";
        } else if (skill.contains("api") || skill.contains("restful")) {
            return "API开发";
        } else {
            return "其他技能";
        }
    }

    /**
     * 将课程分类归类到大类别
     */
    private String categorizeCourseCategory(String category) {
        category = category.toLowerCase();

        if (category.contains("programming") || category.contains("编程")) {
            return "编程基础";
        } else if (category.contains("framework") || category.contains("框架")) {
            return "框架技术";
        } else if (category.contains("frontend") || category.contains("前端")) {
            return "前端开发";
        } else if (category.contains("backend") || category.contains("后端")) {
            return "后端开发";
        } else if (category.contains("data") || category.contains("数据")) {
            return "数据分析";
        } else if (category.contains("computer") || category.contains("science") || category.contains("计算机")) {
            return "计算机科学";
        } else {
            return "其他分类";
        }
    }
}
