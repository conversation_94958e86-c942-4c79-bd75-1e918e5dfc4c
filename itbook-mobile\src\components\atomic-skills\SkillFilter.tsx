import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { DifficultyLevel, SkillType, SkillSearchFilter } from '../../types/atomicSkill';

interface SkillFilterProps {
  categories?: string[];
  selectedFilter?: SkillSearchFilter;
  onFilterChange: (filter: SkillSearchFilter) => void;
  style?: any;
}

/**
 * 原子技能快速筛选组件
 * 提供分类、难度、类型等快速筛选选项
 */
export const SkillFilter: React.FC<SkillFilterProps> = ({
  categories = [],
  selectedFilter = {},
  onFilterChange,
  style,
}) => {
  const colors = useThemeColors();
  const [activeTab, setActiveTab] = useState<'category' | 'difficulty' | 'type'>('category');

  // 处理筛选条件变更
  const handleFilterChange = useCallback((key: keyof SkillSearchFilter, value?: any) => {
    const newFilter = { ...selectedFilter };
    
    if (value === undefined || value === newFilter[key]) {
      // 如果值为undefined或与当前值相同，则清除该筛选条件
      delete newFilter[key];
    } else {
      newFilter[key] = value;
    }
    
    onFilterChange(newFilter);
  }, [selectedFilter, onFilterChange]);

  // 渲染筛选标签页
  const renderFilterTabs = () => {
    const tabs = [
      { key: 'category', label: '分类', icon: 'library-outline' },
      { key: 'difficulty', label: '难度', icon: 'bar-chart-outline' },
      { key: 'type', label: '类型', icon: 'shapes-outline' },
    ] as const;

    return (
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              { backgroundColor: colors.surface },
              activeTab === tab.key && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => setActiveTab(tab.key)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={tab.icon as any}
              size={16}
              color={activeTab === tab.key ? colors.primary : colors.textSecondary}
            />
            <Text style={[
              styles.tabText,
              { color: activeTab === tab.key ? colors.primary : colors.textSecondary }
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // 渲染分类筛选
  const renderCategoryFilter = () => {
    if (categories.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            暂无分类数据
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterContent}
      >
        <TouchableOpacity
          style={[
            styles.filterChip,
            { backgroundColor: colors.surface },
            !selectedFilter.category && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => handleFilterChange('category')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.filterChipText,
            { color: !selectedFilter.category ? colors.primary : colors.textSecondary }
          ]}>
            全部
          </Text>
        </TouchableOpacity>
        
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.filterChip,
              { backgroundColor: colors.surface },
              selectedFilter.category === category && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => handleFilterChange('category', category)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterChipText,
              { color: selectedFilter.category === category ? colors.primary : colors.textSecondary }
            ]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  // 渲染难度筛选
  const renderDifficultyFilter = () => {
    const difficulties = Object.values(DifficultyLevel);
    const difficultyLabels = {
      [DifficultyLevel.BEGINNER]: '初级',
      [DifficultyLevel.INTERMEDIATE]: '中级',
      [DifficultyLevel.ADVANCED]: '高级',
      [DifficultyLevel.EXPERT]: '专家',
    };

    const difficultyColors = {
      [DifficultyLevel.BEGINNER]: colors.success,
      [DifficultyLevel.INTERMEDIATE]: colors.warning,
      [DifficultyLevel.ADVANCED]: colors.error,
      [DifficultyLevel.EXPERT]: colors.purple,
    };

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterContent}
      >
        <TouchableOpacity
          style={[
            styles.filterChip,
            { backgroundColor: colors.surface },
            !selectedFilter.difficultyLevel && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => handleFilterChange('difficultyLevel')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.filterChipText,
            { color: !selectedFilter.difficultyLevel ? colors.primary : colors.textSecondary }
          ]}>
            全部
          </Text>
        </TouchableOpacity>
        
        {difficulties.map((difficulty) => (
          <TouchableOpacity
            key={difficulty}
            style={[
              styles.filterChip,
              { backgroundColor: colors.surface },
              selectedFilter.difficultyLevel === difficulty && { 
                backgroundColor: difficultyColors[difficulty] + '20' 
              }
            ]}
            onPress={() => handleFilterChange('difficultyLevel', difficulty)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.difficultyIndicator,
              { backgroundColor: difficultyColors[difficulty] }
            ]} />
            <Text style={[
              styles.filterChipText,
              { 
                color: selectedFilter.difficultyLevel === difficulty 
                  ? difficultyColors[difficulty] 
                  : colors.textSecondary 
              }
            ]}>
              {difficultyLabels[difficulty]}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  // 渲染类型筛选
  const renderTypeFilter = () => {
    const types = Object.values(SkillType);
    const typeLabels = {
      [SkillType.CORE]: '核心技能',
      [SkillType.SUPPORTING]: '支撑技能',
      [SkillType.BONUS]: '加分技能',
    };

    const typeIcons = {
      [SkillType.CORE]: 'star',
      [SkillType.SUPPORTING]: 'build',
      [SkillType.BONUS]: 'add-circle',
    };

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterContent}
      >
        <TouchableOpacity
          style={[
            styles.filterChip,
            { backgroundColor: colors.surface },
            !selectedFilter.skillType && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => handleFilterChange('skillType')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.filterChipText,
            { color: !selectedFilter.skillType ? colors.primary : colors.textSecondary }
          ]}>
            全部
          </Text>
        </TouchableOpacity>
        
        {types.map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.filterChip,
              { backgroundColor: colors.surface },
              selectedFilter.skillType === type && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => handleFilterChange('skillType', type)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={typeIcons[type] as any}
              size={14}
              color={selectedFilter.skillType === type ? colors.primary : colors.textSecondary}
              style={styles.typeIcon}
            />
            <Text style={[
              styles.filterChipText,
              { color: selectedFilter.skillType === type ? colors.primary : colors.textSecondary }
            ]}>
              {typeLabels[type]}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  // 渲染当前筛选内容
  const renderFilterContent = () => {
    switch (activeTab) {
      case 'category':
        return renderCategoryFilter();
      case 'difficulty':
        return renderDifficultyFilter();
      case 'type':
        return renderTypeFilter();
      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, style]}>
      {renderFilterTabs()}
      <View style={styles.contentContainer}>
        {renderFilterContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: tokens.spacing('md'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginRight: tokens.spacing('sm'),
  },
  tabText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('xs'),
  },
  contentContainer: {
    minHeight: 60,
  },
  filterContent: {
    paddingHorizontal: tokens.spacing('md'),
    paddingRight: tokens.spacing('lg'),
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('lg'),
    marginRight: tokens.spacing('sm'),
    minHeight: 36,
  },
  filterChipText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
  difficultyIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: tokens.spacing('xs'),
  },
  typeIcon: {
    marginRight: tokens.spacing('xs'),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: tokens.spacing('lg'),
  },
  emptyText: {
    fontSize: tokens.fontSize('body'),
    textAlign: 'center',
  },
});
