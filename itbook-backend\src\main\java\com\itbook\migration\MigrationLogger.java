package com.itbook.migration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 迁移日志记录器
 * 负责记录迁移过程中的详细日志
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Component
public class MigrationLogger {

    private static final Logger logger = LoggerFactory.getLogger(MigrationLogger.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 记录阶段开始
     */
    public void logPhaseStart(String phase, String description) {
        try {
            String sql = "INSERT INTO migration_log (phase, status, start_time, description, created_at) " +
                        "VALUES (?, 'STARTED', ?, ?, ?)";
            jdbcTemplate.update(sql, phase, LocalDateTime.now(), description, LocalDateTime.now());
            logger.info("阶段开始 - {}: {}", phase, description);
        } catch (Exception e) {
            logger.error("记录阶段开始日志失败", e);
        }
    }

    /**
     * 记录阶段完成
     */
    public void logPhaseComplete(String phase, String description, String details) {
        try {
            String sql = "INSERT INTO migration_log (phase, status, end_time, description, details, created_at) " +
                        "VALUES (?, 'COMPLETED', ?, ?, ?, ?)";
            jdbcTemplate.update(sql, phase, LocalDateTime.now(), description, details, LocalDateTime.now());
            logger.info("阶段完成 - {}: {}", phase, description);
        } catch (Exception e) {
            logger.error("记录阶段完成日志失败", e);
        }
    }

    /**
     * 记录错误
     */
    public void logError(String phase, String description, String errorMessage) {
        try {
            String sql = "INSERT INTO migration_log (phase, status, end_time, description, error_message, created_at) " +
                        "VALUES (?, 'FAILED', ?, ?, ?, ?)";
            jdbcTemplate.update(sql, phase, LocalDateTime.now(), description, errorMessage, LocalDateTime.now());
            logger.error("阶段失败 - {}: {} - {}", phase, description, errorMessage);
        } catch (Exception e) {
            logger.error("记录错误日志失败", e);
        }
    }

    /**
     * 初始化日志表（如果不存在）
     */
    public void initializeLogTable() {
        try {
            String createTableSql = "CREATE TABLE IF NOT EXISTS migration_log (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "phase VARCHAR(50) NOT NULL, " +
                    "status VARCHAR(20) NOT NULL, " +
                    "start_time DATETIME, " +
                    "end_time DATETIME, " +
                    "description TEXT, " +
                    "details TEXT, " +
                    "error_message TEXT, " +
                    "created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP" +
                    ")";
            jdbcTemplate.execute(createTableSql);
            logger.info("迁移日志表初始化完成");
        } catch (Exception e) {
            logger.error("初始化日志表失败", e);
        }
    }
}

/**
 * 备份管理器
 * 负责管理数据备份和恢复
 */
@Component
class BackupManager {

    private static final Logger logger = LoggerFactory.getLogger(BackupManager.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 创建备份
     */
    public boolean createBackup(String tableName) {
        try {
            String backupTableName = tableName + "_backup";
            jdbcTemplate.execute("DROP TABLE IF EXISTS " + backupTableName);
            jdbcTemplate.execute("CREATE TABLE " + backupTableName + " AS SELECT * FROM " + tableName);
            logger.info("表 {} 备份完成", tableName);
            return true;
        } catch (Exception e) {
            logger.error("备份表 {} 失败", tableName, e);
            return false;
        }
    }

    /**
     * 恢复备份
     */
    public boolean restoreBackup(String tableName) {
        try {
            String backupTableName = tableName + "_backup";
            jdbcTemplate.execute("DELETE FROM " + tableName);
            jdbcTemplate.execute("INSERT INTO " + tableName + " SELECT * FROM " + backupTableName);
            logger.info("表 {} 恢复完成", tableName);
            return true;
        } catch (Exception e) {
            logger.error("恢复表 {} 失败", tableName, e);
            return false;
        }
    }

    /**
     * 检查备份是否存在
     */
    public boolean backupExists(String tableName) {
        try {
            String backupTableName = tableName + "_backup";
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?", 
                Integer.class, backupTableName);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.error("检查备份表 {} 失败", tableName, e);
            return false;
        }
    }
}
