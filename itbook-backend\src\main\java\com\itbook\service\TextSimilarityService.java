package com.itbook.service;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文本相似度计算服务
 * 提供多种文本相似度计算算法，用于技能映射
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
public class TextSimilarityService {

    /**
     * 计算两个文本的综合相似度分数
     * 结合多种算法的结果，返回0-1之间的分数
     */
    public double calculateSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null || text1.trim().isEmpty() || text2.trim().isEmpty()) {
            return 0.0;
        }
        
        // 预处理文本
        String normalizedText1 = normalizeText(text1);
        String normalizedText2 = normalizeText(text2);
        
        // 计算多种相似度
        double jaccardSimilarity = calculateJaccardSimilarity(normalizedText1, normalizedText2);
        double cosineSimilarity = calculateCosineSimilarity(normalizedText1, normalizedText2);
        double editDistanceSimilarity = calculateEditDistanceSimilarity(normalizedText1, normalizedText2);
        double keywordSimilarity = calculateKeywordSimilarity(normalizedText1, normalizedText2);
        
        // 加权平均（可根据实际效果调整权重）
        double weightedSimilarity = 
            jaccardSimilarity * 0.25 +
            cosineSimilarity * 0.35 +
            editDistanceSimilarity * 0.20 +
            keywordSimilarity * 0.20;
        
        return Math.min(1.0, Math.max(0.0, weightedSimilarity));
    }

    /**
     * 文本预处理和标准化
     */
    private String normalizeText(String text) {
        return text.toLowerCase()
                   .replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]", " ") // 保留中文、英文、数字和空格
                   .replaceAll("\\s+", " ") // 合并多个空格
                   .trim();
    }

    /**
     * 计算Jaccard相似度
     * 基于两个文本的词汇集合的交集和并集
     */
    public double calculateJaccardSimilarity(String text1, String text2) {
        Set<String> words1 = getWordSet(text1);
        Set<String> words2 = getWordSet(text2);
        
        if (words1.isEmpty() && words2.isEmpty()) {
            return 1.0;
        }
        
        Set<String> intersection = new HashSet<>(words1);
        intersection.retainAll(words2);
        
        Set<String> union = new HashSet<>(words1);
        union.addAll(words2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 计算余弦相似度
     * 基于词频向量的余弦值
     */
    public double calculateCosineSimilarity(String text1, String text2) {
        Map<String, Integer> vector1 = getWordFrequency(text1);
        Map<String, Integer> vector2 = getWordFrequency(text2);
        
        if (vector1.isEmpty() || vector2.isEmpty()) {
            return 0.0;
        }
        
        // 获取所有词汇
        Set<String> allWords = new HashSet<>();
        allWords.addAll(vector1.keySet());
        allWords.addAll(vector2.keySet());
        
        // 计算点积和模长
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (String word : allWords) {
            int freq1 = vector1.getOrDefault(word, 0);
            int freq2 = vector2.getOrDefault(word, 0);
            
            dotProduct += freq1 * freq2;
            norm1 += freq1 * freq1;
            norm2 += freq2 * freq2;
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 计算编辑距离相似度
     * 基于Levenshtein距离的标准化相似度
     */
    public double calculateEditDistanceSimilarity(String text1, String text2) {
        int editDistance = calculateLevenshteinDistance(text1, text2);
        int maxLength = Math.max(text1.length(), text2.length());
        
        if (maxLength == 0) {
            return 1.0;
        }
        
        return 1.0 - (double) editDistance / maxLength;
    }

    /**
     * 计算关键词相似度
     * 基于技能领域的关键词匹配
     */
    public double calculateKeywordSimilarity(String text1, String text2) {
        Set<String> keywords1 = extractKeywords(text1);
        Set<String> keywords2 = extractKeywords(text2);
        
        if (keywords1.isEmpty() && keywords2.isEmpty()) {
            return 1.0;
        }
        
        Set<String> intersection = new HashSet<>(keywords1);
        intersection.retainAll(keywords2);
        
        Set<String> union = new HashSet<>(keywords1);
        union.addAll(keywords2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 提取文本中的关键词
     */
    private Set<String> extractKeywords(String text) {
        // 技能领域的关键词词典
        Set<String> techKeywords = Set.of(
            "java", "spring", "boot", "mysql", "数据库", "编程", "开发", "框架",
            "react", "javascript", "前端", "后端", "web", "api", "微服务",
            "架构", "设计", "算法", "数据结构", "网络", "安全", "测试",
            "devops", "docker", "kubernetes", "云计算", "大数据", "机器学习"
        );
        
        Set<String> words = getWordSet(text);
        return words.stream()
                   .filter(word -> techKeywords.contains(word) || word.length() >= 2)
                   .collect(Collectors.toSet());
    }

    /**
     * 获取文本的词汇集合
     */
    private Set<String> getWordSet(String text) {
        return Arrays.stream(text.split("\\s+"))
                     .filter(word -> !word.isEmpty())
                     .collect(Collectors.toSet());
    }

    /**
     * 获取文本的词频统计
     */
    private Map<String, Integer> getWordFrequency(String text) {
        Map<String, Integer> frequency = new HashMap<>();
        String[] words = text.split("\\s+");
        
        for (String word : words) {
            if (!word.isEmpty()) {
                frequency.put(word, frequency.getOrDefault(word, 0) + 1);
            }
        }
        
        return frequency;
    }

    /**
     * 计算Levenshtein编辑距离
     */
    private int calculateLevenshteinDistance(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        
        int[][] dp = new int[len1 + 1][len2 + 1];
        
        // 初始化边界条件
        for (int i = 0; i <= len1; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= len2; j++) {
            dp[0][j] = j;
        }
        
        // 动态规划计算编辑距离
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(
                        Math.min(dp[i - 1][j], dp[i][j - 1]),
                        dp[i - 1][j - 1]
                    ) + 1;
                }
            }
        }
        
        return dp[len1][len2];
    }

    /**
     * 批量计算文本与候选文本列表的相似度
     * 返回按相似度降序排列的结果
     */
    public List<SimilarityResult> calculateSimilarities(String targetText, List<String> candidateTexts) {
        List<SimilarityResult> results = new ArrayList<>();
        
        for (int i = 0; i < candidateTexts.size(); i++) {
            String candidateText = candidateTexts.get(i);
            double similarity = calculateSimilarity(targetText, candidateText);
            results.add(new SimilarityResult(i, candidateText, similarity));
        }
        
        // 按相似度降序排列
        results.sort((r1, r2) -> Double.compare(r2.getSimilarity(), r1.getSimilarity()));
        
        return results;
    }

    /**
     * 相似度计算结果
     */
    public static class SimilarityResult {
        private final int index;
        private final String text;
        private final double similarity;
        
        public SimilarityResult(int index, String text, double similarity) {
            this.index = index;
            this.text = text;
            this.similarity = similarity;
        }
        
        public int getIndex() { return index; }
        public String getText() { return text; }
        public double getSimilarity() { return similarity; }
        
        @Override
        public String toString() {
            return String.format("SimilarityResult{index=%d, text='%s', similarity=%.4f}", 
                               index, text, similarity);
        }
    }
}
