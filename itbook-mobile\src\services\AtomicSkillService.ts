import { ApiService } from './ApiService';
import {
  AtomicSkill,
  AtomicSkillApiResponse,
  AtomicSkillUtils,
  SkillSearchFilter,
  SkillRecommendationRequest,
  SkillRecommendation,
  UserAtomicSkillMastery,
  SkillGraph,
  SkillStatistics,
  MasteryLevel,
  DifficultyLevel,
  SkillType,
  SkillStatus
} from '../types/atomicSkill';

/**
 * 原子技能服务类
 * 提供原子技能相关的API调用功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
export class AtomicSkillService {
  private static readonly BASE_URL = '/atomic-skills';

  /**
   * 获取原子技能详情
   */
  static async getAtomicSkill(skillId: number): Promise<AtomicSkill> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}`);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 创建原子技能
   */
  static async createAtomicSkill(skill: Partial<AtomicSkill>): Promise<AtomicSkill> {
    const apiRequest = AtomicSkillUtils.toApiRequest(skill);
    const response = await ApiService.post(this.BASE_URL, apiRequest);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 更新原子技能
   */
  static async updateAtomicSkill(skillId: number, skill: Partial<AtomicSkill>): Promise<AtomicSkill> {
    const apiRequest = AtomicSkillUtils.toApiRequest(skill);
    const response = await ApiService.put(`${this.BASE_URL}/${skillId}`, apiRequest);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 删除原子技能（软删除）
   */
  static async deleteAtomicSkill(skillId: number): Promise<string> {
    const response = await ApiService.delete(`${this.BASE_URL}/${skillId}`);
    return response.data;
  }

  /**
   * 分页查询原子技能
   */
  static async getAtomicSkills(params: {
    page?: number;
    size?: number;
    category?: string;
    subcategory?: string;
    difficultyLevel?: DifficultyLevel;
    status?: SkillStatus;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(this.BASE_URL, { params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 搜索原子技能
   */
  static async searchAtomicSkills(keyword: string, params?: {
    page?: number;
    size?: number;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/search`, {
      params: { keyword, ...params }
    });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 根据分类查询原子技能
   */
  static async getAtomicSkillsByCategory(category: string, params?: {
    page?: number;
    size?: number;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/category/${category}`, { params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 根据难度级别查询原子技能
   */
  static async getAtomicSkillsByDifficulty(difficultyLevel: DifficultyLevel, params?: {
    page?: number;
    size?: number;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/difficulty/${difficultyLevel}`, { params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 发布技能
   */
  static async publishAtomicSkill(skillId: number): Promise<AtomicSkill> {
    const response = await ApiService.post(`${this.BASE_URL}/${skillId}/publish`);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 高级搜索原子技能
   */
  static async advancedSearchSkills(filter: SkillSearchFilter): Promise<AtomicSkill[]> {
    const response = await ApiService.post(`${this.BASE_URL}/advanced-search`, filter);
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 获取技能的前置技能
   */
  static async getPrerequisiteSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/prerequisites`);
    return response.data;
  }

  /**
   * 获取技能的后续技能
   */
  static async getSuccessorSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/successors`);
    return response.data;
  }

  /**
   * 获取相关技能
   */
  static async getRelatedSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/related`);
    return response.data;
  }

  /**
   * 构建技能图谱
   */
  static async buildSkillGraph(skillId: number, depth: number = 3): Promise<SkillGraph> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/graph`, {
      params: { depth }
    });
    return response.data;
  }

  /**
   * 获取用户技能掌握度
   */
  static async getUserSkillMastery(userId: number, skillId: number): Promise<UserAtomicSkillMastery> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/mastery/${userId}`);
    return response.data;
  }

  /**
   * 获取用户所有技能掌握度
   */
  static async getUserAllSkillMasteries(userId: number): Promise<UserAtomicSkillMastery[]> {
    const response = await ApiService.get(`/users/${userId}/skill-masteries`);
    return response.data;
  }

  /**
   * 更新用户技能掌握度
   */
  static async updateUserSkillMastery(
    userId: number, 
    skillId: number, 
    masteryLevel: MasteryLevel, 
    masteryScore: number
  ): Promise<UserAtomicSkillMastery> {
    const response = await ApiService.post(`${this.BASE_URL}/${skillId}/mastery/${userId}`, null, {
      params: { masteryLevel, masteryScore }
    });
    return response.data;
  }

  /**
   * 批量更新用户技能掌握度
   */
  static async batchUpdateSkillMasteries(
    userId: number,
    masteries: Array<{
      skillId: number;
      masteryLevel: MasteryLevel;
      masteryScore: number;
    }>
  ): Promise<UserAtomicSkillMastery[]> {
    const response = await ApiService.post(`/users/${userId}/skill-masteries/batch`, masteries);
    return response.data;
  }

  /**
   * 根据职业目标推荐技能
   */
  static async getRecommendedSkillsForCareer(
    careerGoalId: number,
    excludeSkillIds?: number[]
  ): Promise<AtomicSkill[]> {
    const params: any = {};
    if (excludeSkillIds && excludeSkillIds.length > 0) {
      params.excludeSkillIds = excludeSkillIds.join(',');
    }

    const response = await ApiService.get(`${this.BASE_URL}/recommendations/career/${careerGoalId}`, { params });
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 推荐学习技能
   */
  static async recommendSkillsForUser(
    userId: number,
    careerGoalId?: number
  ): Promise<AtomicSkill[]> {
    const params: any = {};
    if (careerGoalId) {
      params.careerGoalId = careerGoalId;
    }

    const response = await ApiService.get(`${this.BASE_URL}/recommendations/${userId}`, { params });
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 高级技能推荐
   */
  static async getAdvancedSkillRecommendations(
    request: SkillRecommendationRequest
  ): Promise<SkillRecommendation[]> {
    const response = await ApiService.post(`${this.BASE_URL}/advanced-recommendations`, request);
    return response.data;
  }

  /**
   * 获取技能学习路径建议
   */
  static async getSkillLearningPathSuggestions(
    skillId: number, 
    userId?: number
  ): Promise<AtomicSkill[]> {
    const params: any = {};
    if (userId) {
      params.userId = userId;
    }
    
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/learning-path-suggestions`, { params });
    return response.data;
  }

  /**
   * 批量创建原子技能
   */
  static async createAtomicSkillsBatch(skills: Partial<AtomicSkill>[]): Promise<AtomicSkill[]> {
    const apiRequests = skills.map(skill => AtomicSkillUtils.toApiRequest(skill));
    const response = await ApiService.post(`${this.BASE_URL}/batch`, apiRequests);
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 批量删除原子技能
   */
  static async deleteAtomicSkillsBatch(skillIds: number[]): Promise<string> {
    const response = await ApiService.delete(`${this.BASE_URL}/batch`, { data: skillIds });
    return response.data;
  }

  /**
   * 批量发布技能
   */
  static async publishAtomicSkillsBatch(skillIds: number[]): Promise<number> {
    const response = await ApiService.post(`${this.BASE_URL}/batch/publish`, skillIds);
    return response.data; // 返回发布的技能数量
  }

  /**
   * 获取技能统计信息
   */
  static async getSkillStatistics(): Promise<{
    totalSkills: number;
    averageRating: number;
    averageCompletionRate: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/statistics`);
    return response.data;
  }

  /**
   * 获取用户技能统计
   */
  static async getUserSkillStatistics(userId: number): Promise<{
    totalSkillsLearned: number;
    skillsMastered: number;
    averageMasteryScore: number;
    strongCategories: string[];
    improvementAreas: string[];
    learningStreak: number;
    totalLearningHours: number;
  }> {
    const response = await ApiService.get(`/users/${userId}/skill-statistics`);
    return response.data;
  }

  /**
   * 验证技能关系
   */
  static async validateSkillRelationship(
    sourceSkillId: number,
    targetSkillId: number,
    relationshipType: string
  ): Promise<boolean> {
    const response = await ApiService.post(
      `${this.BASE_URL}/${sourceSkillId}/validate-relationship/${targetSkillId}`,
      null,
      { params: { relationshipType } }
    );
    return response.data;
  }

  /**
   * 获取技能分类列表
   */
  static async getSkillCategories(): Promise<string[]> {
    const response = await ApiService.get(`${this.BASE_URL}/categories`);
    return response.data;
  }

  /**
   * 获取热门技能
   */
  static async getPopularSkills(params?: {
    page?: number;
    size?: number;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/popular`, { params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 获取趋势技能
   */
  static async getTrendingSkills(limit: number = 10): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/trending`, {
      params: { limit }
    });
    return response.data;
  }

  /**
   * 获取推荐的下一步技能
   */
  static async getNextStepSkills(userId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/next-steps/${userId}`);
    return response.data;
  }

  /**
   * 记录技能学习活动
   */
  static async recordLearningActivity(
    userId: number,
    skillId: number,
    activityType: 'view' | 'practice' | 'complete',
    duration?: number,
    score?: number
  ): Promise<void> {
    await ApiService.post(`${this.BASE_URL}/${skillId}/activity`, {
      userId,
      activityType,
      duration,
      score,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取技能学习建议
   */
  static async getSkillLearningTips(skillId: number): Promise<{
    tips: string[];
    resources: Array<{
      type: string;
      title: string;
      url: string;
      description: string;
    }>;
    commonMistakes: string[];
    bestPractices: string[];
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/learning-tips`);
    return response.data;
  }

  /**
   * 评价技能内容
   */
  static async rateSkill(
    userId: number,
    skillId: number,
    rating: number,
    comment?: string
  ): Promise<void> {
    await ApiService.post(`${this.BASE_URL}/${skillId}/rate`, {
      userId,
      rating,
      comment,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取技能评价
   */
  static async getSkillRatings(skillId: number, page: number = 0, size: number = 10): Promise<{
    ratings: Array<{
      userId: number;
      userName: string;
      rating: number;
      comment: string;
      timestamp: string;
    }>;
    averageRating: number;
    totalRatings: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/ratings`, {
      params: { page, size }
    });
    return response.data;
  }

  /**
   * 收藏/取消收藏技能
   */
  static async toggleSkillFavorite(userId: number, skillId: number): Promise<boolean> {
    const response = await ApiService.post(`${this.BASE_URL}/${skillId}/favorite/${userId}`);
    return response.data; // 返回是否已收藏
  }

  /**
   * 获取用户收藏的技能
   */
  static async getUserFavoriteSkills(userId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`/users/${userId}/favorite-skills`);
    return response.data;
  }
}
