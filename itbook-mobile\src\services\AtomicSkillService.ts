import { ApiService } from './ApiService';
import { 
  AtomicSkill, 
  SkillSearchFilter, 
  SkillRecommendationRequest, 
  SkillRecommendation,
  UserAtomicSkillMastery,
  SkillGraph,
  SkillStatistics,
  MasteryLevel
} from '../types/atomicSkill';

/**
 * 原子技能服务类
 * 提供原子技能相关的API调用功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
export class AtomicSkillService {
  private static readonly BASE_URL = '/atomic-skills';

  /**
   * 获取原子技能详情
   */
  static async getAtomicSkill(skillId: number): Promise<AtomicSkill> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}`);
    return response.data;
  }

  /**
   * 分页查询原子技能
   */
  static async getAtomicSkills(params: {
    page?: number;
    size?: number;
    category?: string;
    subcategory?: string;
    difficultyLevel?: string;
    status?: string;
  }): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    number: number;
    size: number;
  }> {
    const response = await ApiService.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * 搜索原子技能
   */
  static async searchAtomicSkills(keyword: string): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/search`, {
      params: { keyword }
    });
    return response.data;
  }

  /**
   * 高级搜索原子技能
   */
  static async advancedSearchSkills(filter: SkillSearchFilter): Promise<AtomicSkill[]> {
    const response = await ApiService.post(`${this.BASE_URL}/advanced-search`, filter);
    return response.data;
  }

  /**
   * 获取技能的前置技能
   */
  static async getPrerequisiteSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/prerequisites`);
    return response.data;
  }

  /**
   * 获取技能的后续技能
   */
  static async getSuccessorSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/successors`);
    return response.data;
  }

  /**
   * 获取相关技能
   */
  static async getRelatedSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/related`);
    return response.data;
  }

  /**
   * 构建技能图谱
   */
  static async buildSkillGraph(skillId: number, depth: number = 3): Promise<SkillGraph> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/graph`, {
      params: { depth }
    });
    return response.data;
  }

  /**
   * 获取用户技能掌握度
   */
  static async getUserSkillMastery(userId: number, skillId: number): Promise<UserAtomicSkillMastery> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/mastery/${userId}`);
    return response.data;
  }

  /**
   * 获取用户所有技能掌握度
   */
  static async getUserAllSkillMasteries(userId: number): Promise<UserAtomicSkillMastery[]> {
    const response = await ApiService.get(`/users/${userId}/skill-masteries`);
    return response.data;
  }

  /**
   * 更新用户技能掌握度
   */
  static async updateUserSkillMastery(
    userId: number, 
    skillId: number, 
    masteryLevel: MasteryLevel, 
    masteryScore: number
  ): Promise<UserAtomicSkillMastery> {
    const response = await ApiService.post(`${this.BASE_URL}/${skillId}/mastery/${userId}`, null, {
      params: { masteryLevel, masteryScore }
    });
    return response.data;
  }

  /**
   * 批量更新用户技能掌握度
   */
  static async batchUpdateSkillMasteries(
    userId: number,
    masteries: Array<{
      skillId: number;
      masteryLevel: MasteryLevel;
      masteryScore: number;
    }>
  ): Promise<UserAtomicSkillMastery[]> {
    const response = await ApiService.post(`/users/${userId}/skill-masteries/batch`, masteries);
    return response.data;
  }

  /**
   * 推荐学习技能
   */
  static async recommendSkillsForUser(
    userId: number, 
    careerGoalId?: number
  ): Promise<AtomicSkill[]> {
    const params: any = {};
    if (careerGoalId) {
      params.careerGoalId = careerGoalId;
    }
    
    const response = await ApiService.get(`${this.BASE_URL}/recommendations/${userId}`, { params });
    return response.data;
  }

  /**
   * 高级技能推荐
   */
  static async getAdvancedSkillRecommendations(
    request: SkillRecommendationRequest
  ): Promise<SkillRecommendation[]> {
    const response = await ApiService.post(`${this.BASE_URL}/advanced-recommendations`, request);
    return response.data;
  }

  /**
   * 获取技能学习路径建议
   */
  static async getSkillLearningPathSuggestions(
    skillId: number, 
    userId?: number
  ): Promise<AtomicSkill[]> {
    const params: any = {};
    if (userId) {
      params.userId = userId;
    }
    
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/learning-path-suggestions`, { params });
    return response.data;
  }

  /**
   * 获取技能统计信息
   */
  static async getSkillStatistics(): Promise<SkillStatistics> {
    const response = await ApiService.get(`${this.BASE_URL}/statistics`);
    return response.data;
  }

  /**
   * 获取用户技能统计
   */
  static async getUserSkillStatistics(userId: number): Promise<{
    totalSkillsLearned: number;
    skillsMastered: number;
    averageMasteryScore: number;
    strongCategories: string[];
    improvementAreas: string[];
    learningStreak: number;
    totalLearningHours: number;
  }> {
    const response = await ApiService.get(`/users/${userId}/skill-statistics`);
    return response.data;
  }

  /**
   * 验证技能关系
   */
  static async validateSkillRelationship(
    sourceSkillId: number,
    targetSkillId: number,
    relationshipType: string
  ): Promise<boolean> {
    const response = await ApiService.post(
      `${this.BASE_URL}/${sourceSkillId}/validate-relationship/${targetSkillId}`,
      null,
      { params: { relationshipType } }
    );
    return response.data;
  }

  /**
   * 获取技能分类列表
   */
  static async getSkillCategories(): Promise<Array<{
    category: string;
    subcategories: string[];
    skillCount: number;
  }>> {
    const response = await ApiService.get(`${this.BASE_URL}/categories`);
    return response.data;
  }

  /**
   * 获取热门技能
   */
  static async getPopularSkills(limit: number = 10): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/popular`, {
      params: { limit }
    });
    return response.data;
  }

  /**
   * 获取趋势技能
   */
  static async getTrendingSkills(limit: number = 10): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/trending`, {
      params: { limit }
    });
    return response.data;
  }

  /**
   * 获取推荐的下一步技能
   */
  static async getNextStepSkills(userId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`${this.BASE_URL}/next-steps/${userId}`);
    return response.data;
  }

  /**
   * 记录技能学习活动
   */
  static async recordLearningActivity(
    userId: number,
    skillId: number,
    activityType: 'view' | 'practice' | 'complete',
    duration?: number,
    score?: number
  ): Promise<void> {
    await ApiService.post(`${this.BASE_URL}/${skillId}/activity`, {
      userId,
      activityType,
      duration,
      score,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取技能学习建议
   */
  static async getSkillLearningTips(skillId: number): Promise<{
    tips: string[];
    resources: Array<{
      type: string;
      title: string;
      url: string;
      description: string;
    }>;
    commonMistakes: string[];
    bestPractices: string[];
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/learning-tips`);
    return response.data;
  }

  /**
   * 评价技能内容
   */
  static async rateSkill(
    userId: number,
    skillId: number,
    rating: number,
    comment?: string
  ): Promise<void> {
    await ApiService.post(`${this.BASE_URL}/${skillId}/rate`, {
      userId,
      rating,
      comment,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取技能评价
   */
  static async getSkillRatings(skillId: number, page: number = 0, size: number = 10): Promise<{
    ratings: Array<{
      userId: number;
      userName: string;
      rating: number;
      comment: string;
      timestamp: string;
    }>;
    averageRating: number;
    totalRatings: number;
  }> {
    const response = await ApiService.get(`${this.BASE_URL}/${skillId}/ratings`, {
      params: { page, size }
    });
    return response.data;
  }

  /**
   * 收藏/取消收藏技能
   */
  static async toggleSkillFavorite(userId: number, skillId: number): Promise<boolean> {
    const response = await ApiService.post(`${this.BASE_URL}/${skillId}/favorite/${userId}`);
    return response.data; // 返回是否已收藏
  }

  /**
   * 获取用户收藏的技能
   */
  static async getUserFavoriteSkills(userId: number): Promise<AtomicSkill[]> {
    const response = await ApiService.get(`/users/${userId}/favorite-skills`);
    return response.data;
  }
}
