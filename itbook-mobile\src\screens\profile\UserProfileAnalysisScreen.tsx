import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import userBehaviorAnalysisService, { 
  LearningBehaviorAnalysis,
  UserProfileScore 
} from '../../services/UserBehaviorAnalysisService';

/**
 * 用户画像分析页面
 * 展示用户的学习行为分析、技能掌握情况、学习偏好等信息
 */
const UserProfileAnalysisScreen: React.FC = () => {
  const navigation = useNavigation();
  const colors = useThemeColors();
  const user = useSelector((state: RootState) => state.auth.user);

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [behaviorAnalysis, setBehaviorAnalysis] = useState<LearningBehaviorAnalysis | null>(null);
  const [profileScore, setProfileScore] = useState<UserProfileScore | null>(null);
  const [preferredSkillCategories, setPreferredSkillCategories] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'skills' | 'insights'>('overview');

  // 加载用户画像分析数据
  const loadAnalysisData = async () => {
    if (!user?.id) {
      setError('用户信息不存在');
      setLoading(false);
      return;
    }

    try {
      setError(null);
      console.log('🔍 开始加载用户画像分析数据...');

      const [analysis, score, skillCategories] = await Promise.all([
        userBehaviorAnalysisService.getLearningBehaviorAnalysis(user.id),
        userBehaviorAnalysisService.getUserProfileScore(user.id),
        userBehaviorAnalysisService.getUserPreferredSkillCategories(user.id)
      ]);

      setBehaviorAnalysis(analysis);
      setProfileScore(score);
      setPreferredSkillCategories(skillCategories);
      
      console.log('✅ 用户画像分析数据加载完成');
    } catch (error: any) {
      console.error('❌ 加载用户画像分析数据失败:', error);
      setError(error.message || '加载用户画像分析数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadAnalysisData();
  };

  // 初始化加载
  useEffect(() => {
    loadAnalysisData();
  }, [user?.id]);

  // 渲染加载状态
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            正在分析您的学习画像...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.textSecondary} />
          <Text style={[styles.errorTitle, { color: colors.text }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadAnalysisData}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.surface }]}>
              重试
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // 渲染综合评分卡片
  const renderScoreCard = () => {
    if (!profileScore) return null;

    const scoreColor = userBehaviorAnalysisService.getScoreColor(profileScore.totalScore);
    const levelIcon = userBehaviorAnalysisService.getLevelIcon(profileScore.level);

    return (
      <View style={[styles.scoreCard, { backgroundColor: colors.surface }]}>
        <View style={styles.scoreHeader}>
          <View style={styles.scoreIconContainer}>
            <Ionicons name={levelIcon as any} size={32} color={scoreColor} />
          </View>
          <View style={styles.scoreInfo}>
            <Text style={[styles.scoreTitle, { color: colors.text }]}>
              学习画像评分
            </Text>
            <Text style={[styles.scoreLevel, { color: scoreColor }]}>
              {profileScore.level}
            </Text>
          </View>
          <View style={styles.scoreValueContainer}>
            <Text style={[styles.scoreValue, { color: scoreColor }]}>
              {profileScore.totalScore}
            </Text>
            <Text style={[styles.scoreMaxValue, { color: colors.textSecondary }]}>
              /100
            </Text>
          </View>
        </View>

        <View style={styles.scoreBreakdown}>
          <View style={styles.scoreItem}>
            <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
              学习活跃度
            </Text>
            <Text style={[styles.scoreItemValue, { color: colors.text }]}>
              {profileScore.activityScore}
            </Text>
          </View>
          <View style={styles.scoreItem}>
            <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
              学习效率
            </Text>
            <Text style={[styles.scoreItemValue, { color: colors.text }]}>
              {profileScore.efficiencyScore}
            </Text>
          </View>
          <View style={styles.scoreItem}>
            <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
              技能掌握
            </Text>
            <Text style={[styles.scoreItemValue, { color: colors.text }]}>
              {profileScore.masteryScore}
            </Text>
          </View>
          <View style={styles.scoreItem}>
            <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
              社区参与
            </Text>
            <Text style={[styles.scoreItemValue, { color: colors.text }]}>
              {profileScore.communityScore}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // 渲染Tab导航
  const renderTabNavigation = () => {
    const tabs = [
      { key: 'overview', label: '概览', icon: 'analytics-outline' },
      { key: 'skills', label: '技能', icon: 'school-outline' },
      { key: 'insights', label: '洞察', icon: 'bulb-outline' },
    ];

    return (
      <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabItem,
              selectedTab === tab.key && styles.tabItemActive,
              selectedTab === tab.key && { borderBottomColor: colors.primary }
            ]}
            onPress={() => setSelectedTab(tab.key as any)}
            activeOpacity={0.7}
          >
            <Ionicons 
              name={tab.icon as any} 
              size={20} 
              color={selectedTab === tab.key ? colors.primary : colors.textSecondary} 
            />
            <Text style={[
              styles.tabLabel,
              { color: selectedTab === tab.key ? colors.primary : colors.textSecondary }
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // 渲染概览内容
  const renderOverviewContent = () => {
    if (!behaviorAnalysis) return null;

    return (
      <View style={styles.contentContainer}>
        {/* 学习活跃度 */}
        <View style={[styles.analysisCard, { backgroundColor: colors.surface }]}>
          <View style={styles.cardHeader}>
            <Ionicons name="trending-up-outline" size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              学习活跃度
            </Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                活跃度评分
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {behaviorAnalysis.learningActivityScore}/100
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                总学习天数
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {behaviorAnalysis.totalLearningDays} 天
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                连续学习天数
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {behaviorAnalysis.continuousLearningDays} 天
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                日均学习时长
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {behaviorAnalysis.averageDailyLearningTime} 小时
              </Text>
            </View>
          </View>
        </View>

        {/* 学习偏好 */}
        <View style={[styles.analysisCard, { backgroundColor: colors.surface }]}>
          <View style={styles.cardHeader}>
            <Ionicons name="settings-outline" size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              学习偏好
            </Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                偏好学习时间
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {userBehaviorAnalysisService.getTimePreferenceName(behaviorAnalysis.preferredLearningTime)}
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                学习模式
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {userBehaviorAnalysisService.getLearningPatternName(behaviorAnalysis.learningPatternType)}
              </Text>
            </View>
            <View style={styles.preferredCategories}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                偏好技能分类
              </Text>
              <View style={styles.categoryTags}>
                {behaviorAnalysis.preferredSkillCategories.map((category, index) => (
                  <View key={index} style={[styles.categoryTag, { backgroundColor: colors.primary + '20' }]}>
                    <Text style={[styles.categoryTagText, { color: colors.primary }]}>
                      {category}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          学习画像分析
        </Text>
        
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* 综合评分卡片 */}
        {renderScoreCard()}

        {/* Tab导航 */}
        {renderTabNavigation()}

        {/* 内容区域 */}
        {selectedTab === 'overview' && renderOverviewContent()}
        
        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  loadingText: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: '500',
    marginTop: tokens.spacing('md'),
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: '700',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('body-sm'),
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('md'),
    borderBottomWidth: 1,
  },
  backButton: {
    padding: tokens.spacing('xs'),
  },
  headerTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
  },
  refreshButton: {
    padding: tokens.spacing('xs'),
  },
  scrollView: {
    flex: 1,
  },
  scoreCard: {
    margin: tokens.spacing('md'),
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  scoreIconContainer: {
    marginRight: tokens.spacing('md'),
  },
  scoreInfo: {
    flex: 1,
  },
  scoreTitle: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: '600',
    marginBottom: tokens.spacing('xs'),
  },
  scoreLevel: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: '500',
  },
  scoreValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  scoreValue: {
    fontSize: tokens.fontSize('title-lg'),
    fontWeight: '700',
  },
  scoreMaxValue: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: '500',
  },
  scoreBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scoreItem: {
    alignItems: 'center',
  },
  scoreItemLabel: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: '500',
    marginBottom: tokens.spacing('xs'),
  },
  scoreItemValue: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    overflow: 'hidden',
  },
  tabItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('md'),
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabItemActive: {
    borderBottomWidth: 2,
  },
  tabLabel: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: '600',
    marginLeft: tokens.spacing('xs'),
  },
  contentContainer: {
    padding: tokens.spacing('md'),
  },
  analysisCard: {
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    marginBottom: tokens.spacing('md'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  cardTitle: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: '700',
    marginLeft: tokens.spacing('sm'),
  },
  cardContent: {
    gap: tokens.spacing('sm'),
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: '500',
  },
  statValue: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: '600',
  },
  preferredCategories: {
    marginTop: tokens.spacing('sm'),
  },
  categoryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('sm'),
    marginTop: tokens.spacing('sm'),
  },
  categoryTag: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
  },
  categoryTagText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: '600',
  },
});

export default UserProfileAnalysisScreen;
