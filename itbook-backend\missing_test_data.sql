-- 插入缺失的测试数据
USE itbook_dev;

-- 1. 创建学习路径步骤（如果不存在）
INSERT IGNORE INTO learning_path_step (id, learning_path_id, name, description, step_type, step_order, estimated_minutes, difficulty_level, is_required, status, content_type, created_at) VALUES
(1, 1, 'Java Basic Syntax', 'Java language basic syntax', 'COURSE', 1, 480, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(2, 1, 'Java OOP Programming', 'Java OOP concepts', 'COURSE', 2, 600, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(3, 1, 'Spring Boot Introduction', 'Spring Boot framework', 'COURSE', 3, 720, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(4, 2, 'React Hooks Deep Dive', 'Advanced React Hooks', 'COURSE', 1, 360, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(5, 2, 'Redux State Management', 'Redux state management', 'COURSE', 2, 480, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(6, 3, 'Arrays and Linked Lists', 'Basic data structures', 'COURSE', 1, 360, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(7, 3, 'Sorting Algorithms', 'Common sorting algorithms', 'COURSE', 2, 420, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW());

-- 2. 创建用户职业目标（如果不存在）
INSERT IGNORE INTO user_career_goal (id, user_id, target_job_id, target_level, career_level_id, set_at, target_completion_date, is_active, priority, description, created_at, updated_at) VALUES
(1, 2, 2, 'mid', 5, '2024-01-15 10:00:00', '2025-06-01 00:00:00', 1, 1, 'Become a mid-level Java backend developer', NOW(), NOW());

-- 3. 创建用户学习路径进度
INSERT IGNORE INTO user_learning_path_progress (id, user_id, learning_path_id, status, completion_percentage, completed_steps, total_steps, studied_minutes, started_at, last_studied_at, created_at, updated_at, career_goal_id, current_step_id) VALUES
(1, 2, 1, 'IN_PROGRESS', 85.00, 2, 3, 1800, '2024-01-20 09:00:00', '2024-07-20 14:30:00', NOW(), NOW(), 1, 3),
(2, 2, 2, 'IN_PROGRESS', 60.00, 1, 2, 840, '2024-03-01 10:00:00', '2024-07-18 16:45:00', NOW(), NOW(), NULL, 2),
(3, 2, 3, 'IN_PROGRESS', 30.00, 1, 2, 360, '2024-05-15 11:00:00', '2024-07-15 20:15:00', NOW(), NOW(), NULL, 2);

-- 4. 创建用户学习步骤进度
INSERT IGNORE INTO user_step_progress (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, last_studied_at, completed_at, created_at, updated_at, difficulty_rating, quality_rating) VALUES
(1, 2, 1, 'COMPLETED', 100.00, 480, '2024-01-20 09:00:00', '2024-02-05 16:30:00', '2024-02-05 16:30:00', NOW(), NOW(), 3, 5),
(2, 2, 2, 'COMPLETED', 100.00, 600, '2024-02-06 10:00:00', '2024-02-25 15:45:00', '2024-02-25 15:45:00', NOW(), NOW(), 4, 4),
(3, 2, 3, 'IN_PROGRESS', 75.00, 540, '2024-02-26 11:00:00', '2024-07-20 14:30:00', NULL, NOW(), NOW(), 5, 5),
(4, 2, 4, 'COMPLETED', 100.00, 360, '2024-03-01 10:00:00', '2024-03-20 15:30:00', '2024-03-20 15:30:00', NOW(), NOW(), 4, 5),
(5, 2, 5, 'IN_PROGRESS', 60.00, 288, '2024-03-21 11:00:00', '2024-07-18 16:45:00', NULL, NOW(), NOW(), 5, 4),
(6, 2, 6, 'COMPLETED', 100.00, 360, '2024-05-15 11:00:00', '2024-06-05 14:20:00', '2024-06-05 14:20:00', NOW(), NOW(), 3, 4),
(7, 2, 7, 'IN_PROGRESS', 50.00, 210, '2024-06-06 10:00:00', '2024-07-15 20:15:00', NULL, NOW(), NOW(), 4, 4);

-- 5. 创建课程数据（如果不存在）
INSERT IGNORE INTO course (id, title, description, category, difficulty, duration, price, currency, instructor, status, created_at, updated_at, enrollment_count, rating, review_count) VALUES
(1, 'Java Programming Basics', 'Java language fundamentals', 'Programming', 'beginner', 8, 199.00, 'CNY', 'Teacher Zhang', 'PUBLISHED', NOW(), NOW(), 156, 4.5, 23),
(2, 'Spring Boot Practical', 'Spring Boot framework development', 'Framework', 'intermediate', 12, 299.00, 'CNY', 'Teacher Li', 'PUBLISHED', NOW(), NOW(), 89, 4.7, 15),
(3, 'React Advanced Development', 'React framework advanced features', 'Frontend', 'intermediate', 6, 249.00, 'CNY', 'Teacher Wang', 'PUBLISHED', NOW(), NOW(), 67, 4.6, 12);

-- 6. 创建社区参与数据
INSERT IGNORE INTO recommendation_feedback (id, user_id, path_id, action, rating, feedback, feedback_time, is_helpful, created_at, updated_at) VALUES
(1, 2, 1, 'LIKE', 5, 'Java backend path is very practical', '2024-02-10 14:30:00', 1, NOW(), NOW()),
(2, 2, 2, 'LIKE', 4, 'React path content is rich', '2024-04-01 16:45:00', 1, NOW(), NOW()),
(3, 2, 3, 'ACCEPT', 4, 'Data structures path is well structured', '2024-05-20 11:20:00', 1, NOW(), NOW()),
(4, 2, 1, 'LIKE', 5, 'Found a great job after completing Java path', '2024-07-01 09:15:00', 1, NOW(), NOW()),
(5, 2, 2, 'LIKE', 4, 'React Hooks changed my programming mindset', '2024-03-25 16:50:00', 1, NOW(), NOW()),
(6, 2, 3, 'LIKE', 5, 'Algorithm visualization is excellent', '2024-06-01 20:10:00', 1, NOW(), NOW()),
(7, 2, 1, 'LIKE', 5, 'Spring Boot section is very up-to-date', '2024-04-25 14:15:00', 1, NOW(), NOW()),
(8, 2, 2, 'LIKE', 4, 'TypeScript integration is well explained', '2024-04-20 15:30:00', 1, NOW(), NOW()),
(9, 2, 3, 'LIKE', 5, 'Algorithm problems are progressive', '2024-06-10 21:45:00', 1, NOW(), NOW()),
(10, 2, 1, 'LIKE', 5, 'Project practice is very valuable', '2024-03-15 10:20:00', 1, NOW(), NOW());

-- 7. 更新学习路径步骤与课程的关联
UPDATE learning_path_step SET course_id = 1 WHERE id IN (1, 2);
UPDATE learning_path_step SET course_id = 2 WHERE id = 3;
UPDATE learning_path_step SET course_id = 3 WHERE id IN (4, 5);

-- 8. 更新用户基本信息
UPDATE user SET 
    bio = 'Backend developer passionate about programming, learning full-stack development skills.',
    profession = 'Software Developer',
    company = 'Tech Company',
    updated_at = NOW()
WHERE id = 2;
