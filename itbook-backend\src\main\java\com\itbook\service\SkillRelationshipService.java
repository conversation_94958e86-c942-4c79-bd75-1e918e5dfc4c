package com.itbook.service;

import com.itbook.entity.SkillRelationship;
import com.itbook.entity.AtomicSkill;
import com.itbook.repository.SkillRelationshipRepository;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.dto.SkillRelationshipDTO;
import com.itbook.dto.SkillGraphDTO;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能关系服务类
 * 提供技能关系图谱的管理、查询、分析等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class SkillRelationshipService {

    @Autowired
    private SkillRelationshipRepository relationshipRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    /**
     * 创建技能关系
     */
    public ApiResponse<SkillRelationship> createRelationship(SkillRelationshipDTO relationshipDTO) {
        try {
            // 验证源技能和目标技能是否存在
            if (!atomicSkillRepository.existsById(relationshipDTO.getSourceSkillId())) {
                return ApiResponse.error("源技能不存在: " + relationshipDTO.getSourceSkillId());
            }
            if (!atomicSkillRepository.existsById(relationshipDTO.getTargetSkillId())) {
                return ApiResponse.error("目标技能不存在: " + relationshipDTO.getTargetSkillId());
            }

            // 检查是否已存在相同关系
            if (relationshipRepository.existsRelationship(
                    relationshipDTO.getSourceSkillId(),
                    relationshipDTO.getTargetSkillId(),
                    relationshipDTO.getRelationshipType())) {
                return ApiResponse.error("技能关系已存在");
            }

            // 检查循环依赖（仅对前置技能关系）
            if (relationshipDTO.getRelationshipType() == SkillRelationship.RelationshipType.PREREQUISITE) {
                if (relationshipRepository.existsCircularDependency(
                        relationshipDTO.getSourceSkillId(),
                        relationshipDTO.getTargetSkillId())) {
                    return ApiResponse.error("创建关系会导致循环依赖");
                }
            }

            // 创建关系实体
            SkillRelationship relationship = new SkillRelationship();
            relationship.setSourceSkillId(relationshipDTO.getSourceSkillId());
            relationship.setTargetSkillId(relationshipDTO.getTargetSkillId());
            relationship.setRelationshipType(relationshipDTO.getRelationshipType());
            relationship.setRelationshipStrength(relationshipDTO.getRelationshipStrength());
            relationship.setIsMandatory(relationshipDTO.getIsMandatory());
            relationship.setLearningSequence(relationshipDTO.getLearningSequence());
            relationship.setDescription(relationshipDTO.getDescription());
            relationship.setSource(relationshipDTO.getSource());
            relationship.setConfidenceScore(relationshipDTO.getConfidenceScore());

            SkillRelationship savedRelationship = relationshipRepository.save(relationship);
            return ApiResponse.success(savedRelationship);

        } catch (Exception e) {
            return ApiResponse.error("创建技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 更新技能关系
     */
    public ApiResponse<SkillRelationship> updateRelationship(Long relationshipId, SkillRelationshipDTO relationshipDTO) {
        try {
            Optional<SkillRelationship> optionalRelationship = relationshipRepository.findById(relationshipId);
            if (!optionalRelationship.isPresent()) {
                return ApiResponse.error("技能关系不存在");
            }

            SkillRelationship relationship = optionalRelationship.get();
            relationship.setRelationshipStrength(relationshipDTO.getRelationshipStrength());
            relationship.setIsMandatory(relationshipDTO.getIsMandatory());
            relationship.setLearningSequence(relationshipDTO.getLearningSequence());
            relationship.setDescription(relationshipDTO.getDescription());
            relationship.setConfidenceScore(relationshipDTO.getConfidenceScore());

            SkillRelationship updatedRelationship = relationshipRepository.save(relationship);
            return ApiResponse.success(updatedRelationship);

        } catch (Exception e) {
            return ApiResponse.error("更新技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除技能关系
     */
    public ApiResponse<Void> deleteRelationship(Long relationshipId) {
        try {
            if (!relationshipRepository.existsById(relationshipId)) {
                return ApiResponse.error("技能关系不存在");
            }

            relationshipRepository.deleteById(relationshipId);
            return ApiResponse.success(null);

        } catch (Exception e) {
            return ApiResponse.error("删除技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询技能关系
     */
    @Transactional(readOnly = true)
    public ApiResponse<SkillRelationship> getRelationshipById(Long relationshipId) {
        try {
            Optional<SkillRelationship> relationship = relationshipRepository.findById(relationshipId);
            if (relationship.isPresent()) {
                return ApiResponse.success(relationship.get());
            } else {
                return ApiResponse.error("技能关系不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("查询技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的所有关系
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getSkillRelationships(Long skillId) {
        try {
            List<SkillRelationship> relationships = relationshipRepository
                    .findBySourceSkillIdOrTargetSkillId(skillId, skillId);
            return ApiResponse.success(relationships);
        } catch (Exception e) {
            return ApiResponse.error("查询技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的前置技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getPrerequisiteSkills(Long skillId) {
        try {
            List<SkillRelationship> prerequisites = relationshipRepository
                    .findPrerequisiteRelationships(skillId);
            return ApiResponse.success(prerequisites);
        } catch (Exception e) {
            return ApiResponse.error("查询前置技能失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的后续技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getSuccessorSkills(Long skillId) {
        try {
            List<SkillRelationship> successors = relationshipRepository
                    .findSuccessorRelationships(skillId);
            return ApiResponse.success(successors);
        } catch (Exception e) {
            return ApiResponse.error("查询后续技能失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的相关技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getRelatedSkills(Long skillId) {
        try {
            List<SkillRelationship> relatedSkills = relationshipRepository
                    .findRelatedSkills(skillId);
            return ApiResponse.success(relatedSkills);
        } catch (Exception e) {
            return ApiResponse.error("查询相关技能失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的并行技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getCorequisiteSkills(Long skillId) {
        try {
            List<SkillRelationship> corequisites = relationshipRepository
                    .findCorequisiteRelationships(skillId);
            return ApiResponse.success(corequisites);
        } catch (Exception e) {
            return ApiResponse.error("查询并行技能失败: " + e.getMessage());
        }
    }

    /**
     * 查询技能的替代技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<SkillRelationship>> getAlternativeSkills(Long skillId) {
        try {
            List<SkillRelationship> alternatives = relationshipRepository
                    .findAlternativeRelationships(skillId);
            return ApiResponse.success(alternatives);
        } catch (Exception e) {
            return ApiResponse.error("查询替代技能失败: " + e.getMessage());
        }
    }

    /**
     * 构建技能学习路径
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<Long>> buildLearningPath(Long targetSkillId) {
        try {
            List<Long> learningPath = new ArrayList<>();
            Set<Long> visited = new HashSet<>();
            
            buildLearningPathRecursive(targetSkillId, learningPath, visited);
            
            // 移除重复并排序
            List<Long> uniquePath = learningPath.stream()
                    .distinct()
                    .collect(Collectors.toList());
            
            return ApiResponse.success(uniquePath);
        } catch (Exception e) {
            return ApiResponse.error("构建学习路径失败: " + e.getMessage());
        }
    }

    /**
     * 递归构建学习路径
     */
    private void buildLearningPathRecursive(Long skillId, List<Long> path, Set<Long> visited) {
        if (visited.contains(skillId)) {
            return; // 避免循环依赖
        }
        
        visited.add(skillId);
        
        // 获取前置技能
        List<SkillRelationship> prerequisites = relationshipRepository
                .findPrerequisitesByLearningSequence(skillId);
        
        // 先添加前置技能
        for (SkillRelationship prerequisite : prerequisites) {
            buildLearningPathRecursive(prerequisite.getSourceSkillId(), path, visited);
        }
        
        // 再添加当前技能
        if (!path.contains(skillId)) {
            path.add(skillId);
        }
    }

    /**
     * 获取技能图谱数据
     */
    @Transactional(readOnly = true)
    public ApiResponse<SkillGraphDTO> getSkillGraph(List<Long> skillIds) {
        try {
            SkillGraphDTO graphDTO = new SkillGraphDTO();
            
            // 获取技能节点
            List<AtomicSkill> skills = atomicSkillRepository.findByIdIn(new HashSet<>(skillIds));
            graphDTO.setNodes(skills);
            
            // 获取技能关系边
            List<SkillRelationship> relationships = relationshipRepository
                    .findBySourceSkillIdInOrTargetSkillIdIn(skillIds, skillIds);
            graphDTO.setEdges(relationships);
            
            return ApiResponse.success(graphDTO);
        } catch (Exception e) {
            return ApiResponse.error("获取技能图谱失败: " + e.getMessage());
        }
    }

    /**
     * 获取关系统计信息
     */
    @Transactional(readOnly = true)
    public ApiResponse<Map<String, Object>> getRelationshipStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 关系类型统计
            List<Object[]> typeStats = relationshipRepository.getRelationshipTypeStatistics();
            Map<String, Long> typeStatistics = typeStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            statistics.put("relationshipTypes", typeStatistics);
            
            // 关系来源统计
            List<Object[]> sourceStats = relationshipRepository.getRelationshipSourceStatistics();
            Map<String, Long> sourceStatistics = sourceStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            statistics.put("relationshipSources", sourceStatistics);
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取关系统计失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建技能关系
     */
    public ApiResponse<List<SkillRelationship>> createRelationshipsBatch(List<SkillRelationshipDTO> relationshipDTOs) {
        try {
            List<SkillRelationship> relationships = new ArrayList<>();
            
            for (SkillRelationshipDTO dto : relationshipDTOs) {
                // 验证和创建关系
                if (!relationshipRepository.existsRelationship(
                        dto.getSourceSkillId(), dto.getTargetSkillId(), dto.getRelationshipType())) {
                    
                    SkillRelationship relationship = new SkillRelationship();
                    relationship.setSourceSkillId(dto.getSourceSkillId());
                    relationship.setTargetSkillId(dto.getTargetSkillId());
                    relationship.setRelationshipType(dto.getRelationshipType());
                    relationship.setRelationshipStrength(dto.getRelationshipStrength());
                    relationship.setIsMandatory(dto.getIsMandatory());
                    relationship.setDescription(dto.getDescription());
                    relationship.setSource(dto.getSource());
                    relationship.setConfidenceScore(dto.getConfidenceScore());
                    
                    relationships.add(relationship);
                }
            }
            
            List<SkillRelationship> savedRelationships = relationshipRepository.saveAll(relationships);
            return ApiResponse.success(savedRelationships);
            
        } catch (Exception e) {
            return ApiResponse.error("批量创建技能关系失败: " + e.getMessage());
        }
    }

    /**
     * 验证技能关系的一致性
     */
    @Transactional(readOnly = true)
    public ApiResponse<Map<String, Object>> validateRelationshipConsistency() {
        try {
            Map<String, Object> validationResult = new HashMap<>();
            List<String> issues = new ArrayList<>();
            
            // 检查循环依赖
            // 这里可以实现更复杂的循环依赖检测算法
            
            // 检查孤立节点
            // 检查不一致的关系强度
            // 检查缺失的反向关系等
            
            validationResult.put("issues", issues);
            validationResult.put("isValid", issues.isEmpty());
            
            return ApiResponse.success(validationResult);
        } catch (Exception e) {
            return ApiResponse.error("验证关系一致性失败: " + e.getMessage());
        }
    }
}
