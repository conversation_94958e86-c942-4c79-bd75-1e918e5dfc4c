import { ApiService } from './ApiService';

/**
 * 前置技能分析服务
 * 提供技能依赖分析、学习路径生成等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */

// 技能依赖节点接口
export interface SkillDependencyNode {
  skillId: number;
  skillName: string;
  category: string;
  difficultyLevel: string;
  depth: number;
  importance: number;
  relationshipType: string;
  children: SkillDependencyNode[];
}

// 学习路径步骤接口
export interface LearningPathStep {
  step: number;
  skillId: number;
  skillName: string;
  category: string;
  difficultyLevel: string;
  estimatedHours: number;
  reason: string;
  prerequisiteSkillIds: number[];
}

// 前置技能分析结果接口
export interface PrerequisiteAnalysisResult {
  targetSkillId: number;
  targetSkillName: string;
  directPrerequisites: SkillDependencyNode[];
  allPrerequisites: SkillDependencyNode[];
  recommendedPath: LearningPathStep[];
  totalPrerequisites: number;
  maxDepth: number;
  complexity: number;
  hasCycles: boolean;
}

// 技能依赖统计信息接口
export interface PrerequisiteStatistics {
  skillId: number;
  skillName: string;
  totalPrerequisites: number;
  directPrerequisites: number;
  maxDepth: number;
  complexity: number;
  hasCycles: boolean;
  learningSteps: number;
  totalEstimatedHours: number;
  difficultyDistribution: Record<string, number>;
  categoryDistribution: Record<string, number>;
}

// 循环依赖检测结果接口
export interface CycleDetectionResult {
  skillId: number;
  skillName: string;
  hasCycles: boolean;
  totalPrerequisites: number;
  maxDepth: number;
  status?: string;
  warning?: string;
  recommendation?: string;
}

class PrerequisiteAnalysisService {
  /**
   * 分析指定技能的前置技能
   */
  async analyzePrerequisites(skillId: number): Promise<PrerequisiteAnalysisResult> {
    console.log('🔍 分析技能前置依赖:', { skillId });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}`);
      
      console.log('✅ 前置技能分析成功:', {
        skillId,
        totalPrerequisites: response.data.totalPrerequisites,
        complexity: response.data.complexity
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 分析前置技能失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能的直接前置技能
   */
  async getDirectPrerequisites(skillId: number): Promise<SkillDependencyNode[]> {
    console.log('🔍 获取直接前置技能:', { skillId });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}/direct`);
      
      console.log('✅ 获取直接前置技能成功:', {
        skillId,
        count: response.data.length
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 获取直接前置技能失败:', error);
      throw error;
    }
  }

  /**
   * 生成学习路径
   */
  async generateLearningPath(skillId: number): Promise<LearningPathStep[]> {
    console.log('🔍 生成学习路径:', { skillId });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}/learning-path`);
      
      console.log('✅ 生成学习路径成功:', {
        skillId,
        steps: response.data.length,
        totalHours: response.data.reduce((sum: number, step: LearningPathStep) => sum + step.estimatedHours, 0)
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 生成学习路径失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能依赖统计信息
   */
  async getPrerequisiteStatistics(skillId: number): Promise<PrerequisiteStatistics> {
    console.log('🔍 获取技能依赖统计:', { skillId });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}/statistics`);
      
      console.log('✅ 获取技能依赖统计成功:', {
        skillId,
        totalPrerequisites: response.data.totalPrerequisites,
        totalHours: response.data.totalEstimatedHours
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 获取技能依赖统计失败:', error);
      throw error;
    }
  }

  /**
   * 批量分析多个技能的前置依赖
   */
  async batchAnalyzePrerequisites(skillIds: number[]): Promise<Record<number, PrerequisiteAnalysisResult>> {
    console.log('🔍 批量分析前置依赖:', { skillIds });
    
    try {
      const response = await ApiService.post('/prerequisite-analysis/skills/batch-analyze', skillIds);
      
      console.log('✅ 批量分析前置依赖成功:', {
        requested: skillIds.length,
        analyzed: Object.keys(response.data).length
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 批量分析前置依赖失败:', error);
      throw error;
    }
  }

  /**
   * 检测技能依赖中的循环引用
   */
  async detectCycles(skillId: number): Promise<CycleDetectionResult> {
    console.log('🔍 检测循环依赖:', { skillId });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}/cycle-detection`);
      
      console.log('✅ 循环依赖检测完成:', {
        skillId,
        hasCycles: response.data.hasCycles
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 检测循环依赖失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能依赖树结构
   */
  async getDependencyTree(skillId: number, maxDepth: number = 5): Promise<SkillDependencyNode> {
    console.log('🔍 获取依赖树结构:', { skillId, maxDepth });
    
    try {
      const response = await ApiService.get(`/prerequisite-analysis/skills/${skillId}/dependency-tree`, {
        params: { maxDepth }
      });
      
      console.log('✅ 获取依赖树结构成功:', {
        skillId,
        childrenCount: response.data.children?.length || 0
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ 获取依赖树结构失败:', error);
      throw error;
    }
  }

  /**
   * 计算学习路径的总时间
   */
  calculateTotalHours(learningPath: LearningPathStep[]): number {
    return learningPath.reduce((total, step) => total + step.estimatedHours, 0);
  }

  /**
   * 获取难度级别的显示文本
   */
  getDifficultyText(level: string): string {
    const difficultyMap: Record<string, string> = {
      'beginner': '初级',
      'intermediate': '中级',
      'advanced': '高级',
      'expert': '专家'
    };
    return difficultyMap[level] || level;
  }

  /**
   * 获取关系类型的显示文本
   */
  getRelationshipText(type: string): string {
    const relationshipMap: Record<string, string> = {
      'PREREQUISITE': '前置技能',
      'COREQUISITE': '并行技能',
      'SUCCESSOR': '后续技能',
      'RELATED': '相关技能',
      'ALTERNATIVE': '替代技能'
    };
    return relationshipMap[type] || type;
  }

  /**
   * 格式化复杂度显示
   */
  formatComplexity(complexity: number): string {
    if (complexity === 0) return '简单';
    if (complexity < 2) return '较简单';
    if (complexity < 4) return '中等';
    if (complexity < 6) return '较复杂';
    return '复杂';
  }

  /**
   * 生成学习建议
   */
  generateLearningAdvice(statistics: PrerequisiteStatistics): string[] {
    const advice: string[] = [];
    
    if (statistics.totalPrerequisites === 0) {
      advice.push('这是基础技能，可以直接开始学习');
    } else if (statistics.totalPrerequisites <= 2) {
      advice.push('前置技能较少，学习难度适中');
    } else if (statistics.totalPrerequisites <= 5) {
      advice.push('需要掌握多个前置技能，建议按推荐路径学习');
    } else {
      advice.push('前置技能较多，建议制定详细的学习计划');
    }
    
    if (statistics.totalEstimatedHours <= 40) {
      advice.push('预计学习时间较短，可以快速掌握');
    } else if (statistics.totalEstimatedHours <= 100) {
      advice.push('需要投入一定时间学习，建议分阶段进行');
    } else {
      advice.push('需要长期学习，建议制定长期学习计划');
    }
    
    if (statistics.complexity >= 4) {
      advice.push('技能体系较复杂，建议寻求导师指导');
    }
    
    return advice;
  }
}

// 导出单例实例
export default new PrerequisiteAnalysisService();
