package com.itbook.controller;

import com.itbook.entity.AtomicSkill;
import com.itbook.service.AtomicSkillService;
import com.itbook.common.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 原子技能控制器测试类
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@WebMvcTest(AtomicSkillController.class)
class AtomicSkillControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AtomicSkillService atomicSkillService;

    @Autowired
    private ObjectMapper objectMapper;

    private AtomicSkill testSkill;

    @BeforeEach
    void setUp() {
        testSkill = new AtomicSkill();
        testSkill.setId(1L);
        testSkill.setSkillCode("JAVA_BASIC");
        testSkill.setName("Java基础");
        testSkill.setDescription("Java编程语言基础知识");
        testSkill.setCategory("编程语言");
        testSkill.setSubcategory("Java");
        testSkill.setDifficultyLevel(AtomicSkill.DifficultyLevel.BEGINNER);
        testSkill.setSkillType(AtomicSkill.SkillType.TECHNICAL);
        testSkill.setEstimatedHours(40);
        testSkill.setAverageRating(new BigDecimal("4.5"));
        testSkill.setLearnerCount(1000L);
        testSkill.setCompletionRate(new BigDecimal("0.85"));
        testSkill.setStatus(AtomicSkill.Status.PUBLISHED);
        testSkill.setIsActive(true);
        testSkill.setKeywords("java,编程,基础");
        testSkill.setCreatedAt(LocalDateTime.now());
        testSkill.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testGetAtomicSkillById_Success() throws Exception {
        // Given
        when(atomicSkillService.getAtomicSkillById(1L))
                .thenReturn(ApiResponse.success(testSkill));

        // When & Then
        mockMvc.perform(get("/atomic-skills/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("Java基础"))
                .andExpect(jsonPath("$.data.skillCode").value("JAVA_BASIC"));
    }

    @Test
    void testGetAtomicSkillById_NotFound() throws Exception {
        // Given
        when(atomicSkillService.getAtomicSkillById(999L))
                .thenReturn(ApiResponse.error("技能不存在"));

        // When & Then
        mockMvc.perform(get("/atomic-skills/999"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(40000))
                .andExpected(jsonPath("$.message").value("技能不存在"));
    }

    @Test
    void testSearchAtomicSkills_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.searchAtomicSkills("Java"))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/search")
                .param("keyword", "Java"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("Java基础"));
    }

    @Test
    void testAdvancedSearchAtomicSkills_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.advancedSearchAtomicSkills(
                eq("Java"), eq("编程语言"), eq("Java"), 
                eq(AtomicSkill.DifficultyLevel.BEGINNER), 
                eq(AtomicSkill.SkillType.TECHNICAL),
                eq(30), eq(50), eq(4.0), eq(5.0), eq("java"),
                eq("averageRating"), eq("desc"), eq(20)))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/advanced-search")
                .param("keyword", "Java")
                .param("category", "编程语言")
                .param("subcategory", "Java")
                .param("difficultyLevel", "BEGINNER")
                .param("skillType", "TECHNICAL")
                .param("minHours", "30")
                .param("maxHours", "50")
                .param("minRating", "4.0")
                .param("maxRating", "5.0")
                .param("tag", "java")
                .param("sortBy", "averageRating")
                .param("sortDirection", "desc")
                .param("limit", "20"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("Java基础"));
    }

    @Test
    void testGetSkillsByCategory_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getSkillsByCategory("编程语言", null))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/category/编程语言"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].category").value("编程语言"));
    }

    @Test
    void testGetSkillsByCategoryWithSubcategory_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getSkillsByCategory("编程语言", "Java"))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/category/编程语言")
                .param("subcategory", "Java"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].subcategory").value("Java"));
    }

    @Test
    void testGetSkillsByDifficulty_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getSkillsByDifficulty(AtomicSkill.DifficultyLevel.BEGINNER))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/difficulty/BEGINNER"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].difficultyLevel").value("BEGINNER"));
    }

    @Test
    void testGetSkillsByType_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getSkillsByType(AtomicSkill.SkillType.TECHNICAL))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/type/TECHNICAL"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].skillType").value("TECHNICAL"));
    }

    @Test
    void testGetSkillsByTag_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getSkillsByTag("java"))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/tag/java"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].keywords").value("java,编程,基础"));
    }

    @Test
    void testGetPopularSkills_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getPopularSkills(10))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/popular")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].learnerCount").value(1000));
    }

    @Test
    void testGetRecommendedSkills_Success() throws Exception {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillService.getRecommendedSkills(10))
                .thenReturn(ApiResponse.success(skills));

        // When & Then
        mockMvc.perform(get("/atomic-skills/recommended")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].averageRating").value(4.5));
    }

    @Test
    void testGetSkillStatistics_Success() throws Exception {
        // Given
        when(atomicSkillService.getSkillStatistics())
                .thenReturn(ApiResponse.success(java.util.Map.of(
                        "totalSkills", 100L,
                        "averageRating", 4.2,
                        "categories", java.util.Map.of("编程语言", 50L)
                )));

        // When & Then
        mockMvc.perform(get("/atomic-skills/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(20000))
                .andExpect(jsonPath("$.data.totalSkills").value(100))
                .andExpect(jsonPath("$.data.averageRating").value(4.2));
    }
}
