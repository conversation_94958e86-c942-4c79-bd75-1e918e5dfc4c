package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.dto.DynamicPathGenerationRequest;
import com.itbook.dto.PersonalizationFactors;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态学习路径生成服务
 * 基于用户画像、职业目标和技能图谱生成个性化学习路径
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class DynamicPathGenerationService {

    @Autowired
    private DynamicLearningPathRepository dynamicPathRepository;

    @Autowired
    private DynamicPathStepRepository pathStepRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private SkillRelationshipRepository skillRelationshipRepository;

    @Autowired
    private UserAtomicSkillMasteryRepository masteryRepository;

    @Autowired
    private CareerSkillMappingRepository careerSkillMappingRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    /**
     * 生成动态学习路径
     */
    public ApiResponse<DynamicLearningPath> generateDynamicPath(DynamicPathGenerationRequest request) {
        try {
            // 1. 获取用户画像和职业目标
            PersonalizationFactors factors = analyzePersonalizationFactors(request.getUserId(), request.getCareerGoalId());
            
            // 2. 获取目标技能集合
            List<AtomicSkill> targetSkills = getTargetSkillsForCareer(request.getCareerGoalId());
            
            // 3. 分析用户当前技能水平
            Map<Long, UserAtomicSkillMastery> currentMasteries = getCurrentSkillMasteries(request.getUserId());
            
            // 4. 构建学习路径
            List<AtomicSkill> pathSkills = buildOptimalLearningSequence(targetSkills, currentMasteries, factors);
            
            // 5. 创建动态学习路径
            DynamicLearningPath dynamicPath = createDynamicPath(request, factors, pathSkills);
            
            // 6. 创建路径步骤
            createPathSteps(dynamicPath, pathSkills, factors);
            
            // 7. 计算路径质量分数
            BigDecimal qualityScore = calculatePathQuality(dynamicPath, factors);
            dynamicPath.setQualityScore(qualityScore);
            
            DynamicLearningPath savedPath = dynamicPathRepository.save(dynamicPath);
            return ApiResponse.success(savedPath);
            
        } catch (Exception e) {
            return ApiResponse.error("生成动态学习路径失败: " + e.getMessage());
        }
    }

    /**
     * 分析个性化因子
     */
    private PersonalizationFactors analyzePersonalizationFactors(Long userId, Long careerGoalId) {
        PersonalizationFactors factors = new PersonalizationFactors();
        
        // 获取用户画像
        UserProfile userProfile = userProfileRepository.findByUserId(userId).orElse(null);
        if (userProfile != null) {
            factors.setLearningStyle(userProfile.getLearningStyle());
            factors.setAvailableTimePerWeek(userProfile.getAvailableTimePerWeek());
            factors.setPreferredLearningPace(userProfile.getPreferredLearningPace());
            factors.setCurrentSkillLevel(userProfile.getCurrentSkillLevel());
            factors.setHasProgrammingExperience(userProfile.getHasProgrammingExperience());
        }
        
        // 分析用户学习历史
        List<UserAtomicSkillMastery> masteries = masteryRepository.findByUserId(userId);
        factors.setTotalSkillsMastered(masteries.size());
        factors.setAverageCompletionRate(calculateAverageCompletionRate(masteries));
        factors.setPreferredDifficulty(inferPreferredDifficulty(masteries));
        
        return factors;
    }

    /**
     * 获取职业目标的目标技能
     */
    private List<AtomicSkill> getTargetSkillsForCareer(Long careerGoalId) {
        List<CareerSkillMapping> mappings = careerSkillMappingRepository.findByCareerGoalId(careerGoalId);
        
        return mappings.stream()
                .map(mapping -> mapping.getAtomicSkill())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户当前技能掌握情况
     */
    private Map<Long, UserAtomicSkillMastery> getCurrentSkillMasteries(Long userId) {
        List<UserAtomicSkillMastery> masteries = masteryRepository.findByUserId(userId);
        
        return masteries.stream()
                .collect(Collectors.toMap(
                    UserAtomicSkillMastery::getAtomicSkillId,
                    mastery -> mastery
                ));
    }

    /**
     * 构建最优学习序列
     */
    private List<AtomicSkill> buildOptimalLearningSequence(
            List<AtomicSkill> targetSkills, 
            Map<Long, UserAtomicSkillMastery> currentMasteries,
            PersonalizationFactors factors) {
        
        // 1. 过滤已掌握的技能
        List<AtomicSkill> unmastered = targetSkills.stream()
                .filter(skill -> !isMastered(skill.getId(), currentMasteries))
                .collect(Collectors.toList());
        
        // 2. 构建技能依赖图
        Map<Long, List<Long>> dependencyGraph = buildDependencyGraph(unmastered);
        
        // 3. 拓扑排序确定基础顺序
        List<AtomicSkill> baseSequence = topologicalSort(unmastered, dependencyGraph);
        
        // 4. 基于个性化因子调整顺序
        List<AtomicSkill> personalizedSequence = personalizeSequence(baseSequence, factors);
        
        return personalizedSequence;
    }

    /**
     * 构建技能依赖图
     */
    private Map<Long, List<Long>> buildDependencyGraph(List<AtomicSkill> skills) {
        Map<Long, List<Long>> graph = new HashMap<>();
        Set<Long> skillIds = skills.stream().map(AtomicSkill::getId).collect(Collectors.toSet());
        
        for (AtomicSkill skill : skills) {
            List<SkillRelationship> prerequisites = skillRelationshipRepository
                    .findByTargetSkillIdAndRelationshipType(skill.getId(), SkillRelationship.RelationshipType.PREREQUISITE);
            
            List<Long> deps = prerequisites.stream()
                    .map(SkillRelationship::getSourceSkillId)
                    .filter(skillIds::contains) // 只考虑目标技能集合内的依赖
                    .collect(Collectors.toList());
            
            graph.put(skill.getId(), deps);
        }
        
        return graph;
    }

    /**
     * 拓扑排序
     */
    private List<AtomicSkill> topologicalSort(List<AtomicSkill> skills, Map<Long, List<Long>> dependencyGraph) {
        Map<Long, AtomicSkill> skillMap = skills.stream()
                .collect(Collectors.toMap(AtomicSkill::getId, skill -> skill));
        
        Map<Long, Integer> inDegree = new HashMap<>();
        Queue<Long> queue = new LinkedList<>();
        List<AtomicSkill> result = new ArrayList<>();
        
        // 计算入度
        for (AtomicSkill skill : skills) {
            inDegree.put(skill.getId(), 0);
        }
        
        for (List<Long> deps : dependencyGraph.values()) {
            for (Long dep : deps) {
                inDegree.put(dep, inDegree.getOrDefault(dep, 0) + 1);
            }
        }
        
        // 找到入度为0的节点
        for (Map.Entry<Long, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }
        
        // 拓扑排序
        while (!queue.isEmpty()) {
            Long skillId = queue.poll();
            result.add(skillMap.get(skillId));
            
            List<Long> deps = dependencyGraph.getOrDefault(skillId, Collections.emptyList());
            for (Long dep : deps) {
                inDegree.put(dep, inDegree.get(dep) - 1);
                if (inDegree.get(dep) == 0) {
                    queue.offer(dep);
                }
            }
        }
        
        return result;
    }

    /**
     * 个性化序列调整
     */
    private List<AtomicSkill> personalizeSequence(List<AtomicSkill> baseSequence, PersonalizationFactors factors) {
        List<AtomicSkill> personalizedSequence = new ArrayList<>(baseSequence);
        
        // 根据学习风格调整
        if (factors.getLearningStyle() == UserProfile.LearningStyle.PRACTICAL) {
            // 实践型学习者优先学习实用技能
            personalizedSequence.sort((a, b) -> {
                int aScore = (a.getSkillType() == AtomicSkill.SkillType.CORE) ? 2 : 1;
                int bScore = (b.getSkillType() == AtomicSkill.SkillType.CORE) ? 2 : 1;
                return Integer.compare(bScore, aScore);
            });
        }
        
        // 根据难度偏好调整
        if (factors.getPreferredDifficulty() == AtomicSkill.DifficultyLevel.BEGINNER) {
            // 偏好简单的学习者，优先安排简单技能
            personalizedSequence.sort((a, b) -> 
                a.getDifficultyLevel().compareTo(b.getDifficultyLevel()));
        }
        
        return personalizedSequence;
    }

    /**
     * 创建动态学习路径
     */
    private DynamicLearningPath createDynamicPath(
            DynamicPathGenerationRequest request, 
            PersonalizationFactors factors,
            List<AtomicSkill> pathSkills) {
        
        DynamicLearningPath path = new DynamicLearningPath();
        path.setUserId(request.getUserId());
        path.setCareerGoalId(request.getCareerGoalId());
        path.setBasePathId(request.getBasePathId());
        path.setName(generatePathName(request));
        path.setDescription(generatePathDescription(request, factors));
        path.setPathType(request.getPathType());
        path.setLearningStyle(factors.getLearningStyle());
        path.setDifficultyPreference(factors.getDifficultyPreference());
        path.setTimeConstraint(request.getTimeConstraint());
        path.setTotalSkills(pathSkills.size());
        path.setEstimatedHours(calculateTotalEstimatedHours(pathSkills));
        path.setGenerationAlgorithm("v1.0");
        path.setStatus(DynamicLearningPath.Status.DRAFT);
        
        return path;
    }

    /**
     * 创建路径步骤
     */
    private void createPathSteps(DynamicLearningPath path, List<AtomicSkill> skills, PersonalizationFactors factors) {
        List<DynamicPathStep> steps = new ArrayList<>();
        
        for (int i = 0; i < skills.size(); i++) {
            AtomicSkill skill = skills.get(i);
            
            DynamicPathStep step = new DynamicPathStep();
            step.setPathId(path.getId());
            step.setAtomicSkillId(skill.getId());
            step.setStepOrder(i + 1);
            step.setStepType(DynamicPathStep.StepType.LEARN);
            step.setEstimatedHours(skill.getEstimatedHours());
            step.setDifficultyAdjustment(calculateDifficultyAdjustment(skill, factors));
            step.setPriorityWeight(calculatePriorityWeight(skill, factors));
            step.setPersonalizationReason(generatePersonalizationReason(skill, factors));
            step.setStatus(DynamicPathStep.Status.NOT_STARTED);
            
            steps.add(step);
        }
        
        pathStepRepository.saveAll(steps);
    }

    /**
     * 计算路径质量分数
     */
    private BigDecimal calculatePathQuality(DynamicLearningPath path, PersonalizationFactors factors) {
        // 基于多个维度计算质量分数
        BigDecimal completenessScore = calculateCompletenessScore(path);
        BigDecimal personalizedScore = calculatePersonalizationScore(path, factors);
        BigDecimal feasibilityScore = calculateFeasibilityScore(path, factors);
        
        // 加权平均
        BigDecimal totalScore = completenessScore.multiply(new BigDecimal("0.4"))
                .add(personalizedScore.multiply(new BigDecimal("0.3")))
                .add(feasibilityScore.multiply(new BigDecimal("0.3")));
        
        return totalScore;
    }

    // 辅助方法
    private boolean isMastered(Long skillId, Map<Long, UserAtomicSkillMastery> masteries) {
        UserAtomicSkillMastery mastery = masteries.get(skillId);
        return mastery != null && mastery.getMasteryLevel() != UserAtomicSkillMastery.MasteryLevel.NONE;
    }

    private BigDecimal calculateAverageCompletionRate(List<UserAtomicSkillMastery> masteries) {
        if (masteries.isEmpty()) return BigDecimal.ZERO;
        
        BigDecimal total = masteries.stream()
                .map(UserAtomicSkillMastery::getMasteryScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return total.divide(new BigDecimal(masteries.size()), 2, BigDecimal.ROUND_HALF_UP);
    }

    private AtomicSkill.DifficultyLevel inferPreferredDifficulty(List<UserAtomicSkillMastery> masteries) {
        // 基于用户历史学习记录推断偏好难度
        Map<AtomicSkill.DifficultyLevel, Long> difficultyCount = masteries.stream()
                .filter(m -> m.getAtomicSkill() != null)
                .collect(Collectors.groupingBy(
                    m -> m.getAtomicSkill().getDifficultyLevel(),
                    Collectors.counting()
                ));
        
        return difficultyCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(AtomicSkill.DifficultyLevel.BEGINNER);
    }

    private String generatePathName(DynamicPathGenerationRequest request) {
        return "个性化学习路径 - " + LocalDateTime.now().toString();
    }

    private String generatePathDescription(DynamicPathGenerationRequest request, PersonalizationFactors factors) {
        return "基于用户画像和职业目标生成的个性化学习路径";
    }

    private Integer calculateTotalEstimatedHours(List<AtomicSkill> skills) {
        return skills.stream()
                .mapToInt(skill -> skill.getEstimatedHours() != null ? skill.getEstimatedHours() : 0)
                .sum();
    }

    private BigDecimal calculateDifficultyAdjustment(AtomicSkill skill, PersonalizationFactors factors) {
        // 基于用户能力和偏好调整难度
        return BigDecimal.ONE;
    }

    private BigDecimal calculatePriorityWeight(AtomicSkill skill, PersonalizationFactors factors) {
        // 基于技能重要性和用户偏好计算优先级
        return BigDecimal.ONE;
    }

    private String generatePersonalizationReason(AtomicSkill skill, PersonalizationFactors factors) {
        return "基于用户学习风格和能力水平的个性化推荐";
    }

    private BigDecimal calculateCompletenessScore(DynamicLearningPath path) {
        return new BigDecimal("0.8");
    }

    private BigDecimal calculatePersonalizationScore(DynamicLearningPath path, PersonalizationFactors factors) {
        return new BigDecimal("0.7");
    }

    private BigDecimal calculateFeasibilityScore(DynamicLearningPath path, PersonalizationFactors factors) {
        return new BigDecimal("0.9");
    }
}
