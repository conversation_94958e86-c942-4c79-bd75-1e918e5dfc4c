package com.itbook.service;

import com.itbook.entity.CareerSkillMapping;
import com.itbook.entity.AtomicSkill;
import com.itbook.entity.CareerSkill;
import com.itbook.repository.CareerSkillMappingRepository;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.CareerSkillRepository;
import com.itbook.dto.CareerSkillMappingDTO;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 职业技能映射服务类
 * 提供职业技能与原子技能映射关系的管理功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class CareerSkillMappingService {

    @Autowired
    private CareerSkillMappingRepository mappingRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private CareerSkillRepository careerSkillRepository;

    /**
     * 创建技能映射
     */
    public ApiResponse<CareerSkillMapping> createMapping(CareerSkillMappingDTO mappingDTO) {
        try {
            // 验证职业技能和原子技能是否存在
            if (!careerSkillRepository.existsById(mappingDTO.getCareerSkillId())) {
                return ApiResponse.error("职业技能不存在: " + mappingDTO.getCareerSkillId());
            }
            if (!atomicSkillRepository.existsById(mappingDTO.getAtomicSkillId())) {
                return ApiResponse.error("原子技能不存在: " + mappingDTO.getAtomicSkillId());
            }

            // 检查映射是否已存在
            if (mappingRepository.existsByCareerSkillIdAndAtomicSkillId(
                    mappingDTO.getCareerSkillId(), mappingDTO.getAtomicSkillId())) {
                return ApiResponse.error("技能映射已存在");
            }

            // 创建映射实体
            CareerSkillMapping mapping = new CareerSkillMapping();
            mapping.setCareerSkillId(mappingDTO.getCareerSkillId());
            mapping.setAtomicSkillId(mappingDTO.getAtomicSkillId());
            mapping.setWeight(mappingDTO.getWeight());
            mapping.setImportance(mappingDTO.getImportance());
            mapping.setRequiredMasteryLevel(mappingDTO.getRequiredMasteryLevel());
            mapping.setMappingReason(mappingDTO.getMappingReason());
            mapping.setMappingSource(mappingDTO.getMappingSource());
            mapping.setConfidenceScore(mappingDTO.getConfidenceScore());
            mapping.setCreatedBy(mappingDTO.getCreatedBy());

            CareerSkillMapping savedMapping = mappingRepository.save(mapping);
            return ApiResponse.success(savedMapping);

        } catch (Exception e) {
            return ApiResponse.error("创建技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 更新技能映射
     */
    public ApiResponse<CareerSkillMapping> updateMapping(Long mappingId, CareerSkillMappingDTO mappingDTO) {
        try {
            Optional<CareerSkillMapping> optionalMapping = mappingRepository.findById(mappingId);
            if (!optionalMapping.isPresent()) {
                return ApiResponse.error("技能映射不存在");
            }

            CareerSkillMapping mapping = optionalMapping.get();
            mapping.setWeight(mappingDTO.getWeight());
            mapping.setImportance(mappingDTO.getImportance());
            mapping.setRequiredMasteryLevel(mappingDTO.getRequiredMasteryLevel());
            mapping.setMappingReason(mappingDTO.getMappingReason());
            mapping.setConfidenceScore(mappingDTO.getConfidenceScore());

            CareerSkillMapping updatedMapping = mappingRepository.save(mapping);
            return ApiResponse.success(updatedMapping);

        } catch (Exception e) {
            return ApiResponse.error("更新技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 删除技能映射
     */
    public ApiResponse<Void> deleteMapping(Long mappingId) {
        try {
            if (!mappingRepository.existsById(mappingId)) {
                return ApiResponse.error("技能映射不存在");
            }

            mappingRepository.deleteById(mappingId);
            return ApiResponse.success(null);

        } catch (Exception e) {
            return ApiResponse.error("删除技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 根据职业技能ID查询映射关系
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getMappingsByCareerSkill(Long careerSkillId) {
        try {
            List<CareerSkillMapping> mappings = mappingRepository
                    .findByCareerSkillIdOrderByWeight(careerSkillId);
            return ApiResponse.success(mappings);
        } catch (Exception e) {
            return ApiResponse.error("查询职业技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 根据原子技能ID查询映射关系
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getMappingsByAtomicSkill(Long atomicSkillId) {
        try {
            List<CareerSkillMapping> mappings = mappingRepository
                    .findByAtomicSkillIdOrderByWeight(atomicSkillId);
            return ApiResponse.success(mappings);
        } catch (Exception e) {
            return ApiResponse.error("查询原子技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 根据职业目标ID查询相关的原子技能映射
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getMappingsByCareerGoal(Long careerGoalId) {
        try {
            List<CareerSkillMapping> mappings = mappingRepository
                    .findByCareerGoalId(careerGoalId);
            return ApiResponse.success(mappings);
        } catch (Exception e) {
            return ApiResponse.error("查询职业目标技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 根据职业目标ID获取推荐的原子技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<AtomicSkill>> getRecommendedAtomicSkills(Long careerGoalId, int limit) {
        try {
            // 获取推荐的原子技能ID和权重
            List<Object[]> recommendations = mappingRepository
                    .getRecommendedAtomicSkillsByCareerGoal(careerGoalId);
            
            // 提取技能ID并按权重排序
            List<Long> skillIds = recommendations.stream()
                    .sorted((r1, r2) -> ((BigDecimal) r2[1]).compareTo((BigDecimal) r1[1]))
                    .limit(limit)
                    .map(row -> (Long) row[0])
                    .collect(Collectors.toList());
            
            // 获取技能详情
            List<AtomicSkill> skills = atomicSkillRepository.findByIdIn(new HashSet<>(skillIds));
            
            // 按推荐权重排序
            Map<Long, BigDecimal> weightMap = recommendations.stream()
                    .collect(Collectors.toMap(
                            row -> (Long) row[0],
                            row -> (BigDecimal) row[1]
                    ));
            
            skills.sort((s1, s2) -> {
                BigDecimal w1 = weightMap.getOrDefault(s1.getId(), BigDecimal.ZERO);
                BigDecimal w2 = weightMap.getOrDefault(s2.getId(), BigDecimal.ZERO);
                return w2.compareTo(w1);
            });
            
            return ApiResponse.success(skills);
            
        } catch (Exception e) {
            return ApiResponse.error("获取推荐原子技能失败: " + e.getMessage());
        }
    }

    /**
     * 查询关键技能映射
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getCriticalMappings() {
        try {
            List<CareerSkillMapping> criticalMappings = mappingRepository.findCriticalMappings();
            return ApiResponse.success(criticalMappings);
        } catch (Exception e) {
            return ApiResponse.error("查询关键技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 查询高置信度映射
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getHighConfidenceMappings(BigDecimal minConfidence) {
        try {
            List<CareerSkillMapping> highConfidenceMappings = mappingRepository
                    .findHighConfidenceMappings(minConfidence);
            return ApiResponse.success(highConfidenceMappings);
        } catch (Exception e) {
            return ApiResponse.error("查询高置信度映射失败: " + e.getMessage());
        }
    }

    /**
     * 查询需要验证的映射
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<CareerSkillMapping>> getMappingsNeedingVerification(BigDecimal maxConfidence) {
        try {
            List<CareerSkillMapping> mappingsNeedingVerification = mappingRepository
                    .findMappingsNeedingVerification(maxConfidence);
            return ApiResponse.success(mappingsNeedingVerification);
        } catch (Exception e) {
            return ApiResponse.error("查询需要验证的映射失败: " + e.getMessage());
        }
    }

    /**
     * 获取映射统计信息
     */
    @Transactional(readOnly = true)
    public ApiResponse<Map<String, Object>> getMappingStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 重要程度统计
            List<Object[]> importanceStats = mappingRepository.getImportanceStatistics();
            Map<String, Long> importanceStatistics = importanceStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            statistics.put("importance", importanceStatistics);
            
            // 要求掌握水平统计
            List<Object[]> masteryStats = mappingRepository.getRequiredMasteryLevelStatistics();
            Map<String, Long> masteryStatistics = masteryStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            statistics.put("requiredMasteryLevel", masteryStatistics);
            
            // 映射来源统计
            List<Object[]> sourceStats = mappingRepository.getMappingSourceStatistics();
            Map<String, Long> sourceStatistics = sourceStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            statistics.put("mappingSource", sourceStatistics);
            
            // 平均权重和置信度
            BigDecimal avgWeight = mappingRepository.getAverageWeight();
            BigDecimal avgConfidence = mappingRepository.getAverageConfidenceScore();
            statistics.put("averageWeight", avgWeight != null ? avgWeight : BigDecimal.ZERO);
            statistics.put("averageConfidence", avgConfidence != null ? avgConfidence : BigDecimal.ZERO);
            
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            return ApiResponse.error("获取映射统计失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建技能映射
     */
    public ApiResponse<List<CareerSkillMapping>> createMappingsBatch(List<CareerSkillMappingDTO> mappingDTOs) {
        try {
            List<CareerSkillMapping> mappings = new ArrayList<>();
            
            for (CareerSkillMappingDTO dto : mappingDTOs) {
                // 验证和创建映射
                if (!mappingRepository.existsByCareerSkillIdAndAtomicSkillId(
                        dto.getCareerSkillId(), dto.getAtomicSkillId())) {
                    
                    CareerSkillMapping mapping = new CareerSkillMapping();
                    mapping.setCareerSkillId(dto.getCareerSkillId());
                    mapping.setAtomicSkillId(dto.getAtomicSkillId());
                    mapping.setWeight(dto.getWeight());
                    mapping.setImportance(dto.getImportance());
                    mapping.setRequiredMasteryLevel(dto.getRequiredMasteryLevel());
                    mapping.setMappingReason(dto.getMappingReason());
                    mapping.setMappingSource(dto.getMappingSource());
                    mapping.setConfidenceScore(dto.getConfidenceScore());
                    mapping.setCreatedBy(dto.getCreatedBy());
                    
                    mappings.add(mapping);
                }
            }
            
            List<CareerSkillMapping> savedMappings = mappingRepository.saveAll(mappings);
            return ApiResponse.success(savedMappings);
            
        } catch (Exception e) {
            return ApiResponse.error("批量创建技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 自动生成技能映射
     */
    public ApiResponse<List<CareerSkillMapping>> generateAutomaticMappings(Long careerSkillId) {
        try {
            // 获取职业技能信息
            Optional<CareerSkill> optionalCareerSkill = careerSkillRepository.findById(careerSkillId);
            if (!optionalCareerSkill.isPresent()) {
                return ApiResponse.error("职业技能不存在");
            }
            
            CareerSkill careerSkill = optionalCareerSkill.get();
            List<CareerSkillMapping> generatedMappings = new ArrayList<>();
            
            // 基于技能名称和描述进行关键词匹配
            String skillName = careerSkill.getSkillName().toLowerCase();
            String description = careerSkill.getDescription() != null ? 
                    careerSkill.getDescription().toLowerCase() : "";
            
            // 查找相关的原子技能
            List<AtomicSkill> candidateSkills = atomicSkillRepository
                    .searchByKeyword(skillName);
            
            for (AtomicSkill atomicSkill : candidateSkills) {
                // 计算相似度和置信度
                BigDecimal confidence = calculateSimilarityScore(careerSkill, atomicSkill);
                
                if (confidence.compareTo(new BigDecimal("0.6")) >= 0) {
                    CareerSkillMapping mapping = new CareerSkillMapping();
                    mapping.setCareerSkillId(careerSkillId);
                    mapping.setAtomicSkillId(atomicSkill.getId());
                    mapping.setWeight(confidence);
                    mapping.setImportance(determineImportance(confidence));
                    mapping.setRequiredMasteryLevel(determineRequiredMasteryLevel(atomicSkill));
                    mapping.setMappingSource(CareerSkillMapping.MappingSource.AUTO_GENERATED);
                    mapping.setConfidenceScore(confidence);
                    mapping.setMappingReason("基于关键词匹配自动生成");
                    
                    generatedMappings.add(mapping);
                }
            }
            
            // 保存生成的映射
            List<CareerSkillMapping> savedMappings = mappingRepository.saveAll(generatedMappings);
            return ApiResponse.success(savedMappings);
            
        } catch (Exception e) {
            return ApiResponse.error("自动生成技能映射失败: " + e.getMessage());
        }
    }

    /**
     * 计算技能相似度分数
     */
    private BigDecimal calculateSimilarityScore(CareerSkill careerSkill, AtomicSkill atomicSkill) {
        // 简单的关键词匹配算法
        String careerSkillText = (careerSkill.getSkillName() + " " + 
                (careerSkill.getDescription() != null ? careerSkill.getDescription() : "")).toLowerCase();
        String atomicSkillText = (atomicSkill.getName() + " " + 
                (atomicSkill.getDescription() != null ? atomicSkill.getDescription() : "") + " " +
                (atomicSkill.getKeywords() != null ? atomicSkill.getKeywords() : "")).toLowerCase();
        
        // 计算共同关键词数量
        String[] careerWords = careerSkillText.split("\\s+");
        String[] atomicWords = atomicSkillText.split("\\s+");
        
        Set<String> careerWordSet = new HashSet<>(Arrays.asList(careerWords));
        Set<String> atomicWordSet = new HashSet<>(Arrays.asList(atomicWords));
        
        Set<String> intersection = new HashSet<>(careerWordSet);
        intersection.retainAll(atomicWordSet);
        
        Set<String> union = new HashSet<>(careerWordSet);
        union.addAll(atomicWordSet);
        
        if (union.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        double similarity = (double) intersection.size() / union.size();
        return BigDecimal.valueOf(similarity).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 根据置信度确定重要程度
     */
    private CareerSkillMapping.Importance determineImportance(BigDecimal confidence) {
        if (confidence.compareTo(new BigDecimal("0.8")) >= 0) {
            return CareerSkillMapping.Importance.CRITICAL;
        } else if (confidence.compareTo(new BigDecimal("0.6")) >= 0) {
            return CareerSkillMapping.Importance.IMPORTANT;
        } else {
            return CareerSkillMapping.Importance.NICE_TO_HAVE;
        }
    }

    /**
     * 根据原子技能确定要求掌握水平
     */
    private CareerSkillMapping.RequiredMasteryLevel determineRequiredMasteryLevel(AtomicSkill atomicSkill) {
        switch (atomicSkill.getDifficultyLevel()) {
            case BEGINNER:
                return CareerSkillMapping.RequiredMasteryLevel.BASIC;
            case INTERMEDIATE:
                return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
            case ADVANCED:
                return CareerSkillMapping.RequiredMasteryLevel.ADVANCED;
            case EXPERT:
                return CareerSkillMapping.RequiredMasteryLevel.EXPERT;
            default:
                return CareerSkillMapping.RequiredMasteryLevel.INTERMEDIATE;
        }
    }
}
