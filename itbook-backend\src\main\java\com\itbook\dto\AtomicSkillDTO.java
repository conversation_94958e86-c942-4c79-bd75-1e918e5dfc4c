package com.itbook.dto;

import com.itbook.entity.AtomicSkill;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 原子技能DTO
 * 用于API响应的数据传输对象
 */
public class AtomicSkillDTO {
    private Long id;
    private String skillCode;
    private String name;
    private String description;
    private String category;
    private String subcategory;
    private String difficultyLevel;
    private Integer estimatedHours;
    private String skillType;
    private String keywords;
    private Integer learnerCount;
    private Double completionRate;
    private Double averageRating;
    private String status;
    private String version;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联数据
    private List<String> tags;
    private List<String> prerequisites;
    private List<String> learningResources;

    public AtomicSkillDTO() {}

    public AtomicSkillDTO(AtomicSkill skill) {
        this.id = skill.getId();
        this.skillCode = skill.getSkillCode();
        this.name = skill.getName();
        this.description = skill.getDescription();
        this.category = skill.getCategory();
        this.subcategory = skill.getSubcategory();
        this.difficultyLevel = skill.getDifficultyLevel().name();
        this.estimatedHours = skill.getEstimatedHours();
        this.skillType = skill.getSkillType().name();
        this.keywords = skill.getKeywords();
        this.learnerCount = skill.getLearnerCount();
        this.completionRate = skill.getCompletionRate();
        this.averageRating = skill.getAverageRating();
        this.status = skill.getStatus().name();
        this.version = skill.getVersion();
        this.isActive = skill.getIsActive();
        this.createdAt = skill.getCreatedAt();
        this.updatedAt = skill.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getSkillCode() { return skillCode; }
    public void setSkillCode(String skillCode) { this.skillCode = skillCode; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getSubcategory() { return subcategory; }
    public void setSubcategory(String subcategory) { this.subcategory = subcategory; }

    public String getDifficultyLevel() { return difficultyLevel; }
    public void setDifficultyLevel(String difficultyLevel) { this.difficultyLevel = difficultyLevel; }

    public Integer getEstimatedHours() { return estimatedHours; }
    public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

    public String getSkillType() { return skillType; }
    public void setSkillType(String skillType) { this.skillType = skillType; }

    public String getKeywords() { return keywords; }
    public void setKeywords(String keywords) { this.keywords = keywords; }

    public Integer getLearnerCount() { return learnerCount; }
    public void setLearnerCount(Integer learnerCount) { this.learnerCount = learnerCount; }

    public Double getCompletionRate() { return completionRate; }
    public void setCompletionRate(Double completionRate) { this.completionRate = completionRate; }

    public Double getAverageRating() { return averageRating; }
    public void setAverageRating(Double averageRating) { this.averageRating = averageRating; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }

    public List<String> getPrerequisites() { return prerequisites; }
    public void setPrerequisites(List<String> prerequisites) { this.prerequisites = prerequisites; }

    public List<String> getLearningResources() { return learningResources; }
    public void setLearningResources(List<String> learningResources) { this.learningResources = learningResources; }
}
