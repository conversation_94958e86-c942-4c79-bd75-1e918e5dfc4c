import React, { useEffect } from 'react';
import { TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import {
  View,
  Text,
  Card,
} from 'react-native-ui-lib';

import { MainTabScreenProps } from '../navigation/types';
import { LearningProgressCard } from '../components/business';
import { useAuth } from '../hooks/useAuth';
import { LoginTest } from '../components/common/LoginTest';
import SmartRecommendationCard from '../components/recommendation/SmartRecommendationCard';
import { RecommendationContext } from '../types/recommendation';
import { useThemeColors } from '../contexts/ThemeContext';
import { useCareer, useCareerRecommendations } from '../hooks/useCareer';
import JobProgressCard from '../components/career/JobProgressCard';
import JobRecommendationCard from '../components/career/JobRecommendationCard';
import { useDispatch } from 'react-redux';
import { setCareerGoal, fetchCareerProgress } from '../store/slices/unifiedProgressSlice';

type Props = MainTabScreenProps<'Home'>;

export const HomeScreen: React.FC<Props> = ({ navigation, route }) => {
  // 调试日志：检查route参数
  console.log('HomeScreen: route.params:', route?.params);

  // 使用Redux状态管理
  const { isAuthenticated, user, loadStoredAuth } = useAuth();
  const dispatch = useDispatch();
  // 使用主题颜色
  const colors = useThemeColors();

  // 使用职业目标相关Hook
  const {
    hasCareerSelected,
    loadCareerGoals,
    loadUserCareerData,
    selectCareerGoal,
  } = useCareer();



  // 使用职业目标推荐Hook - 只有登录用户才能获取推荐
  const {
    recommendations,
    isLoading: isRecommendationsLoading,
    refresh: refreshRecommendations
  } = useCareerRecommendations(user?.id);

  // 移除重复的loadStoredAuth调用，认证状态由App.tsx统一管理

  // 处理职业目标选择完成的参数
  useEffect(() => {
    const params = route.params as any;
    if (params?.jobSelected === 'true' && params?.selectedJobId && params?.selectedLevel) {
      if (!user?.id) {
        console.warn('HomeScreen: 用户未登录，无法保存职业目标选择');
        return;
      }
      console.log('HomeScreen: 检测到职业目标选择完成', params.selectedJobId, params.selectedJobName, params.selectedLevel);
      console.log('HomeScreen: 使用用户ID:', user.id);

      // 处理职业目标选择
      const handleJobSelection = async () => {
        if (!user?.id) {
          console.error('HomeScreen: 用户未登录，无法保存职业目标选择');
          return;
        }

        try {
          console.log('HomeScreen: 开始保存职业目标选择');
          await selectCareerGoal(user.id, params.selectedJobId, params.selectedLevel);
          console.log('HomeScreen: 职业目标选择保存完成');

          // 刷新推荐内容
          refreshRecommendations();
          console.log('HomeScreen: 职业目标选择已保存:', params.selectedJobName, params.selectedLevel);

          // 重新加载用户职业数据
          await loadUserCareerData(user.id);

          // 同时保存到统一进度体系
          console.log('HomeScreen: 开始同步到统一进度体系，用户ID:', user.id);

          // 构建职业目标数据
          const goalData = {
            targetJobId: params.selectedJobId,
            targetLevel: params.selectedLevel.toUpperCase() as 'JUNIOR' | 'MID' | 'SENIOR',
            description: `目标成为${params.selectedJobName}`,
            motivation: '通过系统学习提升技能'
          };

          try {
            console.log('HomeScreen: 开始同步到统一进度体系:', goalData);

            // 调用统一进度API
            await dispatch(setCareerGoal({ userId: user.id, goalData }) as any);
            console.log('HomeScreen: 统一进度体系职业目标设置成功');

            // 刷新统一进度数据
            await dispatch(fetchCareerProgress(user.id) as any);
            console.log('HomeScreen: 统一进度数据刷新成功');
          } catch (error) {
            console.error('HomeScreen: 统一进度体系同步失败:', error);
          }
        } catch (error) {
          console.error('HomeScreen: 保存职业目标选择失败:', error);
        }
      };

      handleJobSelection();

      // 清除参数，避免重复处理
      navigation.setParams({
        jobSelected: undefined,
        selectedJobId: undefined,
        selectedJobName: undefined,
        selectedLevel: undefined
      });
    }
  }, [route.params, user?.id, selectCareerGoal, refreshRecommendations, loadUserCareerData, navigation]);

  useEffect(() => {
    // 加载职业目标数据
    loadCareerGoals();
  }, [loadCareerGoals]);

  useEffect(() => {
    // 用户登录后加载职业数据
    if (user?.id) {
      console.log('HomeScreen: 加载用户职业数据，用户ID:', user.id);
      loadUserCareerData(user.id);
    }
  }, [user?.id, loadUserCareerData]);
  const handleNavigateToStudy = () => {
    navigation.navigate('Study');
  };

  const handleNavigateToJobs = () => {
    navigation.navigate('Jobs');
  };

  const handleNavigateToDiscover = () => {
    navigation.navigate('Discover');
  };

  const handleStartLearning = (pathId: string) => {
    // TODO: 导航到具体学习路径
    console.log('开始学习路径:', pathId);
  };

  const handleOpenCodeEditor = () => {
    navigation.navigate('CodeEditor', {
      title: '在线代码编辑器',
      initialCode: '// 欢迎使用ITBook在线代码编辑器\nconsole.log("Hello, ITBook!");\n\n// 这是一个功能强大的代码编辑器\n// 支持多种编程语言\n// 支持实时代码运行\n\nfunction fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}\n\nconsole.log("斐波那契数列前10项:");\nfor (let i = 0; i < 10; i++) {\n  console.log(`F(${i}) = ${fibonacci(i)}`);\n}',
      language: 'javascript'
    });
  };

  const handleNavigateToInterview = () => {
    navigation.navigate('InterviewPractice');
  };

  // 职业目标选择完成的处理函数
  const handleJobSelected = async (job: any, level: any) => {
    // 检查用户登录状态
    if (!user?.id) {
      console.warn('HomeScreen: 用户未登录，无法保存职业目标选择');
      // TODO: 显示登录提示或导航到登录页面
      return;
    }

    try {
      console.log('HomeScreen: 开始保存职业目标选择:', job, level);
      console.log('HomeScreen: 使用用户ID:', user.id);

      // 1. 保存职业目标选择
      await selectCareerGoal(user.id, job.id, level);
      console.log('HomeScreen: 职业目标选择保存完成');

      // 继续执行后续操作
      // 刷新推荐内容
      refreshRecommendations();
      console.log('HomeScreen: 职业目标选择已保存:', job.name, level);

      // 重新加载用户职业数据
      if (user?.id) {
        await loadUserCareerData(user.id);

        // 2. 同时保存到统一进度体系
        console.log(`HomeScreen: 同步到统一进度体系，用户ID: ${user.id}`);

        const goalData = {
          targetJobId: job.id,
          targetLevel: level.toUpperCase() as 'JUNIOR' | 'MID' | 'SENIOR',
          description: `目标成为${job.name}`,
          motivation: '通过系统学习提升技能'
        };

        try {
          console.log('HomeScreen: 开始同步到统一进度体系:', goalData);

          // 调用统一进度API
          await dispatch(setCareerGoal({ userId: user.id, goalData }) as any);
          console.log('HomeScreen: 统一进度体系职业目标设置成功');

          // 刷新统一进度数据
          await dispatch(fetchCareerProgress(user.id) as any);
          console.log('HomeScreen: 统一进度数据刷新成功');
        } catch (error) {
          console.error('HomeScreen: 统一进度体系同步失败:', error);
        }
      }
    } catch (error) {
      console.error('HomeScreen: 保存职业目标选择失败:', error);
    }
  };

  // 职业目标相关处理函数
  const handleSetJobTarget = () => {
    if (!isAuthenticated) {
      // 未登录用户引导到登录页面
      navigation.navigate('Auth');
      return;
    }
    navigation.navigate('JobSelection');
  };

  const handleViewJobProgress = () => {
    if (!isAuthenticated) {
      // 未登录用户引导到登录页面
      navigation.navigate('Auth');
      return;
    }
    navigation.navigate('CareerManagement');
  };

  const handleViewRecommendations = () => {
    // TODO: 导航到推荐页面或展开推荐列表
    console.log('查看推荐内容');
  };

  const handleRecommendationItemPress = (item: any) => {
    // 根据推荐内容类型导航到相应页面
    switch (item.type) {
      case 'course':
        navigation.navigate('Study');
        break;
      case 'article':
        navigation.navigate('Discover');
        break;
      case 'project':
        // TODO: 导航到项目页面
        console.log('打开项目:', item.title);
        break;
      case 'interview-question':
        navigation.navigate('InterviewPractice');
        break;
      default:
        console.log('打开推荐内容:', item.title);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <View flex backgroundColor={colors.background}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View row spread centerV padding-16>
            <View flex>
              <Text text18 $textSemiBold color={colors.text}>
                {isAuthenticated ? `你好，${user?.nickname || user?.username || '用户'}！` : '欢迎体验！'}
              </Text>
              <Text text14 color={colors.textSecondary} marginT-4>
                {isAuthenticated ? '继续你的学习之旅' : 'ITBook学习平台 - 免费体验'}
              </Text>
            </View>
            <TouchableOpacity onPress={() => navigation.navigate('Profile')}>
              <Ionicons
                name={isAuthenticated ? "person-circle" : "person-circle-outline"}
                size={32}
                color={colors.primary}
              />
            </TouchableOpacity>
          </View>

          {/* 搜索栏 */}
          <TouchableOpacity onPress={handleNavigateToDiscover}>
            <Card
              row
              centerV
              padding-16
              margin-16
              marginB-16
              borderRadius={8}
              elevation={2}
              backgroundColor={colors.surface}
            >
              <Ionicons name="search" size={20} color={colors.textSecondary} />
              <Text text16 color={colors.textSecondary} marginL-8>
                搜索课程、技术问题...
              </Text>
            </Card>
          </TouchableOpacity>

          {/* 登录测试组件 */}
          <LoginTest />

          {/* 未登录用户提示 */}
          {!isAuthenticated && (
            <View paddingH-16 marginB-16>
              <Card
                padding-16
                borderRadius={8}
                elevation={2}
                backgroundColor={colors.surface}
              >
                <View row centerV marginB-12>
                  <Ionicons name="person-add" size={24} color={colors.primary} />
                  <Text text16 $textSemiBold color={colors.text} marginL-8>
                    登录解锁更多功能
                  </Text>
                </View>
                <Text text14 color={colors.textSecondary} marginB-16>
                  登录后可以保存学习进度、设置职业目标、获取个性化推荐
                </Text>
                <TouchableOpacity
                  onPress={() => navigation.navigate('Auth')}
                  style={{
                    backgroundColor: colors.primary,
                    borderRadius: 6,
                    paddingVertical: 12,
                    paddingHorizontal: 24,
                    alignItems: 'center',
                  }}
                >
                  <Text text14 $textSemiBold color={colors.onPrimary}>
                    立即登录
                  </Text>
                </TouchableOpacity>
              </Card>
            </View>
          )}

          {/* 学习进度卡片 */}
          <View paddingH-16>
            <LearningProgressCard
              onContinueLearning={() => handleStartLearning('java-junior')}
              onViewAllCourses={handleNavigateToStudy}
            />
          </View>

          {/* 职业目标进度卡片 - 仅在用户登录时显示 */}
          {user && (
            <JobProgressCard
              userId={user.id}
              onSetJobTarget={handleSetJobTarget}
              onViewProgress={handleViewJobProgress}
              onViewRecommendations={handleViewRecommendations}
            />
          )}

          {/* 快速入口 */}
          <View paddingH-16 marginB-24>
            <Text text18 $textSemiBold color={colors.text} marginB-16>
              快速入口
            </Text>
            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={handleNavigateToStudy}
              >
                <Ionicons name="book" size={24} color={colors.primary} />
                <Text text14 color={colors.text} marginT-8 center>
                  我的学习
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={handleNavigateToJobs}
              >
                <Ionicons name="briefcase" size={24} color={colors.secondary} />
                <Text text14 color={colors.text} marginT-8 center>
                  求职
                </Text>
              </TouchableOpacity>
            </View>

            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={handleNavigateToDiscover}
              >
                <Ionicons name="compass" size={24} color={colors.success} />
                <Text text14 color={colors.text} marginT-8 center>
                  发现
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={handleNavigateToInterview}
              >
                <Ionicons name="school" size={24} color={colors.warning} />
                <Text text14 color={colors.text} marginT-8 center>
                  面试题练习
                </Text>
              </TouchableOpacity>
            </View>

            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={handleOpenCodeEditor}
              >
                <Ionicons name="code-slash" size={24} color={colors.accent} />
                <Text text14 color={colors.text} marginT-8 center>
                  代码编辑器
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('SkillAssessment')}
              >
                <Ionicons name="analytics" size={24} color={colors.warning} />
                <Text text14 color={colors.text} marginT-8 center>
                  技能评估
                </Text>
              </TouchableOpacity>
            </View>

            {/* 新增功能入口 - 第三阶段职业目标专属功能 */}
            <Text text18 $textSemiBold color={colors.text} marginB-16 marginT-16>
              🚀 职业目标专属功能
            </Text>

            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('ProjectWorkshop')}
              >
                <Ionicons name="hammer" size={24} color={colors.primary} />
                <Text text14 color={colors.text} marginT-8 center>
                  项目实战
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('InterviewPreparation')}
              >
                <Ionicons name="school" size={24} color={colors.secondary} />
                <Text text14 color={colors.text} marginT-8 center>
                  面试准备
                </Text>
              </TouchableOpacity>
            </View>

            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('PortfolioGeneration')}
              >
                <Ionicons name="document-text" size={24} color={colors.success} />
                <Text text14 color={colors.text} marginT-8 center>
                  作品集生成
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('JobRecommendations')}
              >
                <Ionicons name="search" size={24} color={colors.accent} />
                <Text text14 color={colors.text} marginT-8 center>
                  职业推荐
                </Text>
              </TouchableOpacity>
            </View>

            <View row spread>
              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('CodeEditor', {
                  title: '在线代码编辑器',
                  initialCode: '// 欢迎使用ITBook在线代码编辑器\nconsole.log("Hello, ITBook!");'
                })}
              >
                <Ionicons name="code-working" size={24} color={colors.warning} />
                <Text text14 color={colors.text} marginT-8 center>
                  在线编程
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  width: '48%',
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  padding: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                  elevation: 2,
                }}
                onPress={() => navigation.navigate('AtomicSkillList')}
              >
                <Ionicons name="library" size={24} color={colors.info} />
                <Text text14 color={colors.text} marginT-8 center>
                  原子技能
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* 职业推荐 - 仅在用户有职业目标时显示 */}
          {user && hasCareerSelected && (
            <JobRecommendationCard
              recommendations={recommendations}
              isLoading={isRecommendationsLoading}
              onRefresh={refreshRecommendations}
              onItemPress={handleRecommendationItemPress}
              onViewAll={handleViewRecommendations}
            />
          )}

          {/* 个性化路径 */}
          {user && (
            <SmartRecommendationCard
              userId={user.id}
              context={RecommendationContext.HOME_PAGE}
              title="🎯 个性化路径"
              maxItems={5}
              navigation={navigation}
            />
          )}

          {/* 热门内容 */}
          <View paddingH-16 marginB-32>
            <Text text18 $textSemiBold color={colors.text} marginB-16>
              🔥 热门内容
            </Text>
            <View>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.border,
                }}
              >
                <Ionicons name="play-circle" size={20} color={colors.primary} />
                <Text flex text16 color={colors.text} marginL-8>
                  Redis实战教程
                </Text>
                <Text text14 color={colors.textSecondary}>
                  1.2k观看
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.border,
                }}
                onPress={() => navigation.navigate('ProjectShowcase')}
              >
                <Ionicons name="briefcase" size={20} color={colors.accent} />
                <Text flex text16 color={colors.text} marginL-8>
                  优秀项目作品
                </Text>
                <Text text14 color={colors.textSecondary}>
                  1.5k浏览
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.border,
                }}
              >
                <Ionicons name="code-slash" size={20} color={colors.warning} />
                <Text flex text16 color={colors.text} marginL-8>
                  算法与数据结构
                </Text>
                <Text text14 color={colors.textSecondary}>
                  2.1k学习
                </Text>
              </TouchableOpacity>

              {/* 学习路径API测试按钮 */}
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  backgroundColor: colors.primary + '20',
                  borderRadius: 8,
                  marginTop: 8,
                }}
                onPress={() => navigation.navigate('LearningPathTest')}
              >
                <Ionicons name="flask" size={20} color={colors.primary} />
                <Text flex text16 color={colors.primary} marginL-8>
                  🧪 学习路径API测试
                </Text>
                <Ionicons name="chevron-forward" size={16} color={colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};


