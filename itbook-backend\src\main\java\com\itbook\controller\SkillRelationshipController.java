package com.itbook.controller;

import com.itbook.entity.SkillRelationship;
import com.itbook.service.SkillRelationshipService;
import com.itbook.dto.SkillRelationshipDTO;
import com.itbook.dto.SkillGraphDTO;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 技能关系控制器
 * 提供技能关系图谱相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/skill-relationships")
@Tag(name = "技能关系管理", description = "技能关系图谱的管理、查询、分析等API")
public class SkillRelationshipController {

    @Autowired
    private SkillRelationshipService relationshipService;

    /**
     * 创建技能关系
     */
    @PostMapping
    @Operation(summary = "创建技能关系", description = "创建新的技能关系")
    public ApiResponse<SkillRelationship> createRelationship(@Valid @RequestBody SkillRelationshipDTO relationshipDTO) {
        return relationshipService.createRelationship(relationshipDTO);
    }

    /**
     * 更新技能关系
     */
    @PutMapping("/{relationshipId}")
    @Operation(summary = "更新技能关系", description = "更新指定ID的技能关系信息")
    public ApiResponse<SkillRelationship> updateRelationship(
            @Parameter(description = "关系ID") @PathVariable Long relationshipId,
            @Valid @RequestBody SkillRelationshipDTO relationshipDTO) {
        return relationshipService.updateRelationship(relationshipId, relationshipDTO);
    }

    /**
     * 删除技能关系
     */
    @DeleteMapping("/{relationshipId}")
    @Operation(summary = "删除技能关系", description = "删除指定ID的技能关系")
    public ApiResponse<Void> deleteRelationship(
            @Parameter(description = "关系ID") @PathVariable Long relationshipId) {
        return relationshipService.deleteRelationship(relationshipId);
    }

    /**
     * 根据ID查询技能关系
     */
    @GetMapping("/{relationshipId}")
    @Operation(summary = "查询技能关系", description = "根据ID查询技能关系详情")
    public ApiResponse<SkillRelationship> getRelationshipById(
            @Parameter(description = "关系ID") @PathVariable Long relationshipId) {
        return relationshipService.getRelationshipById(relationshipId);
    }

    /**
     * 查询技能的所有关系
     */
    @GetMapping("/skill/{skillId}")
    @Operation(summary = "查询技能关系", description = "查询指定技能的所有关系")
    public ApiResponse<List<SkillRelationship>> getSkillRelationships(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getSkillRelationships(skillId);
    }

    /**
     * 查询技能的前置技能
     */
    @GetMapping("/skill/{skillId}/prerequisites")
    @Operation(summary = "查询前置技能", description = "查询指定技能的前置技能关系")
    public ApiResponse<List<SkillRelationship>> getPrerequisiteSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getPrerequisiteSkills(skillId);
    }

    /**
     * 查询技能的后续技能
     */
    @GetMapping("/skill/{skillId}/successors")
    @Operation(summary = "查询后续技能", description = "查询指定技能的后续技能关系")
    public ApiResponse<List<SkillRelationship>> getSuccessorSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getSuccessorSkills(skillId);
    }

    /**
     * 查询技能的相关技能
     */
    @GetMapping("/skill/{skillId}/related")
    @Operation(summary = "查询相关技能", description = "查询指定技能的相关技能关系")
    public ApiResponse<List<SkillRelationship>> getRelatedSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getRelatedSkills(skillId);
    }

    /**
     * 查询技能的并行技能
     */
    @GetMapping("/skill/{skillId}/corequisites")
    @Operation(summary = "查询并行技能", description = "查询指定技能的并行技能关系")
    public ApiResponse<List<SkillRelationship>> getCorequisiteSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getCorequisiteSkills(skillId);
    }

    /**
     * 查询技能的替代技能
     */
    @GetMapping("/skill/{skillId}/alternatives")
    @Operation(summary = "查询替代技能", description = "查询指定技能的替代技能关系")
    public ApiResponse<List<SkillRelationship>> getAlternativeSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return relationshipService.getAlternativeSkills(skillId);
    }

    /**
     * 构建技能学习路径
     */
    @GetMapping("/skill/{skillId}/learning-path")
    @Operation(summary = "构建学习路径", description = "为指定技能构建学习路径")
    public ApiResponse<List<Long>> buildLearningPath(
            @Parameter(description = "目标技能ID") @PathVariable Long skillId) {
        return relationshipService.buildLearningPath(skillId);
    }

    /**
     * 获取技能图谱数据
     */
    @PostMapping("/graph")
    @Operation(summary = "获取技能图谱", description = "获取指定技能集合的图谱数据")
    public ApiResponse<SkillGraphDTO> getSkillGraph(@RequestBody List<Long> skillIds) {
        return relationshipService.getSkillGraph(skillIds);
    }

    /**
     * 获取关系统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取关系统计", description = "获取技能关系的统计信息")
    public ApiResponse<Map<String, Object>> getRelationshipStatistics() {
        return relationshipService.getRelationshipStatistics();
    }

    /**
     * 批量创建技能关系
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建关系", description = "批量创建技能关系")
    public ApiResponse<List<SkillRelationship>> createRelationshipsBatch(
            @Valid @RequestBody List<SkillRelationshipDTO> relationshipDTOs) {
        return relationshipService.createRelationshipsBatch(relationshipDTOs);
    }

    /**
     * 验证技能关系的一致性
     */
    @GetMapping("/validate")
    @Operation(summary = "验证关系一致性", description = "验证技能关系图谱的一致性")
    public ApiResponse<Map<String, Object>> validateRelationshipConsistency() {
        return relationshipService.validateRelationshipConsistency();
    }
}
