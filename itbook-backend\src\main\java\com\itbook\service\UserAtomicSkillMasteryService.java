package com.itbook.service;

import com.itbook.entity.UserAtomicSkillMastery;
import com.itbook.entity.AtomicSkill;
import com.itbook.repository.UserAtomicSkillMasteryRepository;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.dto.UserSkillMasteryDTO;
import com.itbook.dto.SkillMasteryAnalysisDTO;
import com.itbook.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户原子技能掌握度服务类
 * 提供用户技能掌握情况的管理、分析、推荐等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Service
@Transactional
public class UserAtomicSkillMasteryService {

    @Autowired
    private UserAtomicSkillMasteryRepository masteryRepository;

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    /**
     * 更新用户技能掌握度
     */
    public ApiResponse<UserAtomicSkillMastery> updateMastery(Long userId, Long atomicSkillId, UserSkillMasteryDTO masteryDTO) {
        try {
            // 验证技能是否存在
            if (!atomicSkillRepository.existsById(atomicSkillId)) {
                return ApiResponse.error("技能不存在: " + atomicSkillId);
            }

            // 查找或创建掌握记录
            Optional<UserAtomicSkillMastery> optionalMastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, atomicSkillId);
            
            UserAtomicSkillMastery mastery;
            if (optionalMastery.isPresent()) {
                mastery = optionalMastery.get();
            } else {
                mastery = new UserAtomicSkillMastery();
                mastery.setUserId(userId);
                mastery.setAtomicSkillId(atomicSkillId);
            }

            // 更新掌握度信息
            mastery.setMasteryLevel(masteryDTO.getMasteryLevel());
            mastery.setMasteryScore(masteryDTO.getMasteryScore());
            mastery.setConfidenceLevel(masteryDTO.getConfidenceLevel());
            mastery.setIsCertified(masteryDTO.getIsCertified());
            mastery.setLearnedViaPathId(masteryDTO.getLearnedViaPathId());
            
            // 计算衰减因子
            mastery.setDecayFactor(calculateDecayFactor(mastery));
            
            // 判断是否需要复习
            mastery.setNeedsRefresh(mastery.getDecayFactor().compareTo(new BigDecimal("0.7")) < 0);

            UserAtomicSkillMastery savedMastery = masteryRepository.save(mastery);
            return ApiResponse.success(savedMastery);

        } catch (Exception e) {
            return ApiResponse.error("更新技能掌握度失败: " + e.getMessage());
        }
    }

    /**
     * 记录学习活动
     */
    public ApiResponse<UserAtomicSkillMastery> recordLearningActivity(Long userId, Long atomicSkillId, 
                                                                     BigDecimal learningHours, String activityType) {
        try {
            Optional<UserAtomicSkillMastery> optionalMastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, atomicSkillId);
            
            UserAtomicSkillMastery mastery;
            if (optionalMastery.isPresent()) {
                mastery = optionalMastery.get();
            } else {
                mastery = new UserAtomicSkillMastery();
                mastery.setUserId(userId);
                mastery.setAtomicSkillId(atomicSkillId);
                mastery.setMasteryLevel(UserAtomicSkillMastery.MasteryLevel.NONE);
                mastery.setMasteryScore(BigDecimal.ZERO);
                mastery.setLearningHours(BigDecimal.ZERO);
                mastery.setPracticeCount(0);
            }

            // 更新学习时长
            if (mastery.getLearningHours() == null) {
                mastery.setLearningHours(learningHours);
            } else {
                mastery.setLearningHours(mastery.getLearningHours().add(learningHours));
            }

            // 更新练习次数和时间
            if ("practice".equals(activityType)) {
                mastery.setPracticeCount(mastery.getPracticeCount() + 1);
                mastery.setLastPracticedAt(LocalDateTime.now());
            }

            // 重新计算衰减因子
            mastery.setDecayFactor(calculateDecayFactor(mastery));

            UserAtomicSkillMastery savedMastery = masteryRepository.save(mastery);
            return ApiResponse.success(savedMastery);

        } catch (Exception e) {
            return ApiResponse.error("记录学习活动失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户技能掌握情况
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<UserAtomicSkillMastery>> getUserMasteries(Long userId) {
        try {
            List<UserAtomicSkillMastery> masteries = masteryRepository.findByUserId(userId);
            return ApiResponse.success(masteries);
        } catch (Exception e) {
            return ApiResponse.error("获取用户技能掌握情况失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户技能掌握分析
     */
    @Transactional(readOnly = true)
    public ApiResponse<SkillMasteryAnalysisDTO> getUserMasteryAnalysis(Long userId) {
        try {
            SkillMasteryAnalysisDTO analysis = new SkillMasteryAnalysisDTO();
            
            // 基础统计
            List<Object[]> masteryStats = masteryRepository.getUserMasteryStatistics(userId);
            Map<String, Long> masteryLevelCounts = masteryStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));
            analysis.setMasteryLevelStatistics(masteryLevelCounts);
            
            // 平均掌握分数
            BigDecimal avgScore = masteryRepository.getUserAverageMasteryScore(userId);
            analysis.setAverageMasteryScore(avgScore != null ? avgScore : BigDecimal.ZERO);
            
            // 掌握技能总数
            Long masteredCount = masteryRepository.getUserMasteredSkillCount(userId);
            analysis.setMasteredSkillCount(masteredCount != null ? masteredCount : 0L);
            
            // 总学习时长
            BigDecimal totalHours = masteryRepository.getUserTotalLearningHours(userId);
            analysis.setTotalLearningHours(totalHours != null ? totalHours : BigDecimal.ZERO);
            
            // 按分类统计
            List<Object[]> categoryStats = masteryRepository.getUserMasteryByCategory(userId);
            Map<String, Map<String, Object>> categoryAnalysis = categoryStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> {
                                Map<String, Object> categoryData = new HashMap<>();
                                categoryData.put("averageScore", row[1]);
                                categoryData.put("skillCount", row[2]);
                                return categoryData;
                            }
                    ));
            analysis.setCategoryAnalysis(categoryAnalysis);
            
            // 按难度统计
            List<Object[]> difficultyStats = masteryRepository.getUserMasteryByDifficulty(userId);
            Map<String, Map<String, Object>> difficultyAnalysis = difficultyStats.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> {
                                Map<String, Object> difficultyData = new HashMap<>();
                                difficultyData.put("averageScore", row[1]);
                                difficultyData.put("skillCount", row[2]);
                                return difficultyData;
                            }
                    ));
            analysis.setDifficultyAnalysis(difficultyAnalysis);
            
            // 强项分类
            List<String> strongCategories = masteryRepository.getUserStrongCategories(
                    userId, new BigDecimal("80"), 3L);
            analysis.setStrongCategories(strongCategories);
            
            // 弱项分类
            List<String> weakCategories = masteryRepository.getUserWeakCategories(
                    userId, new BigDecimal("60"), 2L);
            analysis.setWeakCategories(weakCategories);
            
            return ApiResponse.success(analysis);
            
        } catch (Exception e) {
            return ApiResponse.error("获取用户技能分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取需要复习的技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<UserAtomicSkillMastery>> getSkillsNeedingRefresh(Long userId) {
        try {
            List<UserAtomicSkillMastery> skillsNeedingRefresh = masteryRepository
                    .getSkillsNeedingRefresh(userId, new BigDecimal("0.7"));
            return ApiResponse.success(skillsNeedingRefresh);
        } catch (Exception e) {
            return ApiResponse.error("获取需要复习的技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户最近练习的技能
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<UserAtomicSkillMastery>> getRecentPracticedSkills(Long userId, int limit) {
        try {
            List<UserAtomicSkillMastery> recentSkills = masteryRepository
                    .getUserRecentPracticedSkills(userId);
            
            // 限制返回数量
            if (recentSkills.size() > limit) {
                recentSkills = recentSkills.subList(0, limit);
            }
            
            return ApiResponse.success(recentSkills);
        } catch (Exception e) {
            return ApiResponse.error("获取最近练习技能失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新技能掌握度
     */
    public ApiResponse<Integer> updateMasteriesBatch(Long userId, Map<Long, UserSkillMasteryDTO> masteryUpdates) {
        try {
            int updateCount = 0;
            
            for (Map.Entry<Long, UserSkillMasteryDTO> entry : masteryUpdates.entrySet()) {
                Long skillId = entry.getKey();
                UserSkillMasteryDTO masteryDTO = entry.getValue();
                
                ApiResponse<UserAtomicSkillMastery> result = updateMastery(userId, skillId, masteryDTO);
                if (result.getCode() == 20000) {
                    updateCount++;
                }
            }
            
            return ApiResponse.success(updateCount);
            
        } catch (Exception e) {
            return ApiResponse.error("批量更新技能掌握度失败: " + e.getMessage());
        }
    }

    /**
     * 计算技能推荐
     */
    @Transactional(readOnly = true)
    public ApiResponse<List<AtomicSkill>> getRecommendedSkills(Long userId, int limit) {
        try {
            // 获取用户已掌握的技能
            List<UserAtomicSkillMastery> userMasteries = masteryRepository.findByUserId(userId);
            Set<Long> masteredSkillIds = userMasteries.stream()
                    .filter(m -> m.getMasteryLevel() != UserAtomicSkillMastery.MasteryLevel.NONE)
                    .map(UserAtomicSkillMastery::getAtomicSkillId)
                    .collect(Collectors.toSet());
            
            // 获取用户强项分类
            List<String> strongCategories = masteryRepository.getUserStrongCategories(
                    userId, new BigDecimal("70"), 2L);
            
            // 基于强项分类推荐技能
            List<AtomicSkill> recommendedSkills = new ArrayList<>();
            
            for (String category : strongCategories) {
                List<AtomicSkill> categorySkills = atomicSkillRepository.findByCategory(category);
                categorySkills = categorySkills.stream()
                        .filter(skill -> !masteredSkillIds.contains(skill.getId()))
                        .filter(skill -> skill.getStatus() == AtomicSkill.Status.PUBLISHED)
                        .sorted((s1, s2) -> s2.getAverageRating().compareTo(s1.getAverageRating()))
                        .collect(Collectors.toList());
                
                recommendedSkills.addAll(categorySkills);
                
                if (recommendedSkills.size() >= limit) {
                    break;
                }
            }
            
            // 限制返回数量
            if (recommendedSkills.size() > limit) {
                recommendedSkills = recommendedSkills.subList(0, limit);
            }
            
            return ApiResponse.success(recommendedSkills);
            
        } catch (Exception e) {
            return ApiResponse.error("获取推荐技能失败: " + e.getMessage());
        }
    }

    /**
     * 计算衰减因子
     */
    private BigDecimal calculateDecayFactor(UserAtomicSkillMastery mastery) {
        if (mastery.getLastPracticedAt() == null) {
            return BigDecimal.ONE;
        }
        
        // 计算距离上次练习的天数
        long daysSinceLastPractice = java.time.temporal.ChronoUnit.DAYS.between(
                mastery.getLastPracticedAt().toLocalDate(),
                LocalDateTime.now().toLocalDate()
        );
        
        // 使用指数衰减模型：decay = e^(-λt)
        // λ = 0.1 (衰减常数)，t = 天数
        double lambda = 0.1;
        double decayValue = Math.exp(-lambda * daysSinceLastPractice);
        
        return BigDecimal.valueOf(decayValue).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 删除用户技能掌握记录
     */
    public ApiResponse<Void> deleteMastery(Long userId, Long atomicSkillId) {
        try {
            Optional<UserAtomicSkillMastery> mastery = masteryRepository
                    .findByUserIdAndAtomicSkillId(userId, atomicSkillId);
            
            if (mastery.isPresent()) {
                masteryRepository.delete(mastery.get());
                return ApiResponse.success(null);
            } else {
                return ApiResponse.error("技能掌握记录不存在");
            }
            
        } catch (Exception e) {
            return ApiResponse.error("删除技能掌握记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能的掌握统计
     */
    @Transactional(readOnly = true)
    public ApiResponse<Map<String, Object>> getSkillMasteryStatistics(Long atomicSkillId) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 平均掌握分数
            BigDecimal avgScore = masteryRepository.getSkillAverageMasteryScore(atomicSkillId);
            statistics.put("averageMasteryScore", avgScore != null ? avgScore : BigDecimal.ZERO);
            
            // 掌握人数
            Long masteryCount = masteryRepository.getSkillMasteryCount(atomicSkillId);
            statistics.put("masteryCount", masteryCount != null ? masteryCount : 0L);
            
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            return ApiResponse.error("获取技能掌握统计失败: " + e.getMessage());
        }
    }
}
