import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { 
  AtomicSkill, 
  AtomicSkillUtils,
  DifficultyLevel, 
  SkillType, 
  MasteryLevel,
  UserAtomicSkillMastery 
} from '../../types/atomicSkill';
import { AtomicSkillService } from '../../services/AtomicSkillService';

type RootStackParamList = {
  AtomicSkillDetail: { skillId: number };
};

type AtomicSkillDetailScreenRouteProp = RouteProp<RootStackParamList, 'AtomicSkillDetail'>;
type AtomicSkillDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AtomicSkillDetail'>;

/**
 * 原子技能详情页面
 * 展示技能的完整信息、掌握度、学习资源等
 */
export const AtomicSkillDetailScreen: React.FC = () => {
  const colors = useThemeColors();
  const route = useRoute<AtomicSkillDetailScreenRouteProp>();
  const navigation = useNavigation<AtomicSkillDetailScreenNavigationProp>();
  
  const { skillId } = route.params;
  
  const [skill, setSkill] = useState<AtomicSkill | null>(null);
  const [mastery, setMastery] = useState<UserAtomicSkillMastery | null>(null);
  const [prerequisites, setPrerequisites] = useState<AtomicSkill[]>([]);
  const [relatedSkills, setRelatedSkills] = useState<AtomicSkill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载技能详情
  const loadSkillDetail = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 加载技能详情
      const skillData = await AtomicSkillService.getAtomicSkill(skillId);
      setSkill(skillData);

      // TODO: 实现前置技能和相关技能API后启用
      // const [prerequisitesData, relatedData] = await Promise.all([
      //   AtomicSkillService.getPrerequisiteSkills(skillId),
      //   AtomicSkillService.getRelatedSkills(skillId),
      // ]);
      // setPrerequisites(prerequisitesData);
      // setRelatedSkills(relatedData);
      
      // TODO: 加载用户掌握度数据
      // const masteryData = await AtomicSkillService.getUserSkillMastery(userId, skillId);
      // setMastery(masteryData);
      
    } catch (err) {
      console.error('Failed to load skill detail:', err);
      setError('加载技能详情失败');
    } finally {
      setLoading(false);
    }
  }, [skillId]);

  useEffect(() => {
    loadSkillDetail();
  }, [loadSkillDetail]);

  // 处理开始学习
  const handleStartLearning = useCallback(() => {
    Alert.alert(
      '开始学习',
      `确定要开始学习"${skill?.name}"吗？`,
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '开始', 
          onPress: () => {
            // TODO: 导航到学习页面或记录学习活动
            console.log('Start learning skill:', skill?.id);
          }
        },
      ]
    );
  }, [skill]);

  // 处理技能评估
  const handleAssessment = useCallback(() => {
    Alert.alert(
      '技能评估',
      `开始"${skill?.name}"的技能评估？`,
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '开始评估', 
          onPress: () => {
            // TODO: 导航到评估页面
            console.log('Start assessment for skill:', skill?.id);
          }
        },
      ]
    );
  }, [skill]);

  // 渲染加载状态
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          加载中...
        </Text>
      </View>
    );
  }

  // 渲染错误状态
  if (error || !skill) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: colors.background }]}>
        <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error || '技能不存在'}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={loadSkillDetail}
          activeOpacity={0.7}
        >
          <Text style={[styles.retryButtonText, { color: colors.surface }]}>
            重试
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 技能基本信息 */}
        <View style={[styles.headerCard, { backgroundColor: colors.surface }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleRow}>
              <Ionicons
                name={skill.skillType === SkillType.CORE ? 'star' : 
                      skill.skillType === SkillType.SUPPORTING ? 'build' : 'add-circle'}
                size={24}
                color={colors.primary}
                style={styles.titleIcon}
              />
              <Text style={[styles.title, { color: colors.text }]}>
                {skill.name}
              </Text>
            </View>
            
            {/* 难度和类型标签 */}
            <View style={styles.badgeRow}>
              <View style={[
                styles.difficultyBadge,
                { backgroundColor: getDifficultyColor(skill.difficultyLevel) + '20' }
              ]}>
                <Text style={[
                  styles.badgeText,
                  { color: getDifficultyColor(skill.difficultyLevel) }
                ]}>
                  {AtomicSkillUtils.getDifficultyLevelText(skill.difficultyLevel)}
                </Text>
              </View>
              
              <View style={[styles.typeBadge, { backgroundColor: colors.primary + '20' }]}>
                <Text style={[styles.badgeText, { color: colors.primary }]}>
                  {AtomicSkillUtils.getSkillTypeText(skill.skillType)}
                </Text>
              </View>
            </View>
            
            {/* 技能描述 */}
            {skill.description && (
              <Text style={[styles.description, { color: colors.textSecondary }]}>
                {skill.description}
              </Text>
            )}
          </View>
        </View>

        {/* 技能统计 */}
        <View style={[styles.statsCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            技能统计
          </Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Ionicons name="time-outline" size={20} color={colors.primary} />
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                预计时长
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {skill.estimatedHours}小时
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Ionicons name="people-outline" size={20} color={colors.info} />
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                学习人数
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {skill.learnerCount}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Ionicons name="star-outline" size={20} color={colors.warning} />
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                平均评分
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {skill.averageRating.toFixed(1)}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Ionicons name="trending-up-outline" size={20} color={colors.success} />
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                完成率
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {(skill.completionRate * 100).toFixed(0)}%
              </Text>
            </View>
          </View>
        </View>

        {/* 前置技能 */}
        {prerequisites.length > 0 && (
          <View style={[styles.sectionCard, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              前置技能
            </Text>
            <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
              学习此技能前建议先掌握以下技能
            </Text>
            {prerequisites.map((prereq) => (
              <TouchableOpacity
                key={prereq.id}
                style={[styles.skillItem, { backgroundColor: colors.background }]}
                onPress={() => navigation.push('AtomicSkillDetail', { skillId: prereq.id })}
                activeOpacity={0.7}
              >
                <View style={styles.skillItemContent}>
                  <Text style={[styles.skillItemName, { color: colors.text }]}>
                    {prereq.name}
                  </Text>
                  <Text style={[styles.skillItemCategory, { color: colors.textSecondary }]}>
                    {prereq.category}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* 相关技能 */}
        {relatedSkills.length > 0 && (
          <View style={[styles.sectionCard, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              相关技能
            </Text>
            <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
              与此技能相关的其他技能
            </Text>
            {relatedSkills.slice(0, 5).map((related) => (
              <TouchableOpacity
                key={related.id}
                style={[styles.skillItem, { backgroundColor: colors.background }]}
                onPress={() => navigation.push('AtomicSkillDetail', { skillId: related.id })}
                activeOpacity={0.7}
              >
                <View style={styles.skillItemContent}>
                  <Text style={[styles.skillItemName, { color: colors.text }]}>
                    {related.name}
                  </Text>
                  <Text style={[styles.skillItemCategory, { color: colors.textSecondary }]}>
                    {related.category}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>

      {/* 底部操作按钮 */}
      <View style={[styles.actionBar, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton, { backgroundColor: colors.background }]}
          onPress={handleAssessment}
          activeOpacity={0.7}
        >
          <Ionicons name="checkmark-circle-outline" size={20} color={colors.primary} />
          <Text style={[styles.actionButtonText, { color: colors.primary }]}>
            技能评估
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton, { backgroundColor: colors.primary }]}
          onPress={handleStartLearning}
          activeOpacity={0.7}
        >
          <Ionicons name="play-circle-outline" size={20} color={colors.surface} />
          <Text style={[styles.actionButtonText, { color: colors.surface }]}>
            开始学习
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// 获取难度级别颜色的辅助函数
const getDifficultyColor = (level: DifficultyLevel) => {
  switch (level) {
    case DifficultyLevel.BEGINNER:
      return '#10B981'; // green
    case DifficultyLevel.INTERMEDIATE:
      return '#F59E0B'; // yellow
    case DifficultyLevel.ADVANCED:
      return '#EF4444'; // red
    case DifficultyLevel.EXPERT:
      return '#8B5CF6'; // purple
    default:
      return '#6B7280'; // gray
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  headerCard: {
    margin: tokens.spacing('md'),
    borderRadius: tokens.radius('lg'),
    padding: tokens.spacing('lg'),
  },
  headerContent: {
    alignItems: 'flex-start',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  titleIcon: {
    marginRight: tokens.spacing('sm'),
  },
  title: {
    fontSize: tokens.fontSize('title-lg'),
    fontWeight: tokens.fontWeight('bold'),
    flex: 1,
  },
  badgeRow: {
    flexDirection: 'row',
    marginBottom: tokens.spacing('md'),
  },
  difficultyBadge: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginRight: tokens.spacing('sm'),
  },
  typeBadge: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
  },
  badgeText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('semibold'),
  },
  description: {
    fontSize: tokens.fontSize('body'),
    lineHeight: 24,
  },
  statsCard: {
    margin: tokens.spacing('md'),
    marginTop: 0,
    borderRadius: tokens.radius('lg'),
    padding: tokens.spacing('lg'),
  },
  sectionCard: {
    margin: tokens.spacing('md'),
    marginTop: 0,
    borderRadius: tokens.radius('lg'),
    padding: tokens.spacing('lg'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('xs'),
  },
  sectionSubtitle: {
    fontSize: tokens.fontSize('body'),
    marginBottom: tokens.spacing('md'),
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  statLabel: {
    fontSize: tokens.fontSize('caption'),
    marginTop: tokens.spacing('xs'),
    textAlign: 'center',
  },
  statValue: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginTop: tokens.spacing('xs'),
  },
  skillItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  skillItemContent: {
    flex: 1,
  },
  skillItemName: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
    marginBottom: tokens.spacing('xs'),
  },
  skillItemCategory: {
    fontSize: tokens.fontSize('caption'),
  },
  actionBar: {
    flexDirection: 'row',
    padding: tokens.spacing('md'),
    paddingBottom: tokens.spacing('lg'),
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('lg'),
  },
  primaryButton: {
    marginLeft: tokens.spacing('sm'),
  },
  secondaryButton: {
    marginRight: tokens.spacing('sm'),
    borderWidth: 1,
    borderColor: 'transparent',
  },
  actionButtonText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('semibold'),
    marginLeft: tokens.spacing('xs'),
  },
  loadingText: {
    fontSize: tokens.fontSize('body'),
    marginTop: tokens.spacing('md'),
  },
  errorText: {
    fontSize: tokens.fontSize('body'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('semibold'),
  },
});
