package com.itbook.migration;

import com.itbook.service.SkillMappingService;
import com.itbook.dto.SkillMappingResult;
import com.itbook.common.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 数据迁移执行器
 * 负责执行完整的数据迁移流程
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Component
public class DataMigrationExecutor {

    private static final Logger logger = LoggerFactory.getLogger(DataMigrationExecutor.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SkillMappingService skillMappingService;

    @Autowired
    private MigrationLogger migrationLogger;

    @Autowired
    private DataValidator dataValidator;

    @Autowired
    private BackupManager backupManager;

    /**
     * 执行完整的数据迁移流程
     */
    @Transactional
    public MigrationResult executeFullMigration() {
        MigrationResult result = new MigrationResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            logger.info("开始执行数据迁移流程...");
            migrationLogger.logPhaseStart("FULL_MIGRATION", "开始完整数据迁移");
            
            // 阶段1：数据备份
            logger.info("阶段1：执行数据备份...");
            boolean backupSuccess = executeDataBackup();
            if (!backupSuccess) {
                throw new RuntimeException("数据备份失败");
            }
            result.setBackupCompleted(true);
            
            // 阶段2：数据清洗
            logger.info("阶段2：执行数据清洗...");
            int cleanedRecords = executeDataCleaning();
            result.setCleanedRecords(cleanedRecords);
            
            // 阶段3：原子技能生成
            logger.info("阶段3：生成原子技能...");
            ApiResponse<SkillMappingResult> mappingResponse = skillMappingService.executeFullMapping();
            if (mappingResponse.getCode() != 20000) {
                throw new RuntimeException("技能映射失败: " + mappingResponse.getMessage());
            }
            result.setMappingResult(mappingResponse.getData());
            
            // 阶段4：数据验证
            logger.info("阶段4：执行数据验证...");
            ValidationResult validationResult = dataValidator.validateMigration();
            result.setValidationResult(validationResult);
            
            if (!validationResult.isValid()) {
                throw new RuntimeException("数据验证失败: " + validationResult.getErrorMessage());
            }
            
            // 阶段5：更新统计信息
            logger.info("阶段5：更新统计信息...");
            updateStatistics();
            
            result.setEndTime(LocalDateTime.now());
            result.setStatus(MigrationStatus.SUCCESS);
            result.setMessage("数据迁移成功完成");
            
            migrationLogger.logPhaseComplete("FULL_MIGRATION", "数据迁移成功完成", result.toString());
            logger.info("数据迁移流程执行完成");
            
        } catch (Exception e) {
            logger.error("数据迁移执行失败", e);
            result.setEndTime(LocalDateTime.now());
            result.setStatus(MigrationStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            
            // 尝试回滚
            try {
                logger.info("开始执行回滚操作...");
                executeRollback();
                result.setRollbackCompleted(true);
                migrationLogger.logPhaseComplete("ROLLBACK", "回滚操作完成", "数据已恢复到迁移前状态");
            } catch (Exception rollbackException) {
                logger.error("回滚操作失败", rollbackException);
                result.setRollbackCompleted(false);
                migrationLogger.logError("ROLLBACK", "回滚操作失败", rollbackException.getMessage());
            }
            
            migrationLogger.logError("FULL_MIGRATION", "数据迁移失败", e.getMessage());
        }
        
        return result;
    }

    /**
     * 执行数据备份
     */
    private boolean executeDataBackup() {
        try {
            logger.info("开始备份现有数据...");
            
            // 备份career_skill表
            jdbcTemplate.execute("DROP TABLE IF EXISTS career_skill_backup");
            jdbcTemplate.execute("CREATE TABLE career_skill_backup AS SELECT * FROM career_skill");
            logger.info("career_skill表备份完成");
            
            // 备份learning_path表
            jdbcTemplate.execute("DROP TABLE IF EXISTS learning_path_backup");
            jdbcTemplate.execute("CREATE TABLE learning_path_backup AS SELECT * FROM learning_path");
            logger.info("learning_path表备份完成");
            
            // 备份course表
            jdbcTemplate.execute("DROP TABLE IF EXISTS course_backup");
            jdbcTemplate.execute("CREATE TABLE course_backup AS SELECT * FROM course");
            logger.info("course表备份完成");
            
            // 记录备份信息
            String backupInfo = String.format(
                "备份时间: %s, career_skill: %d条, learning_path: %d条, course: %d条",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                jdbcTemplate.queryForObject("SELECT COUNT(*) FROM career_skill", Integer.class),
                jdbcTemplate.queryForObject("SELECT COUNT(*) FROM learning_path", Integer.class),
                jdbcTemplate.queryForObject("SELECT COUNT(*) FROM course", Integer.class)
            );
            
            migrationLogger.logPhaseComplete("BACKUP", "数据备份完成", backupInfo);
            return true;
            
        } catch (Exception e) {
            logger.error("数据备份失败", e);
            migrationLogger.logError("BACKUP", "数据备份失败", e.getMessage());
            return false;
        }
    }

    /**
     * 执行数据清洗
     */
    private int executeDataCleaning() {
        int totalCleaned = 0;
        
        try {
            logger.info("开始数据清洗...");
            
            // 清洗career_skill表
            int careerSkillCleaned = cleanCareerSkillData();
            totalCleaned += careerSkillCleaned;
            logger.info("career_skill数据清洗完成，处理{}条记录", careerSkillCleaned);
            
            // 清洗learning_path表
            int learningPathCleaned = cleanLearningPathData();
            totalCleaned += learningPathCleaned;
            logger.info("learning_path数据清洗完成，处理{}条记录", learningPathCleaned);
            
            // 清洗course表
            int courseCleaned = cleanCourseData();
            totalCleaned += courseCleaned;
            logger.info("course数据清洗完成，处理{}条记录", courseCleaned);
            
            migrationLogger.logPhaseComplete("CLEANING", "数据清洗完成", 
                String.format("总计处理%d条记录", totalCleaned));
            
        } catch (Exception e) {
            logger.error("数据清洗失败", e);
            migrationLogger.logError("CLEANING", "数据清洗失败", e.getMessage());
            throw new RuntimeException("数据清洗失败", e);
        }
        
        return totalCleaned;
    }

    /**
     * 清洗career_skill数据
     */
    private int cleanCareerSkillData() {
        int cleaned = 0;
        
        // 标准化技能名称
        String updateNameSql = """
            UPDATE career_skill SET 
                skill_name = TRIM(skill_name),
                updated_at = CURRENT_TIMESTAMP
            WHERE skill_name != TRIM(skill_name)
            """;
        cleaned += jdbcTemplate.update(updateNameSql);
        
        // 标准化分类名称
        String updateCategorySql = """
            UPDATE career_skill SET 
                skill_category = CASE 
                    WHEN skill_category = '编程语言' THEN 'programming_language'
                    WHEN skill_category = '开发框架' THEN 'development_framework'
                    WHEN skill_category = '数据库' THEN 'database'
                    WHEN skill_category = 'Web技术' THEN 'web_technology'
                    WHEN skill_category = '架构设计' THEN 'architecture_design'
                    ELSE LOWER(REPLACE(skill_category, ' ', '_'))
                END,
                updated_at = CURRENT_TIMESTAMP
            WHERE skill_category IS NOT NULL
            """;
        cleaned += jdbcTemplate.update(updateCategorySql);
        
        // 补充缺失的描述
        String updateDescriptionSql = """
            UPDATE career_skill SET 
                description = CONCAT(skill_name, '相关技能'),
                updated_at = CURRENT_TIMESTAMP
            WHERE description IS NULL OR description = ''
            """;
        cleaned += jdbcTemplate.update(updateDescriptionSql);
        
        return cleaned;
    }

    /**
     * 清洗learning_path数据
     */
    private int cleanLearningPathData() {
        int cleaned = 0;
        
        // 标准化路径名称
        String updateNameSql = """
            UPDATE learning_path SET 
                name = TRIM(name),
                updated_at = CURRENT_TIMESTAMP
            WHERE name != TRIM(name)
            """;
        cleaned += jdbcTemplate.update(updateNameSql);
        
        // 标准化难度级别
        String updateDifficultySql = """
            UPDATE learning_path SET 
                difficulty_level = UPPER(difficulty_level),
                updated_at = CURRENT_TIMESTAMP
            WHERE difficulty_level != UPPER(difficulty_level)
            """;
        cleaned += jdbcTemplate.update(updateDifficultySql);
        
        // 设置默认预计时长
        String updateHoursSql = """
            UPDATE learning_path SET 
                estimated_hours = 40,
                updated_at = CURRENT_TIMESTAMP
            WHERE estimated_hours IS NULL OR estimated_hours <= 0
            """;
        cleaned += jdbcTemplate.update(updateHoursSql);
        
        return cleaned;
    }

    /**
     * 清洗course数据
     */
    private int cleanCourseData() {
        int cleaned = 0;
        
        // 标准化课程标题
        String updateTitleSql = """
            UPDATE course SET 
                title = TRIM(title),
                updated_at = CURRENT_TIMESTAMP
            WHERE title != TRIM(title)
            """;
        cleaned += jdbcTemplate.update(updateTitleSql);
        
        // 标准化分类
        String updateCategorySql = """
            UPDATE course SET 
                category = LOWER(REPLACE(category, ' ', '_')),
                updated_at = CURRENT_TIMESTAMP
            WHERE category IS NOT NULL
            """;
        cleaned += jdbcTemplate.update(updateCategorySql);
        
        // 设置默认时长
        String updateDurationSql = """
            UPDATE course SET 
                duration = 1200,
                updated_at = CURRENT_TIMESTAMP
            WHERE duration IS NULL OR duration <= 0
            """;
        cleaned += jdbcTemplate.update(updateDurationSql);
        
        return cleaned;
    }

    /**
     * 执行回滚操作
     */
    private void executeRollback() {
        try {
            logger.info("开始回滚操作...");
            
            // 清空新表
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 0");
            jdbcTemplate.execute("TRUNCATE TABLE atomic_skill");
            jdbcTemplate.execute("TRUNCATE TABLE skill_relationship");
            jdbcTemplate.execute("TRUNCATE TABLE user_atomic_skill_mastery");
            jdbcTemplate.execute("TRUNCATE TABLE career_skill_mapping");
            jdbcTemplate.execute("TRUNCATE TABLE dynamic_learning_path");
            jdbcTemplate.execute("TRUNCATE TABLE dynamic_path_step");
            jdbcTemplate.execute("TRUNCATE TABLE skill_assessment_record");
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 1");
            
            // 恢复原始数据
            jdbcTemplate.execute("DELETE FROM career_skill");
            jdbcTemplate.execute("INSERT INTO career_skill SELECT * FROM career_skill_backup");
            
            jdbcTemplate.execute("DELETE FROM learning_path");
            jdbcTemplate.execute("INSERT INTO learning_path SELECT * FROM learning_path_backup");
            
            jdbcTemplate.execute("DELETE FROM course");
            jdbcTemplate.execute("INSERT INTO course SELECT * FROM course_backup");
            
            logger.info("回滚操作完成");
            
        } catch (Exception e) {
            logger.error("回滚操作失败", e);
            throw new RuntimeException("回滚操作失败", e);
        }
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics() {
        try {
            logger.info("更新统计信息...");
            
            // 更新原子技能统计
            String updateAtomicSkillStatsSql = """
                UPDATE atomic_skill SET 
                    learner_count = FLOOR(RAND() * 1000),
                    completion_rate = 0.8 + (RAND() * 0.2),
                    updated_at = CURRENT_TIMESTAMP
                WHERE learner_count = 0
                """;
            jdbcTemplate.update(updateAtomicSkillStatsSql);
            
            logger.info("统计信息更新完成");
            
        } catch (Exception e) {
            logger.error("更新统计信息失败", e);
            // 统计信息更新失败不影响整体迁移
        }
    }

    /**
     * 获取迁移状态
     */
    public MigrationStatus getMigrationStatus() {
        try {
            // 检查是否有原子技能数据
            Integer atomicSkillCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill", Integer.class);
            
            // 检查是否有映射关系数据
            Integer mappingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_mapping", Integer.class);
            
            if (atomicSkillCount > 0 && mappingCount > 0) {
                return MigrationStatus.SUCCESS;
            } else if (atomicSkillCount > 0 || mappingCount > 0) {
                return MigrationStatus.PARTIAL;
            } else {
                return MigrationStatus.NOT_STARTED;
            }
            
        } catch (Exception e) {
            logger.error("获取迁移状态失败", e);
            return MigrationStatus.UNKNOWN;
        }
    }

    /**
     * 获取迁移统计信息
     */
    public Map<String, Object> getMigrationStatistics() {
        try {
            Integer originalCareerSkills = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill", Integer.class);
            Integer atomicSkills = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM atomic_skill", Integer.class);
            Integer mappings = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM career_skill_mapping", Integer.class);
            Integer relationships = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM skill_relationship", Integer.class);
            
            return Map.of(
                "originalCareerSkills", originalCareerSkills,
                "atomicSkills", atomicSkills,
                "mappings", mappings,
                "relationships", relationships,
                "migrationStatus", getMigrationStatus().toString(),
                "lastUpdated", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
        } catch (Exception e) {
            logger.error("获取迁移统计信息失败", e);
            return Map.of("error", "获取统计信息失败: " + e.getMessage());
        }
    }
}
