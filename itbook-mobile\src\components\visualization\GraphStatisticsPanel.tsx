import React from 'react';
import {
  View,
  Text,
  Colors,
  Card,
  TouchableOpacity
} from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { designTokens } from '../../design-tokens';

// 图谱统计数据类型
export interface GraphStatistics {
  totalNodes: number;
  totalEdges: number;
  averageDegree: number;
  stronglyConnectedComponents: number;
  clusteringCoefficient: number;
  diameter: number;
  clusters: Array<{
    clusterId: number;
    skillIds: number[];
    category: string;
    cohesion: number;
  }>;
}

interface GraphStatisticsPanelProps {
  statistics: GraphStatistics;
  onClusterPress?: (clusterId: number) => void;
}

/**
 * 图谱统计面板组件
 * 展示技能图谱的统计信息和分析结果
 */
export const GraphStatisticsPanel: React.FC<GraphStatisticsPanelProps> = ({
  statistics,
  onClusterPress
}) => {
  const colors = useThemeColors();

  // 统计卡片数据
  const statsCards = [
    {
      icon: 'git-network-outline',
      label: '技能节点',
      value: statistics.totalNodes.toString(),
      color: colors.primary,
      backgroundColor: `${colors.primary}20`
    },
    {
      icon: 'git-branch-outline',
      label: '关系连接',
      value: statistics.totalEdges.toString(),
      color: '#4CAF50',
      backgroundColor: '#4CAF5020'
    },
    {
      icon: 'analytics-outline',
      label: '平均度数',
      value: statistics.averageDegree.toFixed(2),
      color: '#FF9800',
      backgroundColor: '#FF980020'
    },
    {
      icon: 'layers-outline',
      label: '连通组件',
      value: statistics.stronglyConnectedComponents.toString(),
      color: '#9C27B0',
      backgroundColor: '#9C27B020'
    }
  ];

  // 高级统计指标
  const advancedStats = [
    {
      label: '聚类系数',
      value: (statistics.clusteringCoefficient * 100).toFixed(1) + '%',
      description: '衡量图的聚集程度'
    },
    {
      label: '图直径',
      value: statistics.diameter.toString(),
      description: '图中最长最短路径的长度'
    }
  ];

  return (
    <View>
      {/* 基础统计卡片 */}
      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginHorizontal: designTokens.spacing.md,
        marginBottom: designTokens.spacing.md
      }}>
        {statsCards.map((stat, index) => (
          <View
            key={index}
            style={{
              width: '48%',
              marginRight: index % 2 === 0 ? '4%' : 0,
              marginBottom: designTokens.spacing.sm
            }}
          >
            <Card style={{
              padding: designTokens.spacing.md,
              backgroundColor: stat.backgroundColor,
              borderRadius: designTokens.borderRadius.md
            }}>
              <View row centerV>
                <Ionicons
                  name={stat.icon as any}
                  size={20}
                  color={stat.color}
                  style={{ marginRight: 8 }}
                />
                <View flex>
                  <Text style={{
                    fontSize: designTokens.fontSize.xs,
                    fontWeight: designTokens.fontWeight.medium,
                    color: stat.color,
                    marginBottom: 2
                  }}>
                    {stat.label}
                  </Text>
                  <Text style={{
                    fontSize: designTokens.fontSize.xl,
                    fontWeight: designTokens.fontWeight.bold,
                    color: stat.color
                  }}>
                    {stat.value}
                  </Text>
                </View>
              </View>
            </Card>
          </View>
        ))}
      </View>

      {/* 高级统计指标 */}
      <Card style={{
        margin: designTokens.spacing.md,
        padding: designTokens.spacing.md,
        backgroundColor: colors.backgroundPrimary
      }}>
        <Text style={{
          fontSize: designTokens.fontSize.md,
          fontWeight: designTokens.fontWeight.semiBold,
          color: colors.textPrimary,
          marginBottom: designTokens.spacing.md
        }}>
          图谱分析指标
        </Text>

        {advancedStats.map((stat, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingVertical: designTokens.spacing.sm,
              borderBottomWidth: index < advancedStats.length - 1 ? 1 : 0,
              borderBottomColor: colors.border
            }}
          >
            <View flex>
              <Text style={{
                fontSize: designTokens.fontSize.sm,
                fontWeight: designTokens.fontWeight.medium,
                color: colors.textPrimary,
                marginBottom: 2
              }}>
                {stat.label}
              </Text>
              <Text style={{
                fontSize: designTokens.fontSize.xs,
                color: colors.textSecondary
              }}>
                {stat.description}
              </Text>
            </View>
            <Text style={{
              fontSize: designTokens.fontSize.lg,
              fontWeight: designTokens.fontWeight.bold,
              color: colors.primary
            }}>
              {stat.value}
            </Text>
          </View>
        ))}
      </Card>

      {/* 技能聚类信息 */}
      <Card style={{
        margin: designTokens.spacing.md,
        padding: designTokens.spacing.md,
        backgroundColor: colors.backgroundPrimary
      }}>
        <Text style={{
          fontSize: designTokens.fontSize.md,
          fontWeight: designTokens.fontWeight.semiBold,
          color: colors.textPrimary,
          marginBottom: designTokens.spacing.md
        }}>
          技能聚类分析
        </Text>

        {statistics.clusters.map((cluster, index) => {
          const categoryColors = {
            'frontend': '#4CAF50',
            'backend': '#2196F3',
            'database': '#FF9800',
            'devops': '#9C27B0'
          };
          
          const clusterColor = categoryColors[cluster.category as keyof typeof categoryColors] || colors.textSecondary;
          
          return (
            <TouchableOpacity
              key={cluster.clusterId}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: designTokens.spacing.sm,
                paddingHorizontal: designTokens.spacing.sm,
                backgroundColor: `${clusterColor}10`,
                borderRadius: designTokens.borderRadius.sm,
                marginBottom: designTokens.spacing.xs,
                borderLeftWidth: 4,
                borderLeftColor: clusterColor
              }}
              onPress={() => onClusterPress?.(cluster.clusterId)}
              activeOpacity={0.7}
            >
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: clusterColor,
                marginRight: designTokens.spacing.sm
              }} />
              
              <View flex>
                <Text style={{
                  fontSize: designTokens.fontSize.sm,
                  fontWeight: designTokens.fontWeight.medium,
                  color: colors.textPrimary,
                  marginBottom: 2
                }}>
                  {cluster.category.toUpperCase()} 集群
                </Text>
                <Text style={{
                  fontSize: designTokens.fontSize.xs,
                  color: colors.textSecondary
                }}>
                  {cluster.skillIds.length} 个技能 · 内聚度 {(cluster.cohesion * 100).toFixed(1)}%
                </Text>
              </View>
              
              <Ionicons
                name="chevron-forward"
                size={16}
                color={colors.textSecondary}
              />
            </TouchableOpacity>
          );
        })}
      </Card>

      {/* 图谱健康度评估 */}
      <Card style={{
        margin: designTokens.spacing.md,
        padding: designTokens.spacing.md,
        backgroundColor: colors.backgroundPrimary
      }}>
        <Text style={{
          fontSize: designTokens.fontSize.md,
          fontWeight: designTokens.fontWeight.semiBold,
          color: colors.textPrimary,
          marginBottom: designTokens.spacing.md
        }}>
          图谱健康度评估
        </Text>

        {/* 连通性评估 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: designTokens.spacing.sm
        }}>
          <Ionicons
            name={statistics.stronglyConnectedComponents <= 5 ? "checkmark-circle" : "warning"}
            size={20}
            color={statistics.stronglyConnectedComponents <= 5 ? '#4CAF50' : '#FF9800'}
            style={{ marginRight: 8 }}
          />
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            color: colors.textPrimary
          }}>
            连通性: {statistics.stronglyConnectedComponents <= 5 ? '良好' : '需要优化'}
          </Text>
        </View>

        {/* 聚类质量评估 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: designTokens.spacing.sm
        }}>
          <Ionicons
            name={statistics.clusteringCoefficient > 0.3 ? "checkmark-circle" : "warning"}
            size={20}
            color={statistics.clusteringCoefficient > 0.3 ? '#4CAF50' : '#FF9800'}
            style={{ marginRight: 8 }}
          />
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            color: colors.textPrimary
          }}>
            聚类质量: {statistics.clusteringCoefficient > 0.3 ? '优秀' : '一般'}
          </Text>
        </View>

        {/* 图规模评估 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center'
        }}>
          <Ionicons
            name={statistics.diameter <= 8 ? "checkmark-circle" : "warning"}
            size={20}
            color={statistics.diameter <= 8 ? '#4CAF50' : '#FF9800'}
            style={{ marginRight: 8 }}
          />
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            color: colors.textPrimary
          }}>
            图规模: {statistics.diameter <= 8 ? '合理' : '过于复杂'}
          </Text>
        </View>
      </Card>
    </View>
  );
};
