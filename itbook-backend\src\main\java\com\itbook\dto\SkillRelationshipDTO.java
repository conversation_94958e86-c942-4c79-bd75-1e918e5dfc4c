package com.itbook.dto;

import com.itbook.entity.SkillRelationship;
import java.time.LocalDateTime;

/**
 * 技能关系DTO
 */
public class SkillRelationshipDTO {
    private Long id;
    private Long sourceSkillId;
    private Long targetSkillId;
    private String sourceSkillName;
    private String targetSkillName;
    private String relationshipType;
    private Double relationshipStrength;
    private Boolean isMandatory;
    private String description;
    private Integer learningSequence;
    private Double confidenceScore;
    private String source;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public SkillRelationshipDTO() {}

    public SkillRelationshipDTO(SkillRelationship relationship) {
        this.id = relationship.getId();
        this.sourceSkillId = relationship.getSourceSkillId();
        this.targetSkillId = relationship.getTargetSkillId();
        this.relationshipType = relationship.getRelationshipType().name();
        this.relationshipStrength = relationship.getRelationshipStrength();
        this.isMandatory = relationship.getIsMandatory();
        this.description = relationship.getDescription();
        this.learningSequence = relationship.getLearningSequence();
        this.confidenceScore = relationship.getConfidenceScore();
        this.source = relationship.getSource().name();
        this.isActive = relationship.getIsActive();
        this.createdAt = relationship.getCreatedAt();
        this.updatedAt = relationship.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getSourceSkillId() { return sourceSkillId; }
    public void setSourceSkillId(Long sourceSkillId) { this.sourceSkillId = sourceSkillId; }

    public Long getTargetSkillId() { return targetSkillId; }
    public void setTargetSkillId(Long targetSkillId) { this.targetSkillId = targetSkillId; }

    public String getSourceSkillName() { return sourceSkillName; }
    public void setSourceSkillName(String sourceSkillName) { this.sourceSkillName = sourceSkillName; }

    public String getTargetSkillName() { return targetSkillName; }
    public void setTargetSkillName(String targetSkillName) { this.targetSkillName = targetSkillName; }

    public String getRelationshipType() { return relationshipType; }
    public void setRelationshipType(String relationshipType) { this.relationshipType = relationshipType; }

    public Double getRelationshipStrength() { return relationshipStrength; }
    public void setRelationshipStrength(Double relationshipStrength) { this.relationshipStrength = relationshipStrength; }

    public Boolean getIsMandatory() { return isMandatory; }
    public void setIsMandatory(Boolean isMandatory) { this.isMandatory = isMandatory; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Integer getLearningSequence() { return learningSequence; }
    public void setLearningSequence(Integer learningSequence) { this.learningSequence = learningSequence; }

    public Double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(Double confidenceScore) { this.confidenceScore = confidenceScore; }

    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
