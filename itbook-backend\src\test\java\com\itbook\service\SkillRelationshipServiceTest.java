package com.itbook.service;

import com.itbook.entity.SkillRelationship;
import com.itbook.repository.SkillRelationshipRepository;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.dto.SkillRelationshipDTO;
import com.itbook.common.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 技能关系服务测试类
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@ExtendWith(MockitoExtension.class)
class SkillRelationshipServiceTest {

    @Mock
    private SkillRelationshipRepository relationshipRepository;

    @Mock
    private AtomicSkillRepository atomicSkillRepository;

    @InjectMocks
    private SkillRelationshipService relationshipService;

    private SkillRelationship testRelationship;
    private SkillRelationshipDTO testRelationshipDTO;

    @BeforeEach
    void setUp() {
        testRelationship = new SkillRelationship();
        testRelationship.setId(1L);
        testRelationship.setSourceSkillId(1L);
        testRelationship.setTargetSkillId(2L);
        testRelationship.setRelationshipType(SkillRelationship.RelationshipType.PREREQUISITE);
        testRelationship.setRelationshipStrength(0.8);
        testRelationship.setIsMandatory(true);
        testRelationship.setLearningSequence(1);
        testRelationship.setDescription("Java基础是Spring框架的前置技能");
        testRelationship.setSource(SkillRelationship.Source.EXPERT_DEFINED);
        testRelationship.setConfidenceScore(0.9);
        testRelationship.setIsActive(true);
        testRelationship.setCreatedAt(LocalDateTime.now());
        testRelationship.setUpdatedAt(LocalDateTime.now());

        testRelationshipDTO = new SkillRelationshipDTO();
        testRelationshipDTO.setSourceSkillId(1L);
        testRelationshipDTO.setTargetSkillId(2L);
        testRelationshipDTO.setRelationshipType(SkillRelationship.RelationshipType.PREREQUISITE);
        testRelationshipDTO.setRelationshipStrength(0.8);
        testRelationshipDTO.setIsMandatory(true);
        testRelationshipDTO.setLearningSequence(1);
        testRelationshipDTO.setDescription("Java基础是Spring框架的前置技能");
        testRelationshipDTO.setSource(SkillRelationship.Source.EXPERT_DEFINED);
        testRelationshipDTO.setConfidenceScore(0.9);
    }

    @Test
    void testCreateRelationship_Success() {
        // Given
        when(atomicSkillRepository.existsById(1L)).thenReturn(true);
        when(atomicSkillRepository.existsById(2L)).thenReturn(true);
        when(relationshipRepository.existsRelationship(1L, 2L, 
                SkillRelationship.RelationshipType.PREREQUISITE)).thenReturn(false);
        when(relationshipRepository.existsCircularDependency(1L, 2L)).thenReturn(false);
        when(relationshipRepository.save(any(SkillRelationship.class))).thenReturn(testRelationship);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.createRelationship(testRelationshipDTO);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1L, response.getData().getSourceSkillId());
        assertEquals(2L, response.getData().getTargetSkillId());
        verify(relationshipRepository).save(any(SkillRelationship.class));
    }

    @Test
    void testCreateRelationship_SourceSkillNotExists() {
        // Given
        when(atomicSkillRepository.existsById(1L)).thenReturn(false);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.createRelationship(testRelationshipDTO);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("源技能不存在"));
        verify(relationshipRepository, never()).save(any());
    }

    @Test
    void testCreateRelationship_TargetSkillNotExists() {
        // Given
        when(atomicSkillRepository.existsById(1L)).thenReturn(true);
        when(atomicSkillRepository.existsById(2L)).thenReturn(false);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.createRelationship(testRelationshipDTO);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("目标技能不存在"));
        verify(relationshipRepository, never()).save(any());
    }

    @Test
    void testCreateRelationship_RelationshipExists() {
        // Given
        when(atomicSkillRepository.existsById(1L)).thenReturn(true);
        when(atomicSkillRepository.existsById(2L)).thenReturn(true);
        when(relationshipRepository.existsRelationship(1L, 2L, 
                SkillRelationship.RelationshipType.PREREQUISITE)).thenReturn(true);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.createRelationship(testRelationshipDTO);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("技能关系已存在"));
        verify(relationshipRepository, never()).save(any());
    }

    @Test
    void testCreateRelationship_CircularDependency() {
        // Given
        when(atomicSkillRepository.existsById(1L)).thenReturn(true);
        when(atomicSkillRepository.existsById(2L)).thenReturn(true);
        when(relationshipRepository.existsRelationship(1L, 2L, 
                SkillRelationship.RelationshipType.PREREQUISITE)).thenReturn(false);
        when(relationshipRepository.existsCircularDependency(1L, 2L)).thenReturn(true);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.createRelationship(testRelationshipDTO);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("循环依赖"));
        verify(relationshipRepository, never()).save(any());
    }

    @Test
    void testUpdateRelationship_Success() {
        // Given
        when(relationshipRepository.findById(1L)).thenReturn(Optional.of(testRelationship));
        when(relationshipRepository.save(any(SkillRelationship.class))).thenReturn(testRelationship);

        // When
        ApiResponse<SkillRelationship> response = relationshipService.updateRelationship(1L, testRelationshipDTO);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        verify(relationshipRepository).save(any(SkillRelationship.class));
    }

    @Test
    void testUpdateRelationship_NotFound() {
        // Given
        when(relationshipRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ApiResponse<SkillRelationship> response = relationshipService.updateRelationship(1L, testRelationshipDTO);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("技能关系不存在"));
        verify(relationshipRepository, never()).save(any());
    }

    @Test
    void testDeleteRelationship_Success() {
        // Given
        when(relationshipRepository.existsById(1L)).thenReturn(true);

        // When
        ApiResponse<Void> response = relationshipService.deleteRelationship(1L);

        // Then
        assertEquals(20000, response.getCode());
        verify(relationshipRepository).deleteById(1L);
    }

    @Test
    void testDeleteRelationship_NotFound() {
        // Given
        when(relationshipRepository.existsById(1L)).thenReturn(false);

        // When
        ApiResponse<Void> response = relationshipService.deleteRelationship(1L);

        // Then
        assertEquals(40000, response.getCode());
        assertTrue(response.getMessage().contains("技能关系不存在"));
        verify(relationshipRepository, never()).deleteById(any());
    }

    @Test
    void testGetRelationshipById_Success() {
        // Given
        when(relationshipRepository.findById(1L)).thenReturn(Optional.of(testRelationship));

        // When
        ApiResponse<SkillRelationship> response = relationshipService.getRelationshipById(1L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1L, response.getData().getId());
    }

    @Test
    void testGetRelationshipById_NotFound() {
        // Given
        when(relationshipRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ApiResponse<SkillRelationship> response = relationshipService.getRelationshipById(1L);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("技能关系不存在"));
    }

    @Test
    void testGetSkillRelationships_Success() {
        // Given
        List<SkillRelationship> relationships = Arrays.asList(testRelationship);
        when(relationshipRepository.findBySourceSkillIdOrTargetSkillId(1L, 1L))
                .thenReturn(relationships);

        // When
        ApiResponse<List<SkillRelationship>> response = relationshipService.getSkillRelationships(1L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(relationshipRepository).findBySourceSkillIdOrTargetSkillId(1L, 1L);
    }

    @Test
    void testGetPrerequisiteSkills_Success() {
        // Given
        List<SkillRelationship> relationships = Arrays.asList(testRelationship);
        when(relationshipRepository.findPrerequisiteRelationships(2L)).thenReturn(relationships);

        // When
        ApiResponse<List<SkillRelationship>> response = relationshipService.getPrerequisiteSkills(2L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(relationshipRepository).findPrerequisiteRelationships(2L);
    }

    @Test
    void testGetSuccessorSkills_Success() {
        // Given
        List<SkillRelationship> relationships = Arrays.asList(testRelationship);
        when(relationshipRepository.findSuccessorRelationships(1L)).thenReturn(relationships);

        // When
        ApiResponse<List<SkillRelationship>> response = relationshipService.getSuccessorSkills(1L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(relationshipRepository).findSuccessorRelationships(1L);
    }

    @Test
    void testBuildLearningPath_Success() {
        // Given
        List<SkillRelationship> prerequisites = Arrays.asList(testRelationship);
        when(relationshipRepository.findPrerequisitesByLearningSequence(2L)).thenReturn(prerequisites);
        when(relationshipRepository.findPrerequisitesByLearningSequence(1L)).thenReturn(Arrays.asList());

        // When
        ApiResponse<List<Long>> response = relationshipService.buildLearningPath(2L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertTrue(response.getData().contains(1L));
        assertTrue(response.getData().contains(2L));
        // 验证前置技能在目标技能之前
        assertTrue(response.getData().indexOf(1L) < response.getData().indexOf(2L));
    }

    @Test
    void testCreateRelationshipsBatch_Success() {
        // Given
        List<SkillRelationshipDTO> dtos = Arrays.asList(testRelationshipDTO);
        when(relationshipRepository.existsRelationship(1L, 2L, 
                SkillRelationship.RelationshipType.PREREQUISITE)).thenReturn(false);
        when(relationshipRepository.saveAll(anyList())).thenReturn(Arrays.asList(testRelationship));

        // When
        ApiResponse<List<SkillRelationship>> response = relationshipService.createRelationshipsBatch(dtos);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(relationshipRepository).saveAll(anyList());
    }
}
