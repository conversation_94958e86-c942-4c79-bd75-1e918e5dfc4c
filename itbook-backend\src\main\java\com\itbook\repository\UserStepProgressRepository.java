package com.itbook.repository;

import com.itbook.entity.UserStepProgress;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户学习步骤进度数据访问接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-11
 */
@Repository
public interface UserStepProgressRepository extends JpaRepository<UserStepProgress, Long> {

    /**
     * 根据用户ID和步骤ID查找进度记录
     * 
     * @param userId 用户ID
     * @param stepId 步骤ID
     * @return 进度记录（可选）
     */
    Optional<UserStepProgress> findByUserIdAndStepId(Long userId, Long stepId);

    /**
     * 根据用户ID查找所有步骤进度
     *
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 步骤进度分页列表
     */
    Page<UserStepProgress> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID查找所有步骤进度（不分页）
     *
     * @param userId 用户ID
     * @return 步骤进度列表
     */
    List<UserStepProgress> findByUserId(Long userId);

    /**
     * 根据用户ID和状态查找步骤进度
     * 
     * @param userId 用户ID
     * @param status 学习状态
     * @param pageable 分页参数
     * @return 步骤进度分页列表
     */
    Page<UserStepProgress> findByUserIdAndStatus(Long userId, UserStepProgress.Status status, Pageable pageable);

    /**
     * 根据步骤ID查找所有用户进度
     * 
     * @param stepId 步骤ID
     * @param pageable 分页参数
     * @return 用户进度分页列表
     */
    Page<UserStepProgress> findByStepId(Long stepId, Pageable pageable);

    /**
     * 根据用户ID和步骤ID列表查找进度
     * 
     * @param userId 用户ID
     * @param stepIds 步骤ID列表
     * @return 步骤进度列表
     */
    List<UserStepProgress> findByUserIdAndStepIdIn(Long userId, List<Long> stepIds);

    /**
     * 查找用户正在进行的步骤
     * 
     * @param userId 用户ID
     * @return 正在进行的步骤进度列表
     */
    List<UserStepProgress> findByUserIdAndStatusIn(Long userId, List<UserStepProgress.Status> statuses);

    /**
     * 查找用户已完成的步骤
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 已完成的步骤进度分页列表
     */
    Page<UserStepProgress> findByUserIdAndStatusOrderByCompletedAtDesc(Long userId, 
                                                                       UserStepProgress.Status status, 
                                                                       Pageable pageable);

    /**
     * 查找用户最近学习的步骤
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 最近学习的步骤进度分页列表
     */
    Page<UserStepProgress> findByUserIdAndLastStudiedAtIsNotNullOrderByLastStudiedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据完成百分比范围查找步骤进度
     * 
     * @param userId 用户ID
     * @param minPercentage 最小完成百分比
     * @param maxPercentage 最大完成百分比
     * @param pageable 分页参数
     * @return 步骤进度分页列表
     */
    Page<UserStepProgress> findByUserIdAndCompletionPercentageBetween(Long userId, 
                                                                      BigDecimal minPercentage, 
                                                                      BigDecimal maxPercentage, 
                                                                      Pageable pageable);

    /**
     * 统计用户的步骤进度数量
     * 
     * @param userId 用户ID
     * @return 步骤进度数量
     */
    long countByUserId(Long userId);

    /**
     * 统计用户已完成的步骤数量
     * 
     * @param userId 用户ID
     * @return 已完成的步骤数量
     */
    long countByUserIdAndStatus(Long userId, UserStepProgress.Status status);

    /**
     * 计算用户的平均完成百分比
     * 
     * @param userId 用户ID
     * @return 平均完成百分比
     */
    @Query("SELECT AVG(usp.completionPercentage) FROM UserStepProgress usp WHERE usp.userId = :userId")
    BigDecimal calculateAverageCompletionPercentage(@Param("userId") Long userId);

    /**
     * 计算用户的总学习时间
     * 
     * @param userId 用户ID
     * @return 总学习时间（分钟）
     */
    @Query("SELECT SUM(usp.studiedMinutes) FROM UserStepProgress usp WHERE usp.userId = :userId")
    Long calculateTotalStudiedMinutes(@Param("userId") Long userId);

    /**
     * 查找用户在特定学习路径中的步骤进度
     * 
     * @param userId 用户ID
     * @param learningPathId 学习路径ID
     * @return 步骤进度列表
     */
    @Query("SELECT usp FROM UserStepProgress usp " +
           "JOIN LearningPathStep lps ON usp.stepId = lps.id " +
           "WHERE usp.userId = :userId AND lps.learningPathId = :learningPathId " +
           "ORDER BY lps.stepOrder")
    List<UserStepProgress> findByUserIdAndLearningPathId(@Param("userId") Long userId, 
                                                         @Param("learningPathId") Long learningPathId);

    /**
     * 计算用户在特定学习路径中的完成百分比
     * 
     * @param userId 用户ID
     * @param learningPathId 学习路径ID
     * @return 完成百分比
     */
    @Query("SELECT AVG(usp.completionPercentage) FROM UserStepProgress usp " +
           "JOIN LearningPathStep lps ON usp.stepId = lps.id " +
           "WHERE usp.userId = :userId AND lps.learningPathId = :learningPathId")
    BigDecimal calculatePathCompletionPercentage(@Param("userId") Long userId, 
                                                 @Param("learningPathId") Long learningPathId);

    /**
     * 查找步骤的学习统计
     * 
     * @param stepId 步骤ID
     * @return 学习统计
     */
    @Query("SELECT " +
           "COUNT(usp) as totalLearners, " +
           "COUNT(CASE WHEN usp.status = 'COMPLETED' THEN 1 END) as completedLearners, " +
           "AVG(usp.completionPercentage) as avgCompletion, " +
           "AVG(usp.studiedMinutes) as avgStudyTime " +
           "FROM UserStepProgress usp " +
           "WHERE usp.stepId = :stepId")
    Object[] findStepStatistics(@Param("stepId") Long stepId);

    /**
     * 查找用户学习活跃度统计
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活跃度统计
     */
    @Query("SELECT DATE(usp.lastStudiedAt) as studyDate, COUNT(DISTINCT usp.stepId) as stepsStudied " +
           "FROM UserStepProgress usp " +
           "WHERE usp.userId = :userId " +
           "AND usp.lastStudiedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(usp.lastStudiedAt) " +
           "ORDER BY studyDate")
    List<Object[]> findUserActivityStatistics(@Param("userId") Long userId, 
                                              @Param("startDate") LocalDateTime startDate, 
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户的学习连续天数
     * 
     * @param userId 用户ID
     * @return 连续学习天数
     */
    @Query(value = "SELECT COUNT(*) FROM (" +
                   "SELECT DATE(last_studied_at) as study_date " +
                   "FROM user_step_progress " +
                   "WHERE user_id = :userId " +
                   "AND last_studied_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) " +
                   "GROUP BY DATE(last_studied_at) " +
                   "ORDER BY study_date DESC " +
                   "LIMIT 30" +
                   ") as recent_studies", nativeQuery = true)
    Integer findUserStreakDays(@Param("userId") Long userId);

    /**
     * 删除用户的所有步骤进度
     * 
     * @param userId 用户ID
     * @return 删除的记录数
     */
    long deleteByUserId(Long userId);

    /**
     * 删除特定步骤的所有用户进度
     * 
     * @param stepId 步骤ID
     * @return 删除的记录数
     */
    long deleteByStepId(Long stepId);
}
