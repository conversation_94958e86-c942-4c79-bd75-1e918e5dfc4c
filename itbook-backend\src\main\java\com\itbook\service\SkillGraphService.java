package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.SkillRelationshipRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能图算法引擎服务
 * 提供技能关系图的算法分析功能，包括路径推荐、依赖检测、关系强度计算等
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SkillGraphService {

    private final SkillRelationshipRepository skillRelationshipRepository;
    private final AtomicSkillRepository atomicSkillRepository;
    private final SkillRelationshipService skillRelationshipService;

    /**
     * 构建技能学习路径
     * 使用拓扑排序算法构建从基础技能到目标技能的最优学习路径
     */
    @Transactional(readOnly = true)
    public LearningPath buildOptimalLearningPath(Long targetSkillId) {
        log.debug("构建最优学习路径: targetSkillId={}", targetSkillId);
        
        AtomicSkill targetSkill = atomicSkillRepository.findById(targetSkillId)
                .orElseThrow(() -> new IllegalArgumentException("目标技能不存在: " + targetSkillId));
        
        // 构建技能图
        SkillGraph graph = buildSkillGraph();
        
        // 检测循环依赖
        List<List<Long>> cycles = detectCycles(graph);
        if (!cycles.isEmpty()) {
            log.warn("检测到循环依赖: {}", cycles);
        }
        
        // 使用拓扑排序构建学习路径
        List<SkillNode> path = topologicalSort(graph, targetSkillId);
        
        // 计算路径统计信息
        PathStatistics statistics = calculatePathStatistics(path);
        
        LearningPath learningPath = new LearningPath();
        learningPath.setTargetSkill(targetSkill);
        learningPath.setPath(path);
        learningPath.setStatistics(statistics);
        learningPath.setCycles(cycles);
        
        return learningPath;
    }

    /**
     * 推荐相关技能
     * 基于技能关系强度和类型推荐相关技能
     */
    @Transactional(readOnly = true)
    public List<SkillRecommendation> recommendRelatedSkills(Long skillId, int maxRecommendations) {
        log.debug("推荐相关技能: skillId={}, maxRecommendations={}", skillId, maxRecommendations);
        
        List<SkillRelationship> relationships = skillRelationshipRepository.findBySourceSkillIdOrTargetSkillId(skillId, skillId);
        
        Map<Long, SkillRecommendation> recommendations = new HashMap<>();
        
        for (SkillRelationship relationship : relationships) {
            Long relatedSkillId = relationship.getSourceSkillId().equals(skillId) 
                    ? relationship.getTargetSkillId() 
                    : relationship.getSourceSkillId();
            
            if (relatedSkillId.equals(skillId)) continue;
            
            AtomicSkill relatedSkill = atomicSkillRepository.findById(relatedSkillId).orElse(null);
            if (relatedSkill == null) continue;
            
            SkillRecommendation recommendation = recommendations.computeIfAbsent(relatedSkillId, 
                    id -> new SkillRecommendation(relatedSkill, new ArrayList<>(), BigDecimal.ZERO));
            
            // 计算推荐分数
            BigDecimal score = calculateRecommendationScore(relationship);
            recommendation.setScore(recommendation.getScore().add(score));
            recommendation.getReasons().add(createRecommendationReason(relationship));
        }
        
        return recommendations.values().stream()
                .sorted((a, b) -> b.getScore().compareTo(a.getScore()))
                .limit(maxRecommendations)
                .collect(Collectors.toList());
    }

    /**
     * 检测技能图中的循环依赖
     */
    @Transactional(readOnly = true)
    public List<List<Long>> detectCycles(SkillGraph graph) {
        log.debug("检测循环依赖");
        
        List<List<Long>> cycles = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        Set<Long> recursionStack = new HashSet<>();
        
        for (Long skillId : graph.getNodes().keySet()) {
            if (!visited.contains(skillId)) {
                List<Long> currentPath = new ArrayList<>();
                if (detectCyclesDFS(graph, skillId, visited, recursionStack, currentPath, cycles)) {
                    log.warn("发现循环依赖: {}", currentPath);
                }
            }
        }
        
        return cycles;
    }

    /**
     * 计算技能关系强度
     * 基于多个因素计算技能之间的关系强度
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateRelationshipStrength(Long sourceSkillId, Long targetSkillId) {
        log.debug("计算关系强度: sourceSkillId={}, targetSkillId={}", sourceSkillId, targetSkillId);
        
        List<SkillRelationship> directRelationships = skillRelationshipRepository
                .findBySourceSkillIdAndTargetSkillId(sourceSkillId, targetSkillId);
        
        if (directRelationships.isEmpty()) {
            // 计算间接关系强度
            return calculateIndirectRelationshipStrength(sourceSkillId, targetSkillId);
        }
        
        // 计算直接关系强度的加权平均
        BigDecimal totalStrength = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;
        
        for (SkillRelationship relationship : directRelationships) {
            BigDecimal weight = getRelationshipTypeWeight(relationship.getRelationshipType());
            totalStrength = totalStrength.add(relationship.getRelationshipStrength().multiply(weight));
            totalWeight = totalWeight.add(weight);
        }
        
        return totalWeight.compareTo(BigDecimal.ZERO) > 0 
                ? totalStrength.divide(totalWeight, 2, BigDecimal.ROUND_HALF_UP)
                : BigDecimal.ZERO;
    }

    /**
     * 构建技能图
     */
    private SkillGraph buildSkillGraph() {
        List<SkillRelationship> relationships = skillRelationshipRepository.findAll();
        SkillGraph graph = new SkillGraph();
        
        for (SkillRelationship relationship : relationships) {
            if (!relationship.getIsActive()) continue;
            
            Long sourceId = relationship.getSourceSkillId();
            Long targetId = relationship.getTargetSkillId();
            
            graph.addNode(sourceId);
            graph.addNode(targetId);
            graph.addEdge(sourceId, targetId, relationship);
        }
        
        return graph;
    }

    /**
     * 拓扑排序算法
     */
    private List<SkillNode> topologicalSort(SkillGraph graph, Long targetSkillId) {
        Map<Long, Integer> inDegree = new HashMap<>();
        Queue<Long> queue = new LinkedList<>();
        List<SkillNode> result = new ArrayList<>();
        
        // 计算入度
        for (Long skillId : graph.getNodes().keySet()) {
            inDegree.put(skillId, 0);
        }
        
        for (Long skillId : graph.getNodes().keySet()) {
            for (SkillRelationship edge : graph.getEdges(skillId)) {
                inDegree.put(edge.getTargetSkillId(), 
                        inDegree.get(edge.getTargetSkillId()) + 1);
            }
        }
        
        // 找到所有入度为0的节点
        for (Map.Entry<Long, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }
        
        // 拓扑排序
        while (!queue.isEmpty()) {
            Long currentSkillId = queue.poll();
            AtomicSkill skill = atomicSkillRepository.findById(currentSkillId).orElse(null);
            
            if (skill != null) {
                SkillNode node = new SkillNode();
                node.setSkill(skillRelationshipService.createCleanSkill(skill));
                node.setLevel(calculateSkillLevel(currentSkillId, graph));
                result.add(node);
            }
            
            // 更新相邻节点的入度
            for (SkillRelationship edge : graph.getEdges(currentSkillId)) {
                Long targetId = edge.getTargetSkillId();
                inDegree.put(targetId, inDegree.get(targetId) - 1);
                if (inDegree.get(targetId) == 0) {
                    queue.offer(targetId);
                }
            }
        }
        
        return result;
    }

    /**
     * DFS检测循环依赖
     */
    private boolean detectCyclesDFS(SkillGraph graph, Long skillId, Set<Long> visited, 
                                   Set<Long> recursionStack, List<Long> currentPath, 
                                   List<List<Long>> cycles) {
        visited.add(skillId);
        recursionStack.add(skillId);
        currentPath.add(skillId);
        
        for (SkillRelationship edge : graph.getEdges(skillId)) {
            Long targetId = edge.getTargetSkillId();
            
            if (!visited.contains(targetId)) {
                if (detectCyclesDFS(graph, targetId, visited, recursionStack, currentPath, cycles)) {
                    return true;
                }
            } else if (recursionStack.contains(targetId)) {
                // 发现循环
                int cycleStart = currentPath.indexOf(targetId);
                cycles.add(new ArrayList<>(currentPath.subList(cycleStart, currentPath.size())));
                return true;
            }
        }
        
        recursionStack.remove(skillId);
        currentPath.remove(currentPath.size() - 1);
        return false;
    }

    /**
     * 计算路径统计信息
     */
    private PathStatistics calculatePathStatistics(List<SkillNode> path) {
        PathStatistics stats = new PathStatistics();
        stats.setTotalSkills(path.size());
        stats.setEstimatedHours(path.stream()
                .mapToInt(node -> node.getSkill().getEstimatedHours() != null 
                        ? node.getSkill().getEstimatedHours() : 0)
                .sum());
        stats.setAverageDifficulty(path.stream()
                .collect(Collectors.groupingBy(node -> node.getSkill().getDifficultyLevel(),
                        Collectors.counting())));
        
        return stats;
    }

    /**
     * 计算推荐分数
     */
    private BigDecimal calculateRecommendationScore(SkillRelationship relationship) {
        BigDecimal baseScore = relationship.getRelationshipStrength();
        BigDecimal typeMultiplier = getRelationshipTypeWeight(relationship.getRelationshipType());
        BigDecimal mandatoryBonus = relationship.getIsMandatory() ? new BigDecimal("0.2") : BigDecimal.ZERO;
        
        return baseScore.multiply(typeMultiplier).add(mandatoryBonus);
    }

    /**
     * 创建推荐原因
     */
    private RecommendationReason createRecommendationReason(SkillRelationship relationship) {
        RecommendationReason reason = new RecommendationReason();
        reason.setType(relationship.getRelationshipType().toString());
        reason.setDescription(relationship.getDescription());
        reason.setStrength(relationship.getRelationshipStrength());
        return reason;
    }

    /**
     * 获取关系类型权重
     */
    private BigDecimal getRelationshipTypeWeight(SkillRelationship.RelationshipType type) {
        switch (type) {
            case PREREQUISITE: return new BigDecimal("1.0");
            case SUCCESSOR: return new BigDecimal("0.8");
            case COREQUISITE: return new BigDecimal("0.7");
            case RELATED: return new BigDecimal("0.6");
            case ALTERNATIVE: return new BigDecimal("0.5");
            default: return new BigDecimal("0.5");
        }
    }

    /**
     * 计算间接关系强度
     */
    private BigDecimal calculateIndirectRelationshipStrength(Long sourceSkillId, Long targetSkillId) {
        // 简化实现：通过共同的相关技能计算间接关系强度
        List<AtomicSkill> sourceRelated = skillRelationshipService.getRelatedSkills(sourceSkillId);
        List<AtomicSkill> targetRelated = skillRelationshipService.getRelatedSkills(targetSkillId);
        
        Set<Long> commonSkills = sourceRelated.stream()
                .map(AtomicSkill::getId)
                .filter(id -> targetRelated.stream().anyMatch(skill -> skill.getId().equals(id)))
                .collect(Collectors.toSet());
        
        return new BigDecimal(commonSkills.size()).multiply(new BigDecimal("0.1"));
    }

    /**
     * 计算技能级别
     */
    private int calculateSkillLevel(Long skillId, SkillGraph graph) {
        // 基于前置技能的数量计算级别
        return graph.getIncomingEdges(skillId).size();
    }

    // 内部数据结构
    @Data
    public static class SkillGraph {
        private Map<Long, List<SkillRelationship>> adjacencyList = new HashMap<>();
        
        public void addNode(Long skillId) {
            adjacencyList.putIfAbsent(skillId, new ArrayList<>());
        }
        
        public void addEdge(Long sourceId, Long targetId, SkillRelationship relationship) {
            adjacencyList.get(sourceId).add(relationship);
        }
        
        public List<SkillRelationship> getEdges(Long skillId) {
            return adjacencyList.getOrDefault(skillId, new ArrayList<>());
        }
        
        public List<SkillRelationship> getIncomingEdges(Long skillId) {
            return adjacencyList.values().stream()
                    .flatMap(List::stream)
                    .filter(edge -> edge.getTargetSkillId().equals(skillId))
                    .collect(Collectors.toList());
        }
        
        public Map<Long, List<SkillRelationship>> getNodes() {
            return adjacencyList;
        }
    }

    @Data
    public static class LearningPath {
        private AtomicSkill targetSkill;
        private List<SkillNode> path;
        private PathStatistics statistics;
        private List<List<Long>> cycles;
    }

    @Data
    public static class SkillNode {
        private AtomicSkill skill;
        private int level;
    }

    @Data
    public static class PathStatistics {
        private int totalSkills;
        private int estimatedHours;
        private Map<AtomicSkill.DifficultyLevel, Long> averageDifficulty;
    }

    @Data
    public static class SkillRecommendation {
        private AtomicSkill skill;
        private List<RecommendationReason> reasons;
        private BigDecimal score;
        
        public SkillRecommendation(AtomicSkill skill, List<RecommendationReason> reasons, BigDecimal score) {
            this.skill = skill;
            this.reasons = reasons;
            this.score = score;
        }
    }

    @Data
    public static class RecommendationReason {
        private String type;
        private String description;
        private BigDecimal strength;
    }
}
