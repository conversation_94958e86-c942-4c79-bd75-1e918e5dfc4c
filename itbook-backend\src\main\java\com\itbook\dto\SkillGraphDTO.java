package com.itbook.dto;

import java.util.List;
import java.util.Map;

/**
 * 技能图谱DTO
 * 用于表示技能之间的关系网络
 */
public class SkillGraphDTO {
    private List<SkillNodeDTO> nodes;
    private List<SkillEdgeDTO> edges;
    private Map<String, Object> metadata;

    public SkillGraphDTO() {}

    public SkillGraphDTO(List<SkillNodeDTO> nodes, List<SkillEdgeDTO> edges) {
        this.nodes = nodes;
        this.edges = edges;
    }

    // Getters and Setters
    public List<SkillNodeDTO> getNodes() { return nodes; }
    public void setNodes(List<SkillNodeDTO> nodes) { this.nodes = nodes; }

    public List<SkillEdgeDTO> getEdges() { return edges; }
    public void setEdges(List<SkillEdgeDTO> edges) { this.edges = edges; }

    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }

    /**
     * 技能节点DTO
     */
    public static class SkillNodeDTO {
        private Long id;
        private String skillCode;
        private String name;
        private String category;
        private String difficultyLevel;
        private Integer estimatedHours;
        private Double x; // 图形坐标
        private Double y; // 图形坐标

        public SkillNodeDTO() {}

        public SkillNodeDTO(Long id, String skillCode, String name, String category, String difficultyLevel) {
            this.id = id;
            this.skillCode = skillCode;
            this.name = name;
            this.category = category;
            this.difficultyLevel = difficultyLevel;
        }

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getSkillCode() { return skillCode; }
        public void setSkillCode(String skillCode) { this.skillCode = skillCode; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public String getDifficultyLevel() { return difficultyLevel; }
        public void setDifficultyLevel(String difficultyLevel) { this.difficultyLevel = difficultyLevel; }

        public Integer getEstimatedHours() { return estimatedHours; }
        public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

        public Double getX() { return x; }
        public void setX(Double x) { this.x = x; }

        public Double getY() { return y; }
        public void setY(Double y) { this.y = y; }
    }

    /**
     * 技能边DTO
     */
    public static class SkillEdgeDTO {
        private Long sourceId;
        private Long targetId;
        private String relationshipType;
        private Double strength;
        private Boolean isMandatory;
        private String description;

        public SkillEdgeDTO() {}

        public SkillEdgeDTO(Long sourceId, Long targetId, String relationshipType, Double strength) {
            this.sourceId = sourceId;
            this.targetId = targetId;
            this.relationshipType = relationshipType;
            this.strength = strength;
        }

        // Getters and Setters
        public Long getSourceId() { return sourceId; }
        public void setSourceId(Long sourceId) { this.sourceId = sourceId; }

        public Long getTargetId() { return targetId; }
        public void setTargetId(Long targetId) { this.targetId = targetId; }

        public String getRelationshipType() { return relationshipType; }
        public void setRelationshipType(String relationshipType) { this.relationshipType = relationshipType; }

        public Double getStrength() { return strength; }
        public void setStrength(Double strength) { this.strength = strength; }

        public Boolean getIsMandatory() { return isMandatory; }
        public void setIsMandatory(Boolean isMandatory) { this.isMandatory = isMandatory; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
