package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.common.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 原子技能服务测试类
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@ExtendWith(MockitoExtension.class)
class AtomicSkillServiceTest {

    @Mock
    private AtomicSkillRepository atomicSkillRepository;

    @InjectMocks
    private AtomicSkillService atomicSkillService;

    private AtomicSkill testSkill;

    @BeforeEach
    void setUp() {
        testSkill = new AtomicSkill();
        testSkill.setId(1L);
        testSkill.setSkillCode("JAVA_BASIC");
        testSkill.setName("Java基础");
        testSkill.setDescription("Java编程语言基础知识");
        testSkill.setCategory("编程语言");
        testSkill.setSubcategory("Java");
        testSkill.setDifficultyLevel(AtomicSkill.DifficultyLevel.BEGINNER);
        testSkill.setSkillType(AtomicSkill.SkillType.TECHNICAL);
        testSkill.setEstimatedHours(40);
        testSkill.setAverageRating(new BigDecimal("4.5"));
        testSkill.setLearnerCount(1000L);
        testSkill.setCompletionRate(new BigDecimal("0.85"));
        testSkill.setStatus(AtomicSkill.Status.PUBLISHED);
        testSkill.setIsActive(true);
        testSkill.setKeywords("java,编程,基础");
        testSkill.setCreatedAt(LocalDateTime.now());
        testSkill.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testGetAtomicSkillById_Success() {
        // Given
        when(atomicSkillRepository.findById(1L)).thenReturn(Optional.of(testSkill));

        // When
        ApiResponse<AtomicSkill> response = atomicSkillService.getAtomicSkillById(1L);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Java基础", response.getData().getName());
        verify(atomicSkillRepository).findById(1L);
    }

    @Test
    void testGetAtomicSkillById_NotFound() {
        // Given
        when(atomicSkillRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        ApiResponse<AtomicSkill> response = atomicSkillService.getAtomicSkillById(1L);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("不存在"));
    }

    @Test
    void testSearchAtomicSkills_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.searchByKeyword("Java")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.searchAtomicSkills("Java");

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        assertEquals("Java基础", response.getData().get(0).getName());
        verify(atomicSkillRepository).searchByKeyword("Java");
    }

    @Test
    void testGetSkillsByCategory_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findByCategory("编程语言")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.getSkillsByCategory("编程语言", null);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findByCategory("编程语言");
    }

    @Test
    void testGetSkillsByCategoryAndSubcategory_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findByCategoryAndSubcategory("编程语言", "Java")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.getSkillsByCategory("编程语言", "Java");

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findByCategoryAndSubcategory("编程语言", "Java");
    }

    @Test
    void testGetSkillsByDifficulty_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findByDifficultyLevel(AtomicSkill.DifficultyLevel.BEGINNER))
                .thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService
                .getSkillsByDifficulty(AtomicSkill.DifficultyLevel.BEGINNER);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findByDifficultyLevel(AtomicSkill.DifficultyLevel.BEGINNER);
    }

    @Test
    void testGetSkillsByType_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findBySkillType(AtomicSkill.SkillType.TECHNICAL))
                .thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService
                .getSkillsByType(AtomicSkill.SkillType.TECHNICAL);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findBySkillType(AtomicSkill.SkillType.TECHNICAL);
    }

    @Test
    void testGetSkillsByTag_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findByTag("java")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.getSkillsByTag("java");

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findByTag("java");
    }

    @Test
    void testAdvancedSearchAtomicSkills_WithKeyword() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.searchByKeyword("Java")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.advancedSearchAtomicSkills(
                "Java", null, null, null, null,
                null, null, null, null, null,
                "averageRating", "desc", 10);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).searchByKeyword("Java");
    }

    @Test
    void testAdvancedSearchAtomicSkills_WithoutKeyword() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findByStatus(AtomicSkill.Status.PUBLISHED)).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.advancedSearchAtomicSkills(
                null, "编程语言", null, null, null,
                null, null, null, null, null,
                "averageRating", "desc", 10);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findByStatus(AtomicSkill.Status.PUBLISHED);
    }

    @Test
    void testAdvancedSearchAtomicSkills_WithFilters() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.searchByKeyword("Java")).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.advancedSearchAtomicSkills(
                "Java", "编程语言", "Java", AtomicSkill.DifficultyLevel.BEGINNER, 
                AtomicSkill.SkillType.TECHNICAL, 30, 50, 4.0, 5.0, "java",
                "averageRating", "desc", 10);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        
        // 验证过滤条件生效
        AtomicSkill resultSkill = response.getData().get(0);
        assertEquals("编程语言", resultSkill.getCategory());
        assertEquals("Java", resultSkill.getSubcategory());
        assertEquals(AtomicSkill.DifficultyLevel.BEGINNER, resultSkill.getDifficultyLevel());
        assertEquals(AtomicSkill.SkillType.TECHNICAL, resultSkill.getSkillType());
        assertTrue(resultSkill.getEstimatedHours() >= 30 && resultSkill.getEstimatedHours() <= 50);
        assertTrue(resultSkill.getAverageRating().doubleValue() >= 4.0 && 
                  resultSkill.getAverageRating().doubleValue() <= 5.0);
    }

    @Test
    void testAdvancedSearchAtomicSkills_Exception() {
        // Given
        when(atomicSkillRepository.searchByKeyword(anyString()))
                .thenThrow(new RuntimeException("数据库错误"));

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.advancedSearchAtomicSkills(
                "Java", null, null, null, null,
                null, null, null, null, null,
                "averageRating", "desc", 10);

        // Then
        assertEquals(40000, response.getCode());
        assertNull(response.getData());
        assertTrue(response.getMessage().contains("高级搜索原子技能失败"));
    }

    @Test
    void testGetPopularSkills_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findPopularSkills(any())).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.getPopularSkills(10);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findPopularSkills(any());
    }

    @Test
    void testGetRecommendedSkills_Success() {
        // Given
        List<AtomicSkill> skills = Arrays.asList(testSkill);
        when(atomicSkillRepository.findRecommendedSkills(any())).thenReturn(skills);

        // When
        ApiResponse<List<AtomicSkill>> response = atomicSkillService.getRecommendedSkills(10);

        // Then
        assertEquals(20000, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        verify(atomicSkillRepository).findRecommendedSkills(any());
    }
}
