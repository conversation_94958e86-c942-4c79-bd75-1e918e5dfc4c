import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { AtomicSkill, DifficultyLevel, SkillType, MasteryLevel } from '../../types/atomicSkill';

interface AtomicSkillCardProps {
  skill: AtomicSkill;
  masteryLevel?: MasteryLevel;
  masteryScore?: number;
  showProgress?: boolean;
  showPrerequisites?: boolean;
  onPress?: () => void;
  onMasteryPress?: () => void;
  style?: any;
}

/**
 * 原子技能卡片组件
 * 展示单个原子技能的基本信息、掌握度和相关操作
 */
export const AtomicSkillCard: React.FC<AtomicSkillCardProps> = ({
  skill,
  masteryLevel = MasteryLevel.NONE,
  masteryScore = 0,
  showProgress = true,
  showPrerequisites = false,
  onPress,
  onMasteryPress,
  style
}) => {
  const colors = useThemeColors();

  // 获取难度级别颜色
  const getDifficultyColor = (level: DifficultyLevel) => {
    switch (level) {
      case DifficultyLevel.BEGINNER:
        return colors.success;
      case DifficultyLevel.INTERMEDIATE:
        return colors.warning;
      case DifficultyLevel.ADVANCED:
        return colors.error;
      case DifficultyLevel.EXPERT:
        return colors.purple;
      default:
        return colors.textSecondary;
    }
  };

  // 获取技能类型图标
  const getSkillTypeIcon = (type: SkillType) => {
    switch (type) {
      case SkillType.CORE:
        return 'star';
      case SkillType.SUPPORTING:
        return 'build';
      case SkillType.BONUS:
        return 'add-circle';
      default:
        return 'help-circle';
    }
  };

  // 获取掌握度颜色
  const getMasteryColor = (level: MasteryLevel) => {
    switch (level) {
      case MasteryLevel.NONE:
        return colors.textTertiary;
      case MasteryLevel.BASIC:
        return colors.warning;
      case MasteryLevel.INTERMEDIATE:
        return colors.info;
      case MasteryLevel.ADVANCED:
        return colors.success;
      case MasteryLevel.EXPERT:
        return colors.purple;
      default:
        return colors.textSecondary;
    }
  };

  // 获取掌握度文本
  const getMasteryText = (level: MasteryLevel) => {
    switch (level) {
      case MasteryLevel.NONE:
        return '未掌握';
      case MasteryLevel.BASIC:
        return '基础';
      case MasteryLevel.INTERMEDIATE:
        return '中级';
      case MasteryLevel.ADVANCED:
        return '高级';
      case MasteryLevel.EXPERT:
        return '专家';
      default:
        return '未知';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.surface }, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* 头部信息 */}
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Ionicons
            name={getSkillTypeIcon(skill.skillType)}
            size={16}
            color={colors.primary}
            style={styles.typeIcon}
          />
          <Text style={[styles.title, { color: colors.text }]} numberOfLines={2}>
            {skill.name}
          </Text>
        </View>
        
        {/* 难度标签 */}
        <View style={[
          styles.difficultyBadge,
          { backgroundColor: getDifficultyColor(skill.difficultyLevel) + '20' }
        ]}>
          <Text style={[
            styles.difficultyText,
            { color: getDifficultyColor(skill.difficultyLevel) }
          ]}>
            {skill.difficultyLevel}
          </Text>
        </View>
      </View>

      {/* 技能描述 */}
      {skill.description && (
        <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
          {skill.description}
        </Text>
      )}

      {/* 技能分类和标签 */}
      <View style={styles.categoryRow}>
        <View style={[styles.categoryBadge, { backgroundColor: colors.primary + '15' }]}>
          <Text style={[styles.categoryText, { color: colors.primary }]}>
            {skill.category}
          </Text>
        </View>
        
        {skill.subcategory && (
          <View style={[styles.subcategoryBadge, { backgroundColor: colors.textTertiary + '15' }]}>
            <Text style={[styles.subcategoryText, { color: colors.textSecondary }]}>
              {skill.subcategory}
            </Text>
          </View>
        )}
      </View>

      {/* 掌握度信息 */}
      {showProgress && (
        <TouchableOpacity
          style={styles.masterySection}
          onPress={onMasteryPress}
          activeOpacity={0.7}
        >
          <View style={styles.masteryHeader}>
            <Text style={[styles.masteryLabel, { color: colors.textSecondary }]}>
              掌握度
            </Text>
            <View style={styles.masteryInfo}>
              <View style={[
                styles.masteryBadge,
                { backgroundColor: getMasteryColor(masteryLevel) + '20' }
              ]}>
                <Text style={[
                  styles.masteryText,
                  { color: getMasteryColor(masteryLevel) }
                ]}>
                  {getMasteryText(masteryLevel)}
                </Text>
              </View>
              <Text style={[styles.masteryScore, { color: colors.textSecondary }]}>
                {masteryScore.toFixed(0)}%
              </Text>
            </View>
          </View>
          
          {/* 进度条 */}
          <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: getMasteryColor(masteryLevel),
                  width: `${masteryScore}%`
                }
              ]}
            />
          </View>
        </TouchableOpacity>
      )}

      {/* 底部信息 */}
      <View style={styles.footer}>
        <View style={styles.statsRow}>
          {/* 预计时长 */}
          <View style={styles.statItem}>
            <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
            <Text style={[styles.statText, { color: colors.textSecondary }]}>
              {skill.estimatedHours}h
            </Text>
          </View>
          
          {/* 学习人数 */}
          <View style={styles.statItem}>
            <Ionicons name="people-outline" size={14} color={colors.textSecondary} />
            <Text style={[styles.statText, { color: colors.textSecondary }]}>
              {skill.learnerCount}
            </Text>
          </View>
          
          {/* 评分 */}
          <View style={styles.statItem}>
            <Ionicons name="star-outline" size={14} color={colors.textSecondary} />
            <Text style={[styles.statText, { color: colors.textSecondary }]}>
              {skill.averageRating.toFixed(1)}
            </Text>
          </View>
        </View>

        {/* 前置技能提示 */}
        {showPrerequisites && (
          <TouchableOpacity style={styles.prerequisitesHint}>
            <Ionicons name="link-outline" size={14} color={colors.info} />
            <Text style={[styles.prerequisitesText, { color: colors.info }]}>
              查看前置技能
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: tokens.radius('md'),
    padding: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: tokens.spacing('sm'),
  },
  titleRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: tokens.spacing('sm'),
  },
  typeIcon: {
    marginRight: tokens.spacing('xs'),
  },
  title: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: tokens.fontWeight('semibold'),
    flex: 1,
  },
  difficultyBadge: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
  },
  difficultyText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
    textTransform: 'uppercase',
  },
  description: {
    fontSize: tokens.fontSize('body'),
    lineHeight: 20,
    marginBottom: tokens.spacing('sm'),
  },
  categoryRow: {
    flexDirection: 'row',
    marginBottom: tokens.spacing('md'),
  },
  categoryBadge: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginRight: tokens.spacing('xs'),
  },
  categoryText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  subcategoryBadge: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
  },
  subcategoryText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  masterySection: {
    marginBottom: tokens.spacing('md'),
  },
  masteryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  masteryLabel: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  masteryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  masteryBadge: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginRight: tokens.spacing('xs'),
  },
  masteryText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  masteryScore: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('semibold'),
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    paddingTop: tokens.spacing('sm'),
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing('xs'),
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: tokens.fontSize('caption'),
    marginLeft: tokens.spacing('xs'),
  },
  prerequisitesHint: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  prerequisitesText: {
    fontSize: tokens.fontSize('caption'),
    marginLeft: tokens.spacing('xs'),
  },
});
