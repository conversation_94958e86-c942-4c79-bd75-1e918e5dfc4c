package com.itbook.controller;

import com.itbook.service.MappingRelationshipService;
import com.itbook.dto.MappingRelationshipResult;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 映射关系控制器
 * 提供职业技能与原子技能映射关系管理的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/mapping-relationship")
@Tag(name = "映射关系管理", description = "职业技能与原子技能映射关系建立和管理API")
public class MappingRelationshipController {

    @Autowired
    private MappingRelationshipService mappingRelationshipService;

    /**
     * 建立所有职业技能的映射关系
     */
    @PostMapping("/establish-all")
    @Operation(summary = "建立所有映射关系", description = "为所有职业技能建立与原子技能的映射关系")
    public ApiResponse<MappingRelationshipResult> establishAllMappingRelationships() {
        return mappingRelationshipService.establishAllMappingRelationships();
    }

    /**
     * 验证映射关系质量
     */
    @PostMapping("/validate-quality")
    @Operation(summary = "验证映射质量", description = "验证映射关系的质量和准确性")
    public ApiResponse<Map<String, Object>> validateMappingQuality() {
        return mappingRelationshipService.validateMappingQuality();
    }

    /**
     * 获取映射关系摘要
     */
    @GetMapping("/summary")
    @Operation(summary = "获取映射摘要", description = "获取映射关系的统计摘要信息")
    public ApiResponse<Map<String, Object>> getMappingSummary() {
        return mappingRelationshipService.getMappingSummary();
    }

    /**
     * 检查映射关系状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查映射状态", description = "检查当前映射关系的建立状态")
    public ApiResponse<Map<String, Object>> checkMappingStatus() {
        try {
            ApiResponse<Map<String, Object>> summaryResponse = mappingRelationshipService.getMappingSummary();
            
            if (summaryResponse.getCode() == 20000) {
                Map<String, Object> summary = summaryResponse.getData();
                Long totalMappings = (Long) summary.get("totalMappings");
                String status = (String) summary.get("status");
                
                Map<String, Object> statusInfo = new HashMap<>();
                statusInfo.put("hasMappings", totalMappings != null && totalMappings > 0);
                statusInfo.put("totalMappings", totalMappings != null ? totalMappings : 0);
                statusInfo.put("status", status != null ? status : "UNKNOWN");
                statusInfo.put("message", getMappingStatusMessage(totalMappings, status));
                statusInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                
                return ApiResponse.success(statusInfo);
            } else {
                return ApiResponse.error("获取映射状态失败: " + summaryResponse.getMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("检查映射状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取映射关系详细统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取详细统计", description = "获取映射关系的详细统计信息")
    public ApiResponse<Map<String, Object>> getMappingStatistics() {
        try {
            // 获取基础摘要
            ApiResponse<Map<String, Object>> summaryResponse = mappingRelationshipService.getMappingSummary();
            if (summaryResponse.getCode() != 20000) {
                return summaryResponse;
            }
            
            // 获取质量验证
            ApiResponse<Map<String, Object>> qualityResponse = mappingRelationshipService.validateMappingQuality();
            
            Map<String, Object> statistics = summaryResponse.getData();
            
            if (qualityResponse.getCode() == 20000) {
                Map<String, Object> quality = qualityResponse.getData();
                statistics.putAll(quality);
            }
            
            // 添加额外的统计信息
            statistics.put("lastChecked", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            return ApiResponse.error("获取映射统计失败: " + e.getMessage());
        }
    }

    /**
     * 重建映射关系
     */
    @PostMapping("/rebuild")
    @Operation(summary = "重建映射关系", description = "清理现有映射关系并重新建立")
    public ApiResponse<MappingRelationshipResult> rebuildMappingRelationships() {
        try {
            // 直接调用建立映射关系的方法，该方法内部会清理现有映射
            return mappingRelationshipService.establishAllMappingRelationships();
        } catch (Exception e) {
            return ApiResponse.error("重建映射关系失败: " + e.getMessage());
        }
    }

    /**
     * 获取映射关系健康检查
     */
    @GetMapping("/health-check")
    @Operation(summary = "健康检查", description = "检查映射关系系统的健康状态")
    public ApiResponse<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("service", "MappingRelationshipService");
            health.put("status", "HEALTHY");
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            health.put("version", "1.0.0");
            health.put("description", "映射关系服务运行正常");

            return ApiResponse.success(health);
        } catch (Exception e) {
            Map<String, Object> health = new HashMap<>();
            health.put("service", "MappingRelationshipService");
            health.put("status", "UNHEALTHY");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return ApiResponse.error("健康检查失败");
        }
    }

    /**
     * 获取映射关系配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取配置信息", description = "获取映射关系的配置参数")
    public ApiResponse<Map<String, Object>> getMappingConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("highConfidenceThreshold", 0.8);
            config.put("mediumConfidenceThreshold", 0.6);
            config.put("lowConfidenceThreshold", 0.4);
            config.put("autoCleanExistingMappings", true);
            config.put("batchSize", 100);
            config.put("maxMappingsPerSkill", 10);
            config.put("enableQualityValidation", true);
            config.put("description", "映射关系建立的配置参数");
            
            return ApiResponse.success(config);
        } catch (Exception e) {
            return ApiResponse.error("获取配置信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取映射状态消息
     */
    private String getMappingStatusMessage(Long totalMappings, String status) {
        if (totalMappings == null || totalMappings == 0) {
            return "尚未建立映射关系，请先执行映射关系建立操作";
        }
        
        if ("COMPLETED".equals(status)) {
            return "映射关系已建立，共" + totalMappings + "个映射";
        }
        
        return "映射关系状态: " + status + "，共" + totalMappings + "个映射";
    }
}
