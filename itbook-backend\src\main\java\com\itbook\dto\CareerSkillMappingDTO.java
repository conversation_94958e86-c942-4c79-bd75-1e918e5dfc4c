package com.itbook.dto;

import com.itbook.entity.CareerSkillMapping;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 职业技能映射DTO
 */
public class CareerSkillMappingDTO {
    private Long id;
    private Long careerSkillId;
    private Long atomicSkillId;
    private String careerSkillName;
    private String atomicSkillName;
    private String mappingType;
    private Double relevanceScore;
    private Double importanceWeight;
    private String proficiencyLevel;
    private Boolean isRequired;
    private String mappingSource;
    private Double confidenceScore;
    private String validationStatus;
    private String description;
    private List<String> tags;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public CareerSkillMappingDTO() {}

    public CareerSkillMappingDTO(CareerSkillMapping mapping) {
        this.id = mapping.getId();
        this.careerSkillId = mapping.getCareerSkillId();
        this.atomicSkillId = mapping.getAtomicSkillId();
        this.mappingType = mapping.getMappingType().name();
        this.relevanceScore = mapping.getRelevanceScore();
        this.importanceWeight = mapping.getImportanceWeight();
        this.proficiencyLevel = mapping.getProficiencyLevel().name();
        this.isRequired = mapping.getIsRequired();
        this.mappingSource = mapping.getMappingSource().name();
        this.confidenceScore = mapping.getConfidenceScore();
        this.validationStatus = mapping.getValidationStatus().name();
        this.description = mapping.getDescription();
        this.isActive = mapping.getIsActive();
        this.createdAt = mapping.getCreatedAt();
        this.updatedAt = mapping.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getCareerSkillId() { return careerSkillId; }
    public void setCareerSkillId(Long careerSkillId) { this.careerSkillId = careerSkillId; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public String getCareerSkillName() { return careerSkillName; }
    public void setCareerSkillName(String careerSkillName) { this.careerSkillName = careerSkillName; }

    public String getAtomicSkillName() { return atomicSkillName; }
    public void setAtomicSkillName(String atomicSkillName) { this.atomicSkillName = atomicSkillName; }

    public String getMappingType() { return mappingType; }
    public void setMappingType(String mappingType) { this.mappingType = mappingType; }

    public Double getRelevanceScore() { return relevanceScore; }
    public void setRelevanceScore(Double relevanceScore) { this.relevanceScore = relevanceScore; }

    public Double getImportanceWeight() { return importanceWeight; }
    public void setImportanceWeight(Double importanceWeight) { this.importanceWeight = importanceWeight; }

    public String getProficiencyLevel() { return proficiencyLevel; }
    public void setProficiencyLevel(String proficiencyLevel) { this.proficiencyLevel = proficiencyLevel; }

    public Boolean getIsRequired() { return isRequired; }
    public void setIsRequired(Boolean isRequired) { this.isRequired = isRequired; }

    public String getMappingSource() { return mappingSource; }
    public void setMappingSource(String mappingSource) { this.mappingSource = mappingSource; }

    public Double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(Double confidenceScore) { this.confidenceScore = confidenceScore; }

    public String getValidationStatus() { return validationStatus; }
    public void setValidationStatus(String validationStatus) { this.validationStatus = validationStatus; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
