import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { DifficultyLevel, SkillType, SkillStatus, SkillSearchFilter } from '../../types/atomicSkill';

interface SkillSearchProps {
  onSearch: (filter: SkillSearchFilter) => void;
  onClear?: () => void;
  placeholder?: string;
  showAdvancedFilter?: boolean;
  initialFilter?: SkillSearchFilter;
  categories?: string[];
  style?: any;
}

/**
 * 原子技能搜索组件
 * 支持关键词搜索和高级筛选功能
 */
export const SkillSearch: React.FC<SkillSearchProps> = ({
  onSearch,
  onClear,
  placeholder = '搜索技能...',
  showAdvancedFilter = true,
  initialFilter = {},
  categories = [],
  style,
}) => {
  const colors = useThemeColors();
  const [keyword, setKeyword] = useState(initialFilter.keyword || '');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filter, setFilter] = useState<SkillSearchFilter>(initialFilter);

  // 处理搜索
  const handleSearch = useCallback(() => {
    const searchFilter: SkillSearchFilter = {
      ...filter,
      keyword: keyword.trim() || undefined,
    };
    onSearch(searchFilter);
  }, [keyword, filter, onSearch]);

  // 处理清除
  const handleClear = useCallback(() => {
    setKeyword('');
    setFilter({});
    onClear?.();
  }, [onClear]);

  // 处理高级筛选
  const handleAdvancedFilter = useCallback((newFilter: SkillSearchFilter) => {
    setFilter(newFilter);
    setShowFilterModal(false);
    const searchFilter: SkillSearchFilter = {
      ...newFilter,
      keyword: keyword.trim() || undefined,
    };
    onSearch(searchFilter);
  }, [keyword, onSearch]);

  // 检查是否有活动的筛选条件
  const hasActiveFilters = Object.keys(filter).some(key => 
    filter[key as keyof SkillSearchFilter] !== undefined
  );

  return (
    <View style={[styles.container, style]}>
      {/* 搜索框 */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
        <Ionicons name="search-outline" size={20} color={colors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder={placeholder}
          placeholderTextColor={colors.textTertiary}
          value={keyword}
          onChangeText={setKeyword}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
        
        {/* 清除按钮 */}
        {(keyword || hasActiveFilters) && (
          <TouchableOpacity onPress={handleClear} activeOpacity={0.7}>
            <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
        
        {/* 高级筛选按钮 */}
        {showAdvancedFilter && (
          <TouchableOpacity
            style={[
              styles.filterButton,
              hasActiveFilters && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => setShowFilterModal(true)}
            activeOpacity={0.7}
          >
            <Ionicons
              name="options-outline"
              size={20}
              color={hasActiveFilters ? colors.primary : colors.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* 活动筛选条件显示 */}
      {hasActiveFilters && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.activeFiltersContainer}
          contentContainerStyle={styles.activeFiltersContent}
        >
          {filter.category && (
            <View style={[styles.filterTag, { backgroundColor: colors.primary + '20' }]}>
              <Text style={[styles.filterTagText, { color: colors.primary }]}>
                分类: {filter.category}
              </Text>
            </View>
          )}
          {filter.difficultyLevel && (
            <View style={[styles.filterTag, { backgroundColor: colors.warning + '20' }]}>
              <Text style={[styles.filterTagText, { color: colors.warning }]}>
                难度: {filter.difficultyLevel}
              </Text>
            </View>
          )}
          {filter.skillType && (
            <View style={[styles.filterTag, { backgroundColor: colors.info + '20' }]}>
              <Text style={[styles.filterTagText, { color: colors.info }]}>
                类型: {filter.skillType}
              </Text>
            </View>
          )}
          {filter.maxEstimatedHours && (
            <View style={[styles.filterTag, { backgroundColor: colors.success + '20' }]}>
              <Text style={[styles.filterTagText, { color: colors.success }]}>
                时长: ≤{filter.maxEstimatedHours}h
              </Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* 高级筛选模态框 */}
      <AdvancedFilterModal
        visible={showFilterModal}
        filter={filter}
        categories={categories}
        onApply={handleAdvancedFilter}
        onClose={() => setShowFilterModal(false)}
      />
    </View>
  );
};

// 高级筛选模态框组件
interface AdvancedFilterModalProps {
  visible: boolean;
  filter: SkillSearchFilter;
  categories: string[];
  onApply: (filter: SkillSearchFilter) => void;
  onClose: () => void;
}

const AdvancedFilterModal: React.FC<AdvancedFilterModalProps> = ({
  visible,
  filter,
  categories,
  onApply,
  onClose,
}) => {
  const colors = useThemeColors();
  const [localFilter, setLocalFilter] = useState<SkillSearchFilter>(filter);

  useEffect(() => {
    setLocalFilter(filter);
  }, [filter]);

  const handleApply = () => {
    onApply(localFilter);
  };

  const handleReset = () => {
    setLocalFilter({});
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        {/* 模态框头部 */}
        <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose} activeOpacity={0.7}>
            <Text style={[styles.modalCancelText, { color: colors.textSecondary }]}>
              取消
            </Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            高级筛选
          </Text>
          <TouchableOpacity onPress={handleApply} activeOpacity={0.7}>
            <Text style={[styles.modalApplyText, { color: colors.primary }]}>
              应用
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* 分类筛选 */}
          <FilterSection
            title="技能分类"
            options={categories}
            selectedValue={localFilter.category}
            onSelect={(value) => setLocalFilter({ ...localFilter, category: value })}
          />

          {/* 难度级别筛选 */}
          <FilterSection
            title="难度级别"
            options={Object.values(DifficultyLevel)}
            selectedValue={localFilter.difficultyLevel}
            onSelect={(value) => setLocalFilter({ ...localFilter, difficultyLevel: value as DifficultyLevel })}
          />

          {/* 技能类型筛选 */}
          <FilterSection
            title="技能类型"
            options={Object.values(SkillType)}
            selectedValue={localFilter.skillType}
            onSelect={(value) => setLocalFilter({ ...localFilter, skillType: value as SkillType })}
          />

          {/* 重置按钮 */}
          <TouchableOpacity
            style={[styles.resetButton, { backgroundColor: colors.surface }]}
            onPress={handleReset}
            activeOpacity={0.7}
          >
            <Text style={[styles.resetButtonText, { color: colors.textSecondary }]}>
              重置所有筛选条件
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </Modal>
  );
};

// 筛选区域组件
interface FilterSectionProps {
  title: string;
  options: string[];
  selectedValue?: string;
  onSelect: (value?: string) => void;
}

const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  options,
  selectedValue,
  onSelect,
}) => {
  const colors = useThemeColors();

  return (
    <View style={styles.filterSection}>
      <Text style={[styles.filterSectionTitle, { color: colors.text }]}>
        {title}
      </Text>
      <View style={styles.filterOptions}>
        <TouchableOpacity
          style={[
            styles.filterOption,
            { backgroundColor: colors.surface },
            !selectedValue && { backgroundColor: colors.primary + '20' }
          ]}
          onPress={() => onSelect(undefined)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.filterOptionText,
            { color: !selectedValue ? colors.primary : colors.textSecondary }
          ]}>
            全部
          </Text>
        </TouchableOpacity>
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.filterOption,
              { backgroundColor: colors.surface },
              selectedValue === option && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => onSelect(option)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.filterOptionText,
              { color: selectedValue === option ? colors.primary : colors.textSecondary }
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: tokens.spacing('md'),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('lg'),
    marginHorizontal: tokens.spacing('md'),
  },
  searchInput: {
    flex: 1,
    fontSize: tokens.fontSize('body'),
    marginLeft: tokens.spacing('sm'),
    marginRight: tokens.spacing('sm'),
  },
  filterButton: {
    padding: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginLeft: tokens.spacing('xs'),
  },
  activeFiltersContainer: {
    marginTop: tokens.spacing('sm'),
    marginHorizontal: tokens.spacing('md'),
  },
  activeFiltersContent: {
    paddingRight: tokens.spacing('md'),
  },
  filterTag: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginRight: tokens.spacing('xs'),
  },
  filterTagText: {
    fontSize: tokens.fontSize('caption'),
    fontWeight: tokens.fontWeight('medium'),
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('md'),
    borderBottomWidth: 1,
  },
  modalCancelText: {
    fontSize: tokens.fontSize('body'),
  },
  modalTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('semibold'),
  },
  modalApplyText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('semibold'),
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: tokens.spacing('md'),
  },
  filterSection: {
    marginVertical: tokens.spacing('md'),
  },
  filterSectionTitle: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: tokens.fontWeight('semibold'),
    marginBottom: tokens.spacing('sm'),
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('sm'),
  },
  filterOption: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
  },
  filterOptionText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
  resetButton: {
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    marginVertical: tokens.spacing('lg'),
  },
  resetButtonText: {
    fontSize: tokens.fontSize('body'),
    fontWeight: tokens.fontWeight('medium'),
  },
});
