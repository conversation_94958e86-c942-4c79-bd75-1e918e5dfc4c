package com.itbook.controller;

import com.itbook.entity.CareerSkillMapping;
import com.itbook.entity.AtomicSkill;
import com.itbook.service.CareerSkillMappingService;
import com.itbook.dto.CareerSkillMappingDTO;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 职业技能映射控制器
 * 提供职业技能与原子技能映射关系相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/career-skill-mappings")
@Tag(name = "职业技能映射管理", description = "职业技能与原子技能映射关系的管理API")
public class CareerSkillMappingController {

    @Autowired
    private CareerSkillMappingService mappingService;

    /**
     * 创建技能映射
     */
    @PostMapping
    @Operation(summary = "创建技能映射", description = "创建新的职业技能与原子技能映射关系")
    public ApiResponse<CareerSkillMapping> createMapping(@Valid @RequestBody CareerSkillMappingDTO mappingDTO) {
        return mappingService.createMapping(mappingDTO);
    }

    /**
     * 更新技能映射
     */
    @PutMapping("/{mappingId}")
    @Operation(summary = "更新技能映射", description = "更新指定ID的技能映射关系")
    public ApiResponse<CareerSkillMapping> updateMapping(
            @Parameter(description = "映射ID") @PathVariable Long mappingId,
            @Valid @RequestBody CareerSkillMappingDTO mappingDTO) {
        return mappingService.updateMapping(mappingId, mappingDTO);
    }

    /**
     * 删除技能映射
     */
    @DeleteMapping("/{mappingId}")
    @Operation(summary = "删除技能映射", description = "删除指定ID的技能映射关系")
    public ApiResponse<Void> deleteMapping(
            @Parameter(description = "映射ID") @PathVariable Long mappingId) {
        return mappingService.deleteMapping(mappingId);
    }

    /**
     * 根据职业技能ID查询映射关系
     */
    @GetMapping("/career-skill/{careerSkillId}")
    @Operation(summary = "查询职业技能映射", description = "根据职业技能ID查询映射关系")
    public ApiResponse<List<CareerSkillMapping>> getMappingsByCareerSkill(
            @Parameter(description = "职业技能ID") @PathVariable Long careerSkillId) {
        return mappingService.getMappingsByCareerSkill(careerSkillId);
    }

    /**
     * 根据原子技能ID查询映射关系
     */
    @GetMapping("/atomic-skill/{atomicSkillId}")
    @Operation(summary = "查询原子技能映射", description = "根据原子技能ID查询映射关系")
    public ApiResponse<List<CareerSkillMapping>> getMappingsByAtomicSkill(
            @Parameter(description = "原子技能ID") @PathVariable Long atomicSkillId) {
        return mappingService.getMappingsByAtomicSkill(atomicSkillId);
    }

    /**
     * 根据职业目标ID查询相关的原子技能映射
     */
    @GetMapping("/career-goal/{careerGoalId}")
    @Operation(summary = "查询职业目标技能映射", description = "根据职业目标ID查询相关的原子技能映射")
    public ApiResponse<List<CareerSkillMapping>> getMappingsByCareerGoal(
            @Parameter(description = "职业目标ID") @PathVariable Long careerGoalId) {
        return mappingService.getMappingsByCareerGoal(careerGoalId);
    }

    /**
     * 根据职业目标ID获取推荐的原子技能
     */
    @GetMapping("/career-goal/{careerGoalId}/recommended-skills")
    @Operation(summary = "获取推荐原子技能", description = "根据职业目标ID获取推荐的原子技能")
    public ApiResponse<List<AtomicSkill>> getRecommendedAtomicSkills(
            @Parameter(description = "职业目标ID") @PathVariable Long careerGoalId,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") int limit) {
        return mappingService.getRecommendedAtomicSkills(careerGoalId, limit);
    }

    /**
     * 查询关键技能映射
     */
    @GetMapping("/critical")
    @Operation(summary = "查询关键技能映射", description = "查询重要程度为关键的技能映射")
    public ApiResponse<List<CareerSkillMapping>> getCriticalMappings() {
        return mappingService.getCriticalMappings();
    }

    /**
     * 查询高置信度映射
     */
    @GetMapping("/high-confidence")
    @Operation(summary = "查询高置信度映射", description = "查询置信度高于指定阈值的技能映射")
    public ApiResponse<List<CareerSkillMapping>> getHighConfidenceMappings(
            @Parameter(description = "最小置信度") @RequestParam(defaultValue = "0.8") BigDecimal minConfidence) {
        return mappingService.getHighConfidenceMappings(minConfidence);
    }

    /**
     * 查询需要验证的映射
     */
    @GetMapping("/need-verification")
    @Operation(summary = "查询需要验证的映射", description = "查询置信度低于指定阈值需要验证的技能映射")
    public ApiResponse<List<CareerSkillMapping>> getMappingsNeedingVerification(
            @Parameter(description = "最大置信度") @RequestParam(defaultValue = "0.6") BigDecimal maxConfidence) {
        return mappingService.getMappingsNeedingVerification(maxConfidence);
    }

    /**
     * 获取映射统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取映射统计", description = "获取技能映射的统计信息")
    public ApiResponse<Map<String, Object>> getMappingStatistics() {
        return mappingService.getMappingStatistics();
    }

    /**
     * 批量创建技能映射
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建技能映射", description = "批量创建技能映射关系")
    public ApiResponse<List<CareerSkillMapping>> createMappingsBatch(
            @Valid @RequestBody List<CareerSkillMappingDTO> mappingDTOs) {
        return mappingService.createMappingsBatch(mappingDTOs);
    }

    /**
     * 自动生成技能映射
     */
    @PostMapping("/career-skill/{careerSkillId}/auto-generate")
    @Operation(summary = "自动生成技能映射", description = "为指定职业技能自动生成与原子技能的映射关系")
    public ApiResponse<List<CareerSkillMapping>> generateAutomaticMappings(
            @Parameter(description = "职业技能ID") @PathVariable Long careerSkillId) {
        return mappingService.generateAutomaticMappings(careerSkillId);
    }
}
