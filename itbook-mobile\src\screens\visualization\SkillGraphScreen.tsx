import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { apiService } from '../../services/ApiService';

/**
 * 技能图谱可视化页面
 * 展示完整的技能关系图谱和统计分析
 */
const SkillGraphScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<'graph' | 'statistics'>('graph');
  const [error, setError] = useState<string | null>(null);
  const [selectedLayout, setSelectedLayout] = useState<'force' | 'hierarchy' | 'circular'>('force');
  const [layoutLoading, setLayoutLoading] = useState(false);
  const [skillsMap, setSkillsMap] = useState<{[key: number]: any}>({});
  const [skillsLoading, setSkillsLoading] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);

  // 加载技能详细信息
  const loadSkillsData = async () => {
    try {
      setSkillsLoading(true);
      console.log('🔍 开始获取技能详细信息...');

      const response = await apiService.get('/atomic-skills', {
        params: { size: 100 } // 获取足够多的技能数据
      });

      if (response.code === 20000 && response.data && response.data.content) {
        const skillsData = response.data.content;
        const skillsMapping: {[key: number]: any} = {};

        skillsData.forEach((skill: any) => {
          skillsMapping[skill.id] = {
            id: skill.id,
            name: skill.name,
            description: skill.description,
            category: skill.category,
            subcategory: skill.subcategory,
            difficultyLevel: skill.difficultyLevel,
            estimatedHours: skill.estimatedHours,
            skillType: skill.skillType
          };
        });

        setSkillsMap(skillsMapping);
        console.log(`✅ 技能信息加载完成，共${Object.keys(skillsMapping).length}个技能`);
      }
    } catch (error) {
      console.error('❌ 加载技能详细信息失败:', error);
    } finally {
      setSkillsLoading(false);
    }
  };

  // 加载图谱数据
  const loadGraphData = async () => {
    try {
      setError(null);

      // 获取技能图谱统计信息
      const statsResponse = await apiService.get('/skill-graph/statistics');
      if (statsResponse.code === 20000) {
        setStatistics(statsResponse.data);
      }

      // 获取技能聚类信息
      const clustersResponse = await apiService.get('/skill-graph/clusters');
      if (clustersResponse.code === 20000) {
        setClusters(clustersResponse.data);
      }

    } catch (error) {
      console.error('加载图谱数据失败:', error);
      setError('加载图谱数据失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadGraphData();
  };

  // 处理布局切换
  const handleLayoutChange = async (layout: 'force' | 'hierarchy' | 'circular') => {
    if (layout === selectedLayout) return;

    setLayoutLoading(true);
    setSelectedLayout(layout);

    // 模拟布局计算时间
    await new Promise(resolve => setTimeout(resolve, 800));

    setLayoutLoading(false);

    // 显示布局切换成功提示
    const layoutNames = {
      force: '力导向布局',
      hierarchy: '层次布局',
      circular: '圆形布局'
    };

    console.log(`🎨 图谱布局已切换为: ${layoutNames[layout]}`);
  };

  // 处理聚类点击
  const handleClusterPress = (clusterId: number) => {
    Alert.alert(
      '技能聚类',
      `聚类ID: ${clusterId}\n点击查看该聚类的详细信息`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => console.log('查看聚类详情:', clusterId) }
      ]
    );
  };

  // 获取聚类颜色
  const getClusterColor = (category: string) => {
    const colorMap: { [key: string]: string } = {
      'FRONTEND': '#007AFF',
      'BACKEND': '#34C759',
      'DATABASE': '#FF9500',
      'DEVOPS': '#AF52DE',
      'MOBILE': '#FF2D92',
      'AI': '#5AC8FA',
    };
    return colorMap[category] || '#8E8E93';
  };

  // 获取聚类图标
  const getClusterIcon = (category: string) => {
    const iconMap: { [key: string]: any } = {
      'frontend': 'desktop-outline',
      'backend': 'server-outline',
      'database': 'library-outline',
      'devops': 'cloud-outline',
      'mobile': 'phone-portrait-outline',
      'ai': 'bulb-outline',
    };
    return iconMap[category.toLowerCase()] || 'code-outline';
  };

  // 获取技能名称
  const getSkillName = (skillId: number) => {
    const skill = skillsMap[skillId];
    return skill ? skill.name : `技能 ${skillId}`;
  };

  // 获取技能描述
  const getSkillDescription = (skillId: number) => {
    const skill = skillsMap[skillId];
    if (!skill) return `技能ID: ${skillId}`;

    return `技能名称: ${skill.name}\n描述: ${skill.description}\n分类: ${skill.category}\n难度: ${skill.difficultyLevel}\n预计学时: ${skill.estimatedHours}h`;
  };

  // 获取友好的分类名称
  const getFriendlyCategoryName = (category: string) => {
    const categoryMap: { [key: string]: string } = {
      'frontend': '前端开发',
      'backend': '后端开发',
      'database': '数据库',
      'devops': '运维部署',
      'mobile': '移动开发',
      'ai': '人工智能',
    };
    return categoryMap[category.toLowerCase()] || category.toUpperCase();
  };

  // 初始化加载
  useEffect(() => {
    loadGraphData();
    loadSkillsData();
  }, []);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
            加载技能图谱中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadGraphData}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.backgroundPrimary }]}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.backgroundPrimary, borderBottomColor: colors.border }]}>
        <View style={styles.titleContainer}>
          <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
            技能图谱
          </Text>
          <TouchableOpacity
            style={styles.helpButton}
            onPress={() => setShowHelpModal(true)}
            activeOpacity={0.7}
          >
            <Ionicons name="help-circle-outline" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={[styles.tabContainer, { backgroundColor: colors.backgroundPrimary }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'graph' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('graph')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'graph' ? colors.primary : colors.textSecondary }
          ]}>
            图谱视图
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'statistics' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('statistics')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'statistics' ? colors.primary : colors.textSecondary }
          ]}>
            统计分析
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'graph' && (
          <View style={styles.graphContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              技能关系图谱
            </Text>

            {/* 图谱控制面板 */}
            <View style={[styles.controlPanel, { backgroundColor: colors.backgroundSecondary }]}>
              <View style={styles.controlHeader}>
                <View style={styles.controlTitleContainer}>
                  <Ionicons name="options-outline" size={20} color={colors.primary} />
                  <Text style={[styles.controlTitle, { color: colors.textPrimary }]}>
                    布局控制
                  </Text>
                </View>
                <Text style={[styles.controlSubtitle, { color: colors.textSecondary }]}>
                  选择图谱布局方式
                </Text>
              </View>
              <View style={styles.controlButtons}>
                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'force' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'force' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'force' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('force')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="git-network-outline"
                      size={20}
                      color={selectedLayout === 'force' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'force' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      力导向布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'force' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      自然聚集
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'hierarchy' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'hierarchy' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'hierarchy' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('hierarchy')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="list-outline"
                      size={20}
                      color={selectedLayout === 'hierarchy' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'hierarchy' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      层次布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'hierarchy' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      分层展示
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'circular' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'circular' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'circular' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('circular')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="radio-button-on-outline"
                      size={20}
                      color={selectedLayout === 'circular' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'circular' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      圆形布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'circular' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      环形排列
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* 简化的图谱可视化展示 */}
            <View style={[styles.graphVisualization, { backgroundColor: colors.backgroundSecondary }]}>
              <View style={styles.graphHeader}>
                <Text style={[styles.graphTitle, { color: colors.textPrimary }]}>
                  技能关系网络
                </Text>
                {layoutLoading && (
                  <View style={styles.layoutLoadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={[styles.layoutLoadingText, { color: colors.textSecondary }]}>
                      布局计算中...
                    </Text>
                  </View>
                )}
              </View>

              {/* 技能聚类展示 */}
              {clusters.map((cluster, index) => (
                <View key={cluster.category} style={[styles.clusterContainer, { backgroundColor: colors.backgroundPrimary }]}>
                  <View style={styles.clusterHeader}>
                    <View style={[styles.clusterIconContainer, { backgroundColor: getClusterColor(cluster.category) + '15' }]}>
                      <View style={[styles.clusterIcon, { backgroundColor: getClusterColor(cluster.category) }]}>
                        <Ionicons
                          name={getClusterIcon(cluster.category)}
                          size={18}
                          color={colors.backgroundPrimary}
                        />
                      </View>
                    </View>
                    <View style={styles.clusterInfo}>
                      <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                        {getFriendlyCategoryName(cluster.category)} 技能组
                      </Text>
                      <View style={styles.clusterMeta}>
                        <Text style={[styles.clusterCount, { color: colors.textSecondary }]}>
                          {cluster.skillIds?.length || 0} 个技能
                        </Text>
                        <View style={styles.clusterDivider} />
                        <Text style={[styles.clusterCohesion, { color: getClusterColor(cluster.category) }]}>
                          内聚度 {((cluster.cohesion || 0) * 100).toFixed(1)}%
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.skillNodes}>
                    {(cluster.skillIds || []).slice(0, 6).map((skillId: number) => (
                      <TouchableOpacity
                        key={skillId}
                        style={[
                          styles.skillNode,
                          {
                            backgroundColor: getClusterColor(cluster.category) + '10',
                            borderColor: getClusterColor(cluster.category) + '30',
                          }
                        ]}
                        onPress={() => Alert.alert('技能详情', getSkillDescription(skillId))}
                        activeOpacity={0.7}
                      >
                        <Text style={[styles.skillNodeText, { color: colors.textPrimary }]}>
                          {getSkillName(skillId)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                    {(cluster.skillIds?.length || 0) > 6 && (
                      <View style={[
                        styles.moreSkills,
                        {
                          backgroundColor: colors.backgroundSecondary,
                          borderColor: colors.border,
                        }
                      ]}>
                        <Text style={[styles.moreSkillsText, { color: colors.textSecondary }]}>
                          +{(cluster.skillIds?.length || 0) - 6}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}

              {(clusters?.length || 0) === 0 && !skillsLoading && (
                <View style={styles.emptyStateContainer}>
                  <Ionicons name="analytics-outline" size={64} color={colors.textSecondary} />
                  <Text style={[styles.emptyStateTitle, { color: colors.textPrimary }]}>
                    暂无技能组数据
                  </Text>
                  <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                    技能图谱正在构建中，请稍后再试
                  </Text>
                </View>
              )}

              {skillsLoading && (
                <View style={styles.skillsLoadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={[styles.skillsLoadingText, { color: colors.textSecondary }]}>
                    正在加载技能详细信息...
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {selectedTab === 'statistics' && statistics && (
          <View style={styles.statisticsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              图谱统计信息
            </Text>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>技能节点总数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalNodes}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>关系连接数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalEdges}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>平均度数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.averageDegree?.toFixed(2)}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>连通组件</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.stronglyConnectedComponents}</Text>
            </View>

            {(clusters?.length || 0) > 0 && (
              <View style={styles.clustersSection}>
                <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
                  技能聚类
                </Text>
                {clusters.map((cluster, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.clusterCard, { backgroundColor: colors.backgroundSecondary }]}
                    onPress={() => handleClusterPress(cluster.clusterId)}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                      {cluster.category.toUpperCase()} 集群
                    </Text>
                    <Text style={[styles.clusterDescription, { color: colors.textSecondary }]}>
                      {cluster.skillIds?.length || 0} 个技能 · 内聚度 {((cluster.cohesion || 0) * 100).toFixed(1)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        )}

        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>

      {/* 帮助说明模态框 */}
      <Modal
        visible={showHelpModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowHelpModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.backgroundPrimary }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.textPrimary }]}>
                技能图谱使用指南
              </Text>
              <TouchableOpacity
                onPress={() => setShowHelpModal(false)}
                activeOpacity={0.7}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <View style={styles.helpSection}>
                <Text style={[styles.helpSectionTitle, { color: colors.primary }]}>
                  💡 什么是技能组？
                </Text>
                <Text style={[styles.helpSectionText, { color: colors.textSecondary }]}>
                  技能组是将相关技能按照领域分类组织的集合。例如"前端开发"技能组包含JavaScript、React、CSS等相关技能，帮助您更好地理解技能之间的关联性。
                </Text>
              </View>

              <View style={styles.helpSection}>
                <Text style={[styles.helpSectionTitle, { color: colors.primary }]}>
                  🎯 布局模式说明
                </Text>
                <Text style={[styles.helpSectionText, { color: colors.textSecondary }]}>
                  • 力导向布局：技能按照关联度自然聚集，关系越密切的技能距离越近{'\n'}
                  • 层次布局：按照技能难度和依赖关系分层展示，便于理解学习路径{'\n'}
                  • 圆形布局：技能按照分类环形排列，视觉效果更加对称美观
                </Text>
              </View>

              <View style={styles.helpSection}>
                <Text style={[styles.helpSectionTitle, { color: colors.primary }]}>
                  🔍 如何使用？
                </Text>
                <Text style={[styles.helpSectionText, { color: colors.textSecondary }]}>
                  • 点击技能节点查看详细信息，包括技能描述、难度等级和预计学时{'\n'}
                  • 切换不同的布局模式来从不同角度理解技能关系{'\n'}
                  • 查看统计分析了解整体技能图谱的结构特征{'\n'}
                  • 内聚度表示技能组内部技能的关联紧密程度，数值越高关联性越强
                </Text>
              </View>

              <View style={styles.helpSection}>
                <Text style={[styles.helpSectionTitle, { color: colors.primary }]}>
                  📊 图例说明
                </Text>
                <View style={styles.legendContainer}>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendIcon, { backgroundColor: '#007AFF' }]}>
                      <Ionicons name="desktop-outline" size={16} color="white" />
                    </View>
                    <Text style={[styles.legendText, { color: colors.textSecondary }]}>前端开发</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendIcon, { backgroundColor: '#34C759' }]}>
                      <Ionicons name="server-outline" size={16} color="white" />
                    </View>
                    <Text style={[styles.legendText, { color: colors.textSecondary }]}>后端开发</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendIcon, { backgroundColor: '#FF9500' }]}>
                      <Ionicons name="library-outline" size={16} color="white" />
                    </View>
                    <Text style={[styles.legendText, { color: colors.textSecondary }]}>数据库</Text>
                  </View>
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalCloseButton, { backgroundColor: colors.primary }]}
                onPress={() => setShowHelpModal(false)}
                activeOpacity={0.7}
              >
                <Text style={[styles.modalCloseButtonText, { color: colors.backgroundPrimary }]}>
                  我知道了
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('semiBold'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
  },
  tabButton: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  content: {
    flex: 1,
  },
  graphContainer: {
    padding: tokens.spacing('md'),
  },
  statisticsContainer: {
    padding: tokens.spacing('md'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  sectionDescription: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginTop: tokens.spacing('lg'),
  },
  statCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  statValue: {
    fontSize: tokens.fontSize('xl'),
    fontWeight: tokens.fontWeight('bold'),
  },
  clustersSection: {
    marginTop: tokens.spacing('lg'),
  },
  clusterCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterDescription: {
    fontSize: tokens.fontSize('sm'),
  },
  // 新增样式
  controlPanel: {
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    marginBottom: tokens.spacing('lg'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  controlHeader: {
    marginBottom: tokens.spacing('md'),
  },
  controlTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  controlTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginLeft: tokens.spacing('sm'),
  },
  controlSubtitle: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  controlButtons: {
    flexDirection: 'row',
    gap: tokens.spacing('md'),
  },
  controlButton: {
    flex: 1,
    borderRadius: tokens.radius('md'),
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  controlButtonActive: {
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  controlButtonContent: {
    alignItems: 'center',
    padding: tokens.spacing('md'),
  },
  controlButtonText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginTop: tokens.spacing('xs'),
    textAlign: 'center',
  },
  controlButtonDesc: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
    marginTop: tokens.spacing('xs') / 2,
    textAlign: 'center',
  },
  graphVisualization: {
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    minHeight: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  graphHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  graphTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  layoutLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  layoutLoadingText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('sm'),
  },
  clusterContainer: {
    marginBottom: tokens.spacing('lg'),
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('lg'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  clusterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  clusterIconContainer: {
    padding: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginRight: tokens.spacing('md'),
  },
  clusterIcon: {
    width: 32,
    height: 32,
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  clusterInfo: {
    flex: 1,
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clusterCount: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  clusterDivider: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#ccc',
    marginHorizontal: tokens.spacing('sm'),
  },
  clusterCohesion: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  skillNodes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('sm'),
  },
  skillNode: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  skillNodeText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  moreSkills: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  moreSkillsText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('bold'),
  },
  emptyText: {
    textAlign: 'center',
    fontSize: tokens.fontSize('sm'),
    marginTop: tokens.spacing('lg'),
  },
  // 空状态样式
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('xl'),
  },
  emptyStateTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  emptyStateText: {
    fontSize: tokens.fontSize('body-sm'),
    textAlign: 'center',
    lineHeight: 20,
  },
  // 技能加载状态样式
  skillsLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('md'),
  },
  skillsLoadingText: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('sm'),
  },
  // 标题容器和帮助按钮样式
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  helpButton: {
    marginLeft: tokens.spacing('sm'),
    padding: tokens.spacing('xs'),
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  modalContent: {
    borderRadius: tokens.radius('lg'),
    maxHeight: '80%',
    width: '100%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  modalTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
  },
  modalBody: {
    maxHeight: 400,
    padding: tokens.spacing('lg'),
  },
  helpSection: {
    marginBottom: tokens.spacing('lg'),
  },
  helpSectionTitle: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('sm'),
  },
  helpSectionText: {
    fontSize: tokens.fontSize('body-sm'),
    lineHeight: 20,
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('md'),
    marginTop: tokens.spacing('sm'),
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('sm'),
  },
  legendIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: tokens.spacing('sm'),
  },
  legendText: {
    fontSize: tokens.fontSize('body-sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  modalFooter: {
    padding: tokens.spacing('lg'),
    borderTopWidth: 1,
    borderTopColor: '#E5E5E7',
  },
  modalCloseButton: {
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
  },
  modalCloseButtonText: {
    fontSize: tokens.fontSize('body-lg'),
    fontWeight: tokens.fontWeight('semibold'),
  },
});

export default SkillGraphScreen;
