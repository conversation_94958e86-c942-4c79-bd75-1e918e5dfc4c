import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { apiService } from '../../services/ApiService';

/**
 * 技能图谱可视化页面
 * 展示完整的技能关系图谱和统计分析
 */
const SkillGraphScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<'graph' | 'statistics'>('graph');
  const [error, setError] = useState<string | null>(null);

  // 加载图谱数据
  const loadGraphData = async () => {
    try {
      setError(null);

      // 获取技能图谱统计信息
      const statsResponse = await apiService.get('/skill-graph/statistics');
      if (statsResponse.code === 20000) {
        setStatistics(statsResponse.data);
      }

      // 获取技能聚类信息
      const clustersResponse = await apiService.get('/skill-graph/clusters');
      if (clustersResponse.code === 20000) {
        setClusters(clustersResponse.data);
      }

    } catch (error) {
      console.error('加载图谱数据失败:', error);
      setError('加载图谱数据失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadGraphData();
  };

  // 处理聚类点击
  const handleClusterPress = (clusterId: number) => {
    Alert.alert(
      '技能聚类',
      `聚类ID: ${clusterId}\n点击查看该聚类的详细信息`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => console.log('查看聚类详情:', clusterId) }
      ]
    );
  };

  // 获取聚类颜色
  const getClusterColor = (category: string) => {
    const colorMap: { [key: string]: string } = {
      'FRONTEND': '#007AFF',
      'BACKEND': '#34C759',
      'DATABASE': '#FF9500',
      'DEVOPS': '#AF52DE',
      'MOBILE': '#FF2D92',
      'AI': '#5AC8FA',
    };
    return colorMap[category] || '#8E8E93';
  };

  // 获取聚类图标
  const getClusterIcon = (category: string) => {
    const iconMap: { [key: string]: any } = {
      'FRONTEND': 'desktop-outline',
      'BACKEND': 'server-outline',
      'DATABASE': 'library-outline',
      'DEVOPS': 'cloud-outline',
      'MOBILE': 'phone-portrait-outline',
      'AI': 'bulb-outline',
    };
    return iconMap[category] || 'code-outline';
  };

  // 初始化加载
  useEffect(() => {
    loadGraphData();
  }, []);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
            加载技能图谱中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadGraphData}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.backgroundPrimary }]}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.backgroundPrimary, borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
          技能图谱
        </Text>

        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={[styles.tabContainer, { backgroundColor: colors.backgroundPrimary }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'graph' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('graph')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'graph' ? colors.primary : colors.textSecondary }
          ]}>
            图谱视图
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'statistics' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('statistics')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'statistics' ? colors.primary : colors.textSecondary }
          ]}>
            统计分析
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'graph' && (
          <View style={styles.graphContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              技能关系图谱
            </Text>

            {/* 图谱控制面板 */}
            <View style={[styles.controlPanel, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.controlTitle, { color: colors.textPrimary }]}>
                图谱控制
              </Text>
              <View style={styles.controlButtons}>
                <TouchableOpacity
                  style={[styles.controlButton, { backgroundColor: colors.primary }]}
                  onPress={() => Alert.alert('提示', '力导向布局功能')}
                  activeOpacity={0.7}
                >
                  <Ionicons name="git-network" size={16} color={colors.backgroundPrimary} />
                  <Text style={[styles.controlButtonText, { color: colors.backgroundPrimary }]}>
                    力导向
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, { backgroundColor: colors.primary }]}
                  onPress={() => Alert.alert('提示', '层次布局功能')}
                  activeOpacity={0.7}
                >
                  <Ionicons name="list" size={16} color={colors.backgroundPrimary} />
                  <Text style={[styles.controlButtonText, { color: colors.backgroundPrimary }]}>
                    层次布局
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, { backgroundColor: colors.primary }]}
                  onPress={() => Alert.alert('提示', '圆形布局功能')}
                  activeOpacity={0.7}
                >
                  <Ionicons name="radio-button-on" size={16} color={colors.backgroundPrimary} />
                  <Text style={[styles.controlButtonText, { color: colors.backgroundPrimary }]}>
                    圆形布局
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* 简化的图谱可视化展示 */}
            <View style={[styles.graphVisualization, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.graphTitle, { color: colors.textPrimary }]}>
                技能关系网络
              </Text>

              {/* 技能聚类展示 */}
              {clusters.map((cluster, index) => (
                <View key={cluster.category} style={[styles.clusterContainer, { backgroundColor: colors.backgroundPrimary }]}>
                  <View style={styles.clusterHeader}>
                    <View style={[styles.clusterIcon, { backgroundColor: getClusterColor(cluster.category) }]}>
                      <Ionicons
                        name={getClusterIcon(cluster.category)}
                        size={20}
                        color={colors.backgroundPrimary}
                      />
                    </View>
                    <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                      {cluster.category} 技能集群
                    </Text>
                    <Text style={[styles.clusterCount, { color: colors.textSecondary }]}>
                      {cluster.skillIds?.length || 0} 个技能
                    </Text>
                  </View>

                  <View style={styles.skillNodes}>
                    {(cluster.skillIds || []).slice(0, 6).map((skillId: number) => (
                      <TouchableOpacity
                        key={skillId}
                        style={[styles.skillNode, { backgroundColor: getClusterColor(cluster.category) + '20' }]}
                        onPress={() => Alert.alert('技能详情', `技能ID: ${skillId}\n分类: ${cluster.category}\n内聚度: ${((cluster.cohesion || 0) * 100).toFixed(1)}%`)}
                        activeOpacity={0.7}
                      >
                        <Text style={[styles.skillNodeText, { color: colors.textPrimary }]}>
                          技能 {skillId}
                        </Text>
                      </TouchableOpacity>
                    ))}
                    {(cluster.skillIds?.length || 0) > 6 && (
                      <View style={[styles.moreSkills, { backgroundColor: colors.border }]}>
                        <Text style={[styles.moreSkillsText, { color: colors.textSecondary }]}>
                          +{(cluster.skillIds?.length || 0) - 6}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}

              {(clusters?.length || 0) === 0 && (
                <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                  暂无技能聚类数据
                </Text>
              )}
            </View>
          </View>
        )}

        {selectedTab === 'statistics' && statistics && (
          <View style={styles.statisticsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              图谱统计信息
            </Text>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>技能节点总数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalNodes}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>关系连接数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalEdges}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>平均度数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.averageDegree?.toFixed(2)}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>连通组件</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.stronglyConnectedComponents}</Text>
            </View>

            {(clusters?.length || 0) > 0 && (
              <View style={styles.clustersSection}>
                <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
                  技能聚类
                </Text>
                {clusters.map((cluster, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.clusterCard, { backgroundColor: colors.backgroundSecondary }]}
                    onPress={() => handleClusterPress(cluster.clusterId)}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                      {cluster.category.toUpperCase()} 集群
                    </Text>
                    <Text style={[styles.clusterDescription, { color: colors.textSecondary }]}>
                      {cluster.skillIds?.length || 0} 个技能 · 内聚度 {((cluster.cohesion || 0) * 100).toFixed(1)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        )}

        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('semiBold'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
  },
  tabButton: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  content: {
    flex: 1,
  },
  graphContainer: {
    padding: tokens.spacing('md'),
  },
  statisticsContainer: {
    padding: tokens.spacing('md'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  sectionDescription: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginTop: tokens.spacing('lg'),
  },
  statCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  statValue: {
    fontSize: tokens.fontSize('xl'),
    fontWeight: tokens.fontWeight('bold'),
  },
  clustersSection: {
    marginTop: tokens.spacing('lg'),
  },
  clusterCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterDescription: {
    fontSize: tokens.fontSize('sm'),
  },
  // 新增样式
  controlPanel: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('md'),
  },
  controlTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('sm'),
  },
  controlButtons: {
    flexDirection: 'row',
    gap: tokens.spacing('sm'),
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    gap: tokens.spacing('xs'),
  },
  controlButtonText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
  },
  graphVisualization: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    minHeight: 400,
  },
  graphTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('md'),
    textAlign: 'center',
  },
  clusterContainer: {
    marginBottom: tokens.spacing('md'),
    padding: tokens.spacing('sm'),
    borderRadius: tokens.radius('sm'),
  },
  clusterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('sm'),
  },
  clusterIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: tokens.spacing('sm'),
  },
  clusterTitle: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
    flex: 1,
  },
  clusterCount: {
    fontSize: tokens.fontSize('xs'),
  },
  skillNodes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('xs'),
  },
  skillNode: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginBottom: tokens.spacing('xs'),
  },
  skillNodeText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
  },
  moreSkills: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreSkillsText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
  },
  emptyText: {
    textAlign: 'center',
    fontSize: tokens.fontSize('sm'),
    marginTop: tokens.spacing('lg'),
  },
});

export default SkillGraphScreen;
