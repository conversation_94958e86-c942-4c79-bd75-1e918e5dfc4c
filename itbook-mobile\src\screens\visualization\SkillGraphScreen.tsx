import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { apiService } from '../../services/ApiService';

/**
 * 技能图谱可视化页面
 * 展示完整的技能关系图谱和统计分析
 */
const SkillGraphScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<'graph' | 'statistics'>('graph');
  const [error, setError] = useState<string | null>(null);

  // 加载图谱数据
  const loadGraphData = async () => {
    try {
      setError(null);

      // 获取技能图谱统计信息
      const statsResponse = await apiService.get('/skill-graph/statistics');
      if (statsResponse.code === 20000) {
        setStatistics(statsResponse.data);
      }

      // 获取技能聚类信息
      const clustersResponse = await apiService.get('/skill-graph/clusters');
      if (clustersResponse.code === 20000) {
        setClusters(clustersResponse.data);
      }

    } catch (error) {
      console.error('加载图谱数据失败:', error);
      setError('加载图谱数据失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadGraphData();
  };

  // 处理聚类点击
  const handleClusterPress = (clusterId: number) => {
    Alert.alert(
      '技能聚类',
      `聚类ID: ${clusterId}\n点击查看该聚类的详细信息`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => console.log('查看聚类详情:', clusterId) }
      ]
    );
  };

  // 初始化加载
  useEffect(() => {
    loadGraphData();
  }, []);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
            加载技能图谱中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadGraphData}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.backgroundPrimary }]}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.backgroundPrimary, borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
          技能图谱
        </Text>

        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={[styles.tabContainer, { backgroundColor: colors.backgroundPrimary }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'graph' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('graph')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'graph' ? colors.primary : colors.textSecondary }
          ]}>
            图谱视图
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'statistics' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('statistics')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'statistics' ? colors.primary : colors.textSecondary }
          ]}>
            统计分析
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'graph' && (
          <View style={styles.graphContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              技能关系图谱
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              图谱可视化功能正在开发中，敬请期待...
            </Text>
          </View>
        )}

        {selectedTab === 'statistics' && statistics && (
          <View style={styles.statisticsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              图谱统计信息
            </Text>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>技能节点总数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalNodes}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>关系连接数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalEdges}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>平均度数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.averageDegree?.toFixed(2)}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>连通组件</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.stronglyConnectedComponents}</Text>
            </View>

            {clusters.length > 0 && (
              <View style={styles.clustersSection}>
                <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
                  技能聚类
                </Text>
                {clusters.map((cluster, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.clusterCard, { backgroundColor: colors.backgroundSecondary }]}
                    onPress={() => handleClusterPress(cluster.clusterId)}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                      {cluster.category.toUpperCase()} 集群
                    </Text>
                    <Text style={[styles.clusterDescription, { color: colors.textSecondary }]}>
                      {cluster.skillIds.length} 个技能 · 内聚度 {(cluster.cohesion * 100).toFixed(1)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        )}

        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('semiBold'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
  },
  tabButton: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  content: {
    flex: 1,
  },
  graphContainer: {
    padding: tokens.spacing('md'),
  },
  statisticsContainer: {
    padding: tokens.spacing('md'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  sectionDescription: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginTop: tokens.spacing('lg'),
  },
  statCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  statValue: {
    fontSize: tokens.fontSize('xl'),
    fontWeight: tokens.fontWeight('bold'),
  },
  clustersSection: {
    marginTop: tokens.spacing('lg'),
  },
  clusterCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterDescription: {
    fontSize: tokens.fontSize('sm'),
  },
});

export default SkillGraphScreen;
