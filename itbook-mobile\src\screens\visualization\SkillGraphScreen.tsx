import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { apiService } from '../../services/ApiService';

/**
 * 技能图谱可视化页面
 * 展示完整的技能关系图谱和统计分析
 */
const SkillGraphScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<'graph' | 'statistics'>('graph');
  const [error, setError] = useState<string | null>(null);
  const [selectedLayout, setSelectedLayout] = useState<'force' | 'hierarchy' | 'circular'>('force');
  const [layoutLoading, setLayoutLoading] = useState(false);

  // 加载图谱数据
  const loadGraphData = async () => {
    try {
      setError(null);

      // 获取技能图谱统计信息
      const statsResponse = await apiService.get('/skill-graph/statistics');
      if (statsResponse.code === 20000) {
        setStatistics(statsResponse.data);
      }

      // 获取技能聚类信息
      const clustersResponse = await apiService.get('/skill-graph/clusters');
      if (clustersResponse.code === 20000) {
        setClusters(clustersResponse.data);
      }

    } catch (error) {
      console.error('加载图谱数据失败:', error);
      setError('加载图谱数据失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadGraphData();
  };

  // 处理布局切换
  const handleLayoutChange = async (layout: 'force' | 'hierarchy' | 'circular') => {
    if (layout === selectedLayout) return;

    setLayoutLoading(true);
    setSelectedLayout(layout);

    // 模拟布局计算时间
    await new Promise(resolve => setTimeout(resolve, 800));

    setLayoutLoading(false);

    // 显示布局切换成功提示
    const layoutNames = {
      force: '力导向布局',
      hierarchy: '层次布局',
      circular: '圆形布局'
    };

    console.log(`🎨 图谱布局已切换为: ${layoutNames[layout]}`);
  };

  // 处理聚类点击
  const handleClusterPress = (clusterId: number) => {
    Alert.alert(
      '技能聚类',
      `聚类ID: ${clusterId}\n点击查看该聚类的详细信息`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => console.log('查看聚类详情:', clusterId) }
      ]
    );
  };

  // 获取聚类颜色
  const getClusterColor = (category: string) => {
    const colorMap: { [key: string]: string } = {
      'FRONTEND': '#007AFF',
      'BACKEND': '#34C759',
      'DATABASE': '#FF9500',
      'DEVOPS': '#AF52DE',
      'MOBILE': '#FF2D92',
      'AI': '#5AC8FA',
    };
    return colorMap[category] || '#8E8E93';
  };

  // 获取聚类图标
  const getClusterIcon = (category: string) => {
    const iconMap: { [key: string]: any } = {
      'FRONTEND': 'desktop-outline',
      'BACKEND': 'server-outline',
      'DATABASE': 'library-outline',
      'DEVOPS': 'cloud-outline',
      'MOBILE': 'phone-portrait-outline',
      'AI': 'bulb-outline',
    };
    return iconMap[category] || 'code-outline';
  };

  // 初始化加载
  useEffect(() => {
    loadGraphData();
  }, []);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
            加载技能图谱中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadGraphData}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.backgroundPrimary }]}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.backgroundPrimary, borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
          技能图谱
        </Text>

        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={[styles.tabContainer, { backgroundColor: colors.backgroundPrimary }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'graph' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('graph')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'graph' ? colors.primary : colors.textSecondary }
          ]}>
            图谱视图
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'statistics' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('statistics')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'statistics' ? colors.primary : colors.textSecondary }
          ]}>
            统计分析
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'graph' && (
          <View style={styles.graphContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              技能关系图谱
            </Text>

            {/* 图谱控制面板 */}
            <View style={[styles.controlPanel, { backgroundColor: colors.backgroundSecondary }]}>
              <View style={styles.controlHeader}>
                <View style={styles.controlTitleContainer}>
                  <Ionicons name="options-outline" size={20} color={colors.primary} />
                  <Text style={[styles.controlTitle, { color: colors.textPrimary }]}>
                    布局控制
                  </Text>
                </View>
                <Text style={[styles.controlSubtitle, { color: colors.textSecondary }]}>
                  选择图谱布局方式
                </Text>
              </View>
              <View style={styles.controlButtons}>
                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'force' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'force' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'force' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('force')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="git-network-outline"
                      size={20}
                      color={selectedLayout === 'force' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'force' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      力导向布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'force' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      自然聚集
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'hierarchy' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'hierarchy' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'hierarchy' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('hierarchy')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="list-outline"
                      size={20}
                      color={selectedLayout === 'hierarchy' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'hierarchy' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      层次布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'hierarchy' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      分层展示
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.controlButton,
                    selectedLayout === 'circular' && styles.controlButtonActive,
                    {
                      backgroundColor: selectedLayout === 'circular' ? colors.primary : colors.backgroundPrimary,
                      borderColor: selectedLayout === 'circular' ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleLayoutChange('circular')}
                  activeOpacity={0.7}
                >
                  <View style={styles.controlButtonContent}>
                    <Ionicons
                      name="radio-button-on-outline"
                      size={20}
                      color={selectedLayout === 'circular' ? colors.backgroundPrimary : colors.textSecondary}
                    />
                    <Text style={[
                      styles.controlButtonText,
                      { color: selectedLayout === 'circular' ? colors.backgroundPrimary : colors.textPrimary }
                    ]}>
                      圆形布局
                    </Text>
                    <Text style={[
                      styles.controlButtonDesc,
                      { color: selectedLayout === 'circular' ? colors.backgroundPrimary + '80' : colors.textSecondary }
                    ]}>
                      环形排列
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* 简化的图谱可视化展示 */}
            <View style={[styles.graphVisualization, { backgroundColor: colors.backgroundSecondary }]}>
              <View style={styles.graphHeader}>
                <Text style={[styles.graphTitle, { color: colors.textPrimary }]}>
                  技能关系网络
                </Text>
                {layoutLoading && (
                  <View style={styles.layoutLoadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={[styles.layoutLoadingText, { color: colors.textSecondary }]}>
                      布局计算中...
                    </Text>
                  </View>
                )}
              </View>

              {/* 技能聚类展示 */}
              {clusters.map((cluster, index) => (
                <View key={cluster.category} style={[styles.clusterContainer, { backgroundColor: colors.backgroundPrimary }]}>
                  <View style={styles.clusterHeader}>
                    <View style={[styles.clusterIconContainer, { backgroundColor: getClusterColor(cluster.category) + '15' }]}>
                      <View style={[styles.clusterIcon, { backgroundColor: getClusterColor(cluster.category) }]}>
                        <Ionicons
                          name={getClusterIcon(cluster.category)}
                          size={18}
                          color={colors.backgroundPrimary}
                        />
                      </View>
                    </View>
                    <View style={styles.clusterInfo}>
                      <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                        {cluster.category.toUpperCase()} 技能集群
                      </Text>
                      <View style={styles.clusterMeta}>
                        <Text style={[styles.clusterCount, { color: colors.textSecondary }]}>
                          {cluster.skillIds?.length || 0} 个技能
                        </Text>
                        <View style={styles.clusterDivider} />
                        <Text style={[styles.clusterCohesion, { color: getClusterColor(cluster.category) }]}>
                          内聚度 {((cluster.cohesion || 0) * 100).toFixed(1)}%
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.skillNodes}>
                    {(cluster.skillIds || []).slice(0, 6).map((skillId: number) => (
                      <TouchableOpacity
                        key={skillId}
                        style={[
                          styles.skillNode,
                          {
                            backgroundColor: getClusterColor(cluster.category) + '10',
                            borderColor: getClusterColor(cluster.category) + '30',
                          }
                        ]}
                        onPress={() => Alert.alert('技能详情', `技能ID: ${skillId}\n分类: ${cluster.category}\n内聚度: ${((cluster.cohesion || 0) * 100).toFixed(1)}%`)}
                        activeOpacity={0.7}
                      >
                        <Text style={[styles.skillNodeText, { color: colors.textPrimary }]}>
                          技能 {skillId}
                        </Text>
                      </TouchableOpacity>
                    ))}
                    {(cluster.skillIds?.length || 0) > 6 && (
                      <View style={[
                        styles.moreSkills,
                        {
                          backgroundColor: colors.backgroundSecondary,
                          borderColor: colors.border,
                        }
                      ]}>
                        <Text style={[styles.moreSkillsText, { color: colors.textSecondary }]}>
                          +{(cluster.skillIds?.length || 0) - 6}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}

              {(clusters?.length || 0) === 0 && (
                <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                  暂无技能聚类数据
                </Text>
              )}
            </View>
          </View>
        )}

        {selectedTab === 'statistics' && statistics && (
          <View style={styles.statisticsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              图谱统计信息
            </Text>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>技能节点总数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalNodes}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>关系连接数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.totalEdges}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>平均度数</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.averageDegree?.toFixed(2)}</Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.backgroundSecondary }]}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>连通组件</Text>
              <Text style={[styles.statValue, { color: colors.primary }]}>{statistics.stronglyConnectedComponents}</Text>
            </View>

            {(clusters?.length || 0) > 0 && (
              <View style={styles.clustersSection}>
                <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
                  技能聚类
                </Text>
                {clusters.map((cluster, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.clusterCard, { backgroundColor: colors.backgroundSecondary }]}
                    onPress={() => handleClusterPress(cluster.clusterId)}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.clusterTitle, { color: colors.textPrimary }]}>
                      {cluster.category.toUpperCase()} 集群
                    </Text>
                    <Text style={[styles.clusterDescription, { color: colors.textSecondary }]}>
                      {cluster.skillIds?.length || 0} 个技能 · 内聚度 {((cluster.cohesion || 0) * 100).toFixed(1)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        )}

        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('semiBold'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
  },
  tabButton: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  content: {
    flex: 1,
  },
  graphContainer: {
    padding: tokens.spacing('md'),
  },
  statisticsContainer: {
    padding: tokens.spacing('md'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  sectionDescription: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginTop: tokens.spacing('lg'),
  },
  statCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  statValue: {
    fontSize: tokens.fontSize('xl'),
    fontWeight: tokens.fontWeight('bold'),
  },
  clustersSection: {
    marginTop: tokens.spacing('lg'),
  },
  clusterCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterDescription: {
    fontSize: tokens.fontSize('sm'),
  },
  // 新增样式
  controlPanel: {
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    marginBottom: tokens.spacing('lg'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  controlHeader: {
    marginBottom: tokens.spacing('md'),
  },
  controlTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('xs'),
  },
  controlTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginLeft: tokens.spacing('sm'),
  },
  controlSubtitle: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  controlButtons: {
    flexDirection: 'row',
    gap: tokens.spacing('md'),
  },
  controlButton: {
    flex: 1,
    borderRadius: tokens.radius('md'),
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  controlButtonActive: {
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  controlButtonContent: {
    alignItems: 'center',
    padding: tokens.spacing('md'),
  },
  controlButtonText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginTop: tokens.spacing('xs'),
    textAlign: 'center',
  },
  controlButtonDesc: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
    marginTop: tokens.spacing('xs') / 2,
    textAlign: 'center',
  },
  graphVisualization: {
    padding: tokens.spacing('lg'),
    borderRadius: tokens.radius('lg'),
    minHeight: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  graphHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  graphTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  layoutLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  layoutLoadingText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('sm'),
  },
  clusterContainer: {
    marginBottom: tokens.spacing('lg'),
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('lg'),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  clusterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing('md'),
  },
  clusterIconContainer: {
    padding: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginRight: tokens.spacing('md'),
  },
  clusterIcon: {
    width: 32,
    height: 32,
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  clusterInfo: {
    flex: 1,
  },
  clusterTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('xs'),
  },
  clusterMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clusterCount: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
  },
  clusterDivider: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#ccc',
    marginHorizontal: tokens.spacing('sm'),
  },
  clusterCohesion: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  skillNodes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing('sm'),
  },
  skillNode: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('sm'),
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  skillNodeText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  moreSkills: {
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  moreSkillsText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('bold'),
  },
  emptyText: {
    textAlign: 'center',
    fontSize: tokens.fontSize('sm'),
    marginTop: tokens.spacing('lg'),
  },
});

export default SkillGraphScreen;
