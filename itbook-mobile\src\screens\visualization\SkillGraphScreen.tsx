import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Colors,
  TouchableOpacity,
  LoaderScreen
} from 'react-native-ui-lib';
import {
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { designTokens } from '../../design-tokens';
import { SkillGraphVisualization, GraphData, SkillNode, SkillEdge } from '../../components/visualization/SkillGraphVisualization';
import { GraphStatisticsPanel, GraphStatistics } from '../../components/visualization/GraphStatisticsPanel';
import { ApiService } from '../../services/ApiService';

/**
 * 技能图谱可视化页面
 * 展示完整的技能关系图谱和统计分析
 */
const SkillGraphScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [graphData, setGraphData] = useState<GraphData | null>(null);
  const [statistics, setStatistics] = useState<GraphStatistics | null>(null);
  const [selectedTab, setSelectedTab] = useState<'graph' | 'statistics'>('graph');
  const [error, setError] = useState<string | null>(null);

  // 加载图谱数据
  const loadGraphData = async () => {
    try {
      setError(null);
      
      // 获取技能图谱统计信息
      const statsResponse = await ApiService.get('/skill-graph/statistics');
      if (statsResponse.code === 20000) {
        setStatistics(statsResponse.data);
      }

      // 获取技能聚类信息
      const clustersResponse = await ApiService.get('/skill-graph/clusters');
      if (clustersResponse.code === 20000 && statistics) {
        setStatistics(prev => prev ? { ...prev, clusters: clustersResponse.data } : null);
      }

      // 构建图谱数据（模拟数据，实际应该从API获取）
      const mockGraphData: GraphData = {
        nodes: await loadSkillNodes(),
        edges: await loadSkillEdges(),
        statistics: statsResponse.data
      };

      setGraphData(mockGraphData);
    } catch (error) {
      console.error('加载图谱数据失败:', error);
      setError('加载图谱数据失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 加载技能节点数据
  const loadSkillNodes = async (): Promise<SkillNode[]> => {
    try {
      const response = await ApiService.get('/atomic-skills');
      if (response.code === 20000) {
        return response.data.content.map((skill: any) => ({
          id: skill.id,
          name: skill.name,
          category: skill.category,
          difficultyLevel: skill.difficultyLevel,
          importance: Math.random() * 3 + 1 // 模拟重要性分数
        }));
      }
      return [];
    } catch (error) {
      console.error('加载技能节点失败:', error);
      return [];
    }
  };

  // 加载技能关系数据
  const loadSkillEdges = async (): Promise<SkillEdge[]> => {
    try {
      const response = await ApiService.get('/skill-relationships');
      if (response.code === 20000) {
        return response.data.map((relationship: any) => ({
          sourceId: relationship.sourceSkillId,
          targetId: relationship.targetSkillId,
          relationshipType: relationship.relationshipType,
          strength: relationship.strength || 1
        }));
      }
      return [];
    } catch (error) {
      console.error('加载技能关系失败:', error);
      return [];
    }
  };

  // 处理下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadGraphData();
  };

  // 处理节点点击
  const handleNodePress = (node: SkillNode) => {
    Alert.alert(
      node.name,
      `类别: ${node.category}\n难度: ${node.difficultyLevel}\n重要性: ${node.importance?.toFixed(2)}`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => navigateToSkillDetail(node.id) }
      ]
    );
  };

  // 处理边点击
  const handleEdgePress = (edge: SkillEdge) => {
    Alert.alert(
      '技能关系',
      `关系类型: ${edge.relationshipType}\n强度: ${edge.strength}`,
      [{ text: '确定' }]
    );
  };

  // 处理聚类点击
  const handleClusterPress = (clusterId: number) => {
    Alert.alert(
      '技能聚类',
      `聚类ID: ${clusterId}\n点击查看该聚类的详细信息`,
      [
        { text: '取消', style: 'cancel' },
        { text: '查看详情', onPress: () => console.log('查看聚类详情:', clusterId) }
      ]
    );
  };

  // 导航到技能详情
  const navigateToSkillDetail = (skillId: number) => {
    // TODO: 实现导航到技能详情页面
    console.log('导航到技能详情:', skillId);
  };

  // 初始化加载
  useEffect(() => {
    loadGraphData();
  }, []);

  if (loading) {
    return (
      <LoaderScreen
        color={colors.primary}
        message="加载技能图谱中..."
        overlay
      />
    );
  }

  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.backgroundPrimary }}>
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: designTokens.spacing.lg
        }}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={{
            fontSize: designTokens.fontSize.lg,
            fontWeight: designTokens.fontWeight.semiBold,
            color: colors.textPrimary,
            textAlign: 'center',
            marginTop: designTokens.spacing.md,
            marginBottom: designTokens.spacing.sm
          }}>
            加载失败
          </Text>
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            color: colors.textSecondary,
            textAlign: 'center',
            marginBottom: designTokens.spacing.lg
          }}>
            {error}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: designTokens.spacing.lg,
              paddingVertical: designTokens.spacing.md,
              borderRadius: designTokens.borderRadius.md
            }}
            onPress={loadGraphData}
            activeOpacity={0.7}
          >
            <Text style={{
              color: colors.backgroundPrimary,
              fontSize: designTokens.fontSize.md,
              fontWeight: designTokens.fontWeight.semiBold
            }}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.backgroundPrimary }}>
      {/* 顶部导航栏 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: designTokens.spacing.md,
        paddingVertical: designTokens.spacing.sm,
        backgroundColor: colors.backgroundPrimary,
        borderBottomWidth: 1,
        borderBottomColor: colors.border
      }}>
        <Text style={{
          fontSize: designTokens.fontSize.lg,
          fontWeight: designTokens.fontWeight.bold,
          color: colors.textPrimary
        }}>
          技能图谱
        </Text>
        
        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.backgroundPrimary,
        paddingHorizontal: designTokens.spacing.md,
        paddingVertical: designTokens.spacing.sm
      }}>
        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: designTokens.spacing.sm,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: selectedTab === 'graph' ? colors.primary : 'transparent'
          }}
          onPress={() => setSelectedTab('graph')}
          activeOpacity={0.7}
        >
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            fontWeight: designTokens.fontWeight.semiBold,
            color: selectedTab === 'graph' ? colors.primary : colors.textSecondary
          }}>
            图谱视图
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: designTokens.spacing.sm,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: selectedTab === 'statistics' ? colors.primary : 'transparent'
          }}
          onPress={() => setSelectedTab('statistics')}
          activeOpacity={0.7}
        >
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            fontWeight: designTokens.fontWeight.semiBold,
            color: selectedTab === 'statistics' ? colors.primary : colors.textSecondary
          }}>
            统计分析
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'graph' && graphData && (
          <SkillGraphVisualization
            data={graphData}
            interactive={true}
            showLabels={true}
            onNodePress={handleNodePress}
            onEdgePress={handleEdgePress}
          />
        )}
        
        {selectedTab === 'statistics' && statistics && (
          <GraphStatisticsPanel
            statistics={statistics}
            onClusterPress={handleClusterPress}
          />
        )}
        
        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default SkillGraphScreen;
