package com.itbook.controller;

import com.itbook.entity.UserAtomicSkillMastery;
import com.itbook.entity.AtomicSkill;
import com.itbook.service.UserAtomicSkillMasteryService;
import com.itbook.dto.UserSkillMasteryDTO;
import com.itbook.dto.SkillMasteryAnalysisDTO;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户原子技能掌握度控制器
 * 提供用户技能掌握情况相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/user-skill-mastery")
@Tag(name = "用户技能掌握度管理", description = "用户技能掌握情况的管理、分析、推荐等API")
public class UserAtomicSkillMasteryController {

    @Autowired
    private UserAtomicSkillMasteryService masteryService;

    /**
     * 更新用户技能掌握度
     */
    @PutMapping("/users/{userId}/skills/{skillId}")
    @Operation(summary = "更新技能掌握度", description = "更新用户对指定技能的掌握度")
    public ApiResponse<UserAtomicSkillMastery> updateMastery(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Valid @RequestBody UserSkillMasteryDTO masteryDTO) {
        return masteryService.updateMastery(userId, skillId, masteryDTO);
    }

    /**
     * 记录学习活动
     */
    @PostMapping("/users/{userId}/skills/{skillId}/activity")
    @Operation(summary = "记录学习活动", description = "记录用户的学习活动")
    public ApiResponse<UserAtomicSkillMastery> recordLearningActivity(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "学习时长") @RequestParam BigDecimal learningHours,
            @Parameter(description = "活动类型") @RequestParam(defaultValue = "practice") String activityType) {
        return masteryService.recordLearningActivity(userId, skillId, learningHours, activityType);
    }

    /**
     * 获取用户技能掌握情况
     */
    @GetMapping("/users/{userId}")
    @Operation(summary = "获取用户技能掌握情况", description = "获取用户所有技能的掌握情况")
    public ApiResponse<List<UserAtomicSkillMastery>> getUserMasteries(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return masteryService.getUserMasteries(userId);
    }

    /**
     * 获取用户技能掌握分析
     */
    @GetMapping("/users/{userId}/analysis")
    @Operation(summary = "获取技能掌握分析", description = "获取用户技能掌握情况的详细分析")
    public ApiResponse<SkillMasteryAnalysisDTO> getUserMasteryAnalysis(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return masteryService.getUserMasteryAnalysis(userId);
    }

    /**
     * 获取需要复习的技能
     */
    @GetMapping("/users/{userId}/refresh-needed")
    @Operation(summary = "获取需要复习的技能", description = "获取用户需要复习的技能列表")
    public ApiResponse<List<UserAtomicSkillMastery>> getSkillsNeedingRefresh(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return masteryService.getSkillsNeedingRefresh(userId);
    }

    /**
     * 获取用户最近练习的技能
     */
    @GetMapping("/users/{userId}/recent-practiced")
    @Operation(summary = "获取最近练习的技能", description = "获取用户最近练习的技能列表")
    public ApiResponse<List<UserAtomicSkillMastery>> getRecentPracticedSkills(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") int limit) {
        return masteryService.getRecentPracticedSkills(userId, limit);
    }

    /**
     * 批量更新技能掌握度
     */
    @PutMapping("/users/{userId}/batch")
    @Operation(summary = "批量更新技能掌握度", description = "批量更新用户的技能掌握度")
    public ApiResponse<Integer> updateMasteriesBatch(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @RequestBody Map<Long, UserSkillMasteryDTO> masteryUpdates) {
        return masteryService.updateMasteriesBatch(userId, masteryUpdates);
    }

    /**
     * 获取技能推荐
     */
    @GetMapping("/users/{userId}/recommendations")
    @Operation(summary = "获取技能推荐", description = "为用户推荐适合学习的技能")
    public ApiResponse<List<AtomicSkill>> getRecommendedSkills(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") int limit) {
        return masteryService.getRecommendedSkills(userId, limit);
    }

    /**
     * 删除用户技能掌握记录
     */
    @DeleteMapping("/users/{userId}/skills/{skillId}")
    @Operation(summary = "删除技能掌握记录", description = "删除用户对指定技能的掌握记录")
    public ApiResponse<Void> deleteMastery(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return masteryService.deleteMastery(userId, skillId);
    }

    /**
     * 获取技能的掌握统计
     */
    @GetMapping("/skills/{skillId}/statistics")
    @Operation(summary = "获取技能掌握统计", description = "获取指定技能的掌握统计信息")
    public ApiResponse<Map<String, Object>> getSkillMasteryStatistics(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        return masteryService.getSkillMasteryStatistics(skillId);
    }
}
