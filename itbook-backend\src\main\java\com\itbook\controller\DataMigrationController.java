package com.itbook.controller;

import com.itbook.migration.*;
import com.itbook.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据迁移控制器
 * 提供数据迁移相关的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@RestController
@RequestMapping("/data-migration")
@Tag(name = "数据迁移管理", description = "数据迁移执行、监控、验证等API")
public class DataMigrationController {

    @Autowired
    private DataMigrationExecutor migrationExecutor;

    @Autowired
    private DataValidator dataValidator;

    @Autowired
    private MigrationLogger migrationLogger;

    /**
     * 执行完整的数据迁移
     */
    @PostMapping("/execute")
    @Operation(summary = "执行数据迁移", description = "执行完整的数据迁移流程")
    public ApiResponse<MigrationResult> executeMigration() {
        try {
            // 初始化日志表
            migrationLogger.initializeLogTable();
            
            // 执行迁移
            MigrationResult result = migrationExecutor.executeFullMigration();
            
            if (result.isSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error("数据迁移失败: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("数据迁移执行异常: " + e.getMessage());
        }
    }

    /**
     * 获取迁移状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取迁移状态", description = "获取当前数据迁移的状态")
    public ApiResponse<Map<String, Object>> getMigrationStatus() {
        try {
            MigrationStatus status = migrationExecutor.getMigrationStatus();
            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("status", status.toString());
            statusInfo.put("statusName", status.getName());
            statusInfo.put("description", status.getDescription());
            statusInfo.put("isInProgress", status.isInProgress());
            statusInfo.put("isCompleted", status.isCompleted());
            statusInfo.put("isSuccessful", status.isSuccessful());
            statusInfo.put("progressPercentage", status.getProgressPercentage());
            
            return ApiResponse.success(statusInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取迁移状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取迁移统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取迁移统计", description = "获取数据迁移的统计信息")
    public ApiResponse<Map<String, Object>> getMigrationStatistics() {
        try {
            Map<String, Object> statistics = migrationExecutor.getMigrationStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取迁移统计失败: " + e.getMessage());
        }
    }

    /**
     * 验证迁移结果
     */
    @PostMapping("/validate")
    @Operation(summary = "验证迁移结果", description = "验证数据迁移的完整性和正确性")
    public ApiResponse<ValidationResult> validateMigration() {
        try {
            long startTime = System.currentTimeMillis();
            ValidationResult result = dataValidator.validateMigration();
            result.setValidationDurationMs(System.currentTimeMillis() - startTime);
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("验证迁移结果失败: " + e.getMessage());
        }
    }

    /**
     * 快速验证迁移状态
     */
    @GetMapping("/validate/quick")
    @Operation(summary = "快速验证", description = "快速验证迁移是否成功")
    public ApiResponse<Map<String, Object>> quickValidate() {
        try {
            boolean isValid = dataValidator.isValidMigration();
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("message", isValid ? "迁移验证通过" : "迁移验证失败");
            result.put("timestamp", java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("快速验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取验证摘要
     */
    @GetMapping("/validate/summary")
    @Operation(summary = "获取验证摘要", description = "获取数据验证的摘要信息")
    public ApiResponse<Map<String, Object>> getValidationSummary() {
        try {
            Map<String, Object> summary = dataValidator.getValidationSummary();
            return ApiResponse.success(summary);
        } catch (Exception e) {
            return ApiResponse.error("获取验证摘要失败: " + e.getMessage());
        }
    }

    /**
     * 检查迁移前置条件
     */
    @GetMapping("/prerequisites")
    @Operation(summary = "检查前置条件", description = "检查执行迁移的前置条件")
    public ApiResponse<Map<String, Object>> checkPrerequisites() {
        try {
            // 检查数据库连接
            boolean dbConnected = true;
            try {
                migrationExecutor.getMigrationStatus();
            } catch (Exception e) {
                dbConnected = false;
            }
            
            // 检查原始数据
            boolean hasOriginalData = false;
            try {
                Map<String, Object> stats = migrationExecutor.getMigrationStatistics();
                Integer careerSkills = (Integer) stats.get("originalCareerSkills");
                hasOriginalData = careerSkills != null && careerSkills > 0;
            } catch (Exception e) {
                // 忽略异常
            }
            
            // 检查是否已迁移
            boolean alreadyMigrated = false;
            try {
                alreadyMigrated = dataValidator.isValidMigration();
            } catch (Exception e) {
                // 忽略异常
            }
            
            Map<String, Object> prerequisites = new HashMap<>();
            prerequisites.put("databaseConnected", dbConnected);
            prerequisites.put("hasOriginalData", hasOriginalData);
            prerequisites.put("alreadyMigrated", alreadyMigrated);
            prerequisites.put("canExecuteMigration", dbConnected && hasOriginalData && !alreadyMigrated);
            prerequisites.put("message", getPrerequisiteMessage(dbConnected, hasOriginalData, alreadyMigrated));
            
            return ApiResponse.success(prerequisites);
        } catch (Exception e) {
            return ApiResponse.error("检查前置条件失败: " + e.getMessage());
        }
    }

    /**
     * 获取迁移日志
     */
    @GetMapping("/logs")
    @Operation(summary = "获取迁移日志", description = "获取数据迁移的执行日志")
    public ApiResponse<Map<String, Object>> getMigrationLogs(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            // 这里可以实现获取迁移日志的逻辑
            Map<String, Object> logs = new HashMap<>();
            logs.put("message", "迁移日志功能开发中");
            logs.put("limit", limit);
            logs.put("timestamp", java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return ApiResponse.success(logs);
        } catch (Exception e) {
            return ApiResponse.error("获取迁移日志失败: " + e.getMessage());
        }
    }

    /**
     * 清理迁移数据
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理迁移数据", description = "清理迁移过程中产生的临时数据")
    public ApiResponse<Map<String, Object>> cleanupMigrationData() {
        try {
            // 这里可以实现清理逻辑
            Map<String, Object> result = new HashMap<>();
            result.put("message", "迁移数据清理功能开发中");
            result.put("timestamp", java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("清理迁移数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取前置条件检查消息
     */
    private String getPrerequisiteMessage(boolean dbConnected, boolean hasOriginalData, boolean alreadyMigrated) {
        if (!dbConnected) {
            return "数据库连接失败，无法执行迁移";
        }
        if (!hasOriginalData) {
            return "缺少原始数据，无法执行迁移";
        }
        if (alreadyMigrated) {
            return "数据已迁移，无需重复执行";
        }
        return "前置条件检查通过，可以执行迁移";
    }
}
