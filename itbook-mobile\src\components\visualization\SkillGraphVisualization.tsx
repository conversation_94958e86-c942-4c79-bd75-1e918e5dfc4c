import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Colors,
  TouchableOpacity,
  Card
} from 'react-native-ui-lib';
import {
  Dimensions,
  ScrollView,
  PanResponder,
  Animated,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Svg, { Circle, Line, Text as SvgText, G } from 'react-native-svg';
import { useThemeColors } from '../../contexts/ThemeContext';
import { designTokens } from '../../design-tokens';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 技能节点数据类型
export interface SkillNode {
  id: number;
  name: string;
  category: string;
  difficultyLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  x?: number;
  y?: number;
  importance?: number;
}

// 技能关系边数据类型
export interface SkillEdge {
  sourceId: number;
  targetId: number;
  relationshipType: 'PREREQUISITE' | 'SUCCESSOR' | 'RELATED' | 'PARALLEL' | 'ALTERNATIVE';
  strength: number;
}

// 图谱数据类型
export interface GraphData {
  nodes: SkillNode[];
  edges: SkillEdge[];
  statistics?: {
    totalNodes: number;
    totalEdges: number;
    clusters: Array<{
      clusterId: number;
      skillIds: number[];
      category: string;
    }>;
  };
}

interface SkillGraphVisualizationProps {
  data: GraphData;
  width?: number;
  height?: number;
  interactive?: boolean;
  showLabels?: boolean;
  onNodePress?: (node: SkillNode) => void;
  onEdgePress?: (edge: SkillEdge) => void;
}

/**
 * 技能图谱可视化组件
 * 提供交互式的技能关系图展示
 */
export const SkillGraphVisualization: React.FC<SkillGraphVisualizationProps> = ({
  data,
  width = screenWidth - 32,
  height = 400,
  interactive = true,
  showLabels = true,
  onNodePress,
  onEdgePress
}) => {
  const colors = useThemeColors();
  const [viewBox, setViewBox] = useState({ x: 0, y: 0, width, height });
  const [selectedNode, setSelectedNode] = useState<number | null>(null);
  const [zoom, setZoom] = useState(1);
  const panRef = useRef(new Animated.ValueXY()).current;

  // 计算节点位置（使用力导向布局算法）
  const calculateNodePositions = (nodes: SkillNode[], edges: SkillEdge[]) => {
    const positionedNodes = [...nodes];
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.3;

    // 按类别分组节点
    const categoryGroups = new Map<string, SkillNode[]>();
    nodes.forEach(node => {
      if (!categoryGroups.has(node.category)) {
        categoryGroups.set(node.category, []);
      }
      categoryGroups.get(node.category)!.push(node);
    });

    // 为每个类别分配角度范围
    const categories = Array.from(categoryGroups.keys());
    const angleStep = (2 * Math.PI) / categories.length;

    categories.forEach((category, categoryIndex) => {
      const categoryNodes = categoryGroups.get(category)!;
      const categoryAngle = categoryIndex * angleStep;
      const categoryRadius = radius + (categoryIndex % 2) * 50;

      categoryNodes.forEach((node, nodeIndex) => {
        const nodeAngle = categoryAngle + (nodeIndex - categoryNodes.length / 2) * 0.3;
        const nodeRadius = categoryRadius + (nodeIndex % 2) * 30;
        
        node.x = centerX + Math.cos(nodeAngle) * nodeRadius;
        node.y = centerY + Math.sin(nodeAngle) * nodeRadius;
      });
    });

    return positionedNodes;
  };

  // 获取节点颜色
  const getNodeColor = (node: SkillNode) => {
    const isSelected = selectedNode === node.id;
    
    switch (node.category) {
      case 'frontend':
        return isSelected ? colors.primary : '#4CAF50';
      case 'backend':
        return isSelected ? colors.primary : '#2196F3';
      case 'database':
        return isSelected ? colors.primary : '#FF9800';
      case 'devops':
        return isSelected ? colors.primary : '#9C27B0';
      default:
        return isSelected ? colors.primary : colors.textSecondary;
    }
  };

  // 获取节点大小
  const getNodeSize = (node: SkillNode) => {
    const baseSize = 8;
    const importanceMultiplier = (node.importance || 1) * 2;
    const difficultyMultiplier = {
      'BEGINNER': 1,
      'INTERMEDIATE': 1.2,
      'ADVANCED': 1.4,
      'EXPERT': 1.6
    }[node.difficultyLevel];
    
    return baseSize * importanceMultiplier * difficultyMultiplier;
  };

  // 获取边的颜色和样式
  const getEdgeStyle = (edge: SkillEdge) => {
    const colors = {
      'PREREQUISITE': '#E91E63',
      'SUCCESSOR': '#3F51B5',
      'RELATED': '#607D8B',
      'PARALLEL': '#FF5722',
      'ALTERNATIVE': '#795548'
    };
    
    return {
      color: colors[edge.relationshipType] || '#999',
      width: Math.max(1, edge.strength * 3),
      opacity: 0.6
    };
  };

  // 处理节点点击
  const handleNodePress = (node: SkillNode) => {
    setSelectedNode(selectedNode === node.id ? null : node.id);
    onNodePress?.(node);
  };

  // 计算节点位置
  const positionedNodes = calculateNodePositions(data.nodes, data.edges);

  return (
    <Card style={{
      margin: designTokens.spacing.md,
      padding: designTokens.spacing.md,
      backgroundColor: colors.backgroundPrimary
    }}>
      {/* 图谱标题和控制栏 */}
      <View row spread centerV marginB-16>
        <View>
          <Text style={{
            fontSize: designTokens.fontSize.lg,
            fontWeight: designTokens.fontWeight.bold,
            color: colors.textPrimary
          }}>
            技能关系图谱
          </Text>
          <Text style={{
            fontSize: designTokens.fontSize.sm,
            color: colors.textSecondary,
            marginTop: 4
          }}>
            {data.nodes.length} 个技能 · {data.edges.length} 个关系
          </Text>
        </View>
        
        <View row centerV>
          <TouchableOpacity
            style={{
              padding: 8,
              borderRadius: designTokens.borderRadius.sm,
              backgroundColor: colors.backgroundSecondary,
              marginRight: 8
            }}
            onPress={() => setZoom(Math.max(0.5, zoom - 0.1))}
          >
            <Ionicons name="remove" size={16} color={colors.textPrimary} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={{
              padding: 8,
              borderRadius: designTokens.borderRadius.sm,
              backgroundColor: colors.backgroundSecondary
            }}
            onPress={() => setZoom(Math.min(2, zoom + 0.1))}
          >
            <Ionicons name="add" size={16} color={colors.textPrimary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* SVG 图谱 */}
      <View style={{
        backgroundColor: colors.backgroundSecondary,
        borderRadius: designTokens.borderRadius.md,
        overflow: 'hidden'
      }}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          maximumZoomScale={2}
          minimumZoomScale={0.5}
          style={{ height }}
        >
          <Svg width={width * zoom} height={height * zoom}>
            {/* 渲染边 */}
            {data.edges.map((edge, index) => {
              const sourceNode = positionedNodes.find(n => n.id === edge.sourceId);
              const targetNode = positionedNodes.find(n => n.id === edge.targetId);
              
              if (!sourceNode || !targetNode) return null;
              
              const edgeStyle = getEdgeStyle(edge);
              
              return (
                <Line
                  key={`edge-${index}`}
                  x1={(sourceNode.x || 0) * zoom}
                  y1={(sourceNode.y || 0) * zoom}
                  x2={(targetNode.x || 0) * zoom}
                  y2={(targetNode.y || 0) * zoom}
                  stroke={edgeStyle.color}
                  strokeWidth={edgeStyle.width}
                  strokeOpacity={edgeStyle.opacity}
                  onPress={() => onEdgePress?.(edge)}
                />
              );
            })}
            
            {/* 渲染节点 */}
            {positionedNodes.map((node) => {
              const nodeSize = getNodeSize(node);
              const nodeColor = getNodeColor(node);
              
              return (
                <G key={`node-${node.id}`}>
                  <Circle
                    cx={(node.x || 0) * zoom}
                    cy={(node.y || 0) * zoom}
                    r={nodeSize * zoom}
                    fill={nodeColor}
                    stroke={colors.backgroundPrimary}
                    strokeWidth={2}
                    onPress={() => handleNodePress(node)}
                  />
                  
                  {showLabels && zoom > 0.8 && (
                    <SvgText
                      x={(node.x || 0) * zoom}
                      y={(node.y || 0) * zoom + nodeSize * zoom + 12}
                      fontSize={10 * zoom}
                      fill={colors.textPrimary}
                      textAnchor="middle"
                      onPress={() => handleNodePress(node)}
                    >
                      {node.name.length > 8 ? node.name.substring(0, 8) + '...' : node.name}
                    </SvgText>
                  )}
                </G>
              );
            })}
          </Svg>
        </ScrollView>
      </View>

      {/* 选中节点信息 */}
      {selectedNode && (
        <View style={{
          marginTop: designTokens.spacing.md,
          padding: designTokens.spacing.md,
          backgroundColor: colors.backgroundSecondary,
          borderRadius: designTokens.borderRadius.md
        }}>
          {(() => {
            const node = data.nodes.find(n => n.id === selectedNode);
            if (!node) return null;
            
            return (
              <View>
                <Text style={{
                  fontSize: designTokens.fontSize.md,
                  fontWeight: designTokens.fontWeight.semiBold,
                  color: colors.textPrimary,
                  marginBottom: 4
                }}>
                  {node.name}
                </Text>
                <Text style={{
                  fontSize: designTokens.fontSize.sm,
                  color: colors.textSecondary
                }}>
                  类别: {node.category} · 难度: {node.difficultyLevel}
                </Text>
              </View>
            );
          })()}
        </View>
      )}
    </Card>
  );
};
